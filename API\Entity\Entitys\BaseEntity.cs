using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace Entity.Entitys;



public class BaseEntity
{
    /// <summary>
    /// 备注
    /// </summary>
    [Comment("备注")]
    public string? Remark { get; set; }

    /// <summary>
    /// 创建人ID
    /// </summary>
    [MaxLength(32)]
    [Comment("创建人ID")]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 创建人
    /// </summary>
    [MaxLength(50)]
    [Comment("创建人")]
    public string? CreatorName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Comment("创建时间")]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新人ID
    /// </summary>
    [MaxLength(32)]
    [Comment("更新人ID")]
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    [MaxLength(50)]
    [Comment("更新人")]
    public string? UpdaterName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Comment("更新时间")]
    public DateTime? UpdateTime { get; set; }

}


public class BaseEntity_GUID : BaseEntity
{
    /// <summary>
    /// 主键ID (32位GUID字符串)
    /// </summary>
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    [MaxLength(32)]
    [Comment("主键ID (32位GUID字符串)")]
    public string Id { get; set; }

    public BaseEntity_GUID()
    {
        // 生成32位的GUID（去除连字符'-'）
        Id = Guid.NewGuid().ToString("N");
    }
}



public class BaseEntity_ID : BaseEntity
{
    /// ID
    /// </summary>
    [Key]
    [Column("id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Comment("ID")]
    public int Id { get; set; }
}
