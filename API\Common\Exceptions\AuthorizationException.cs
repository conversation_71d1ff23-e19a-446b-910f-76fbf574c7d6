namespace Common.Exceptions
{
    /// <summary>
    /// 权限验证异常
    /// 用于表示用户权限验证失败的场景
    /// </summary>
    public class AuthorizationException : Exception
    {
        /// <summary>
        /// 初始化权限验证异常
        /// </summary>
        public AuthorizationException() : base("权限验证失败") { }

        /// <summary>
        /// 使用指定的错误消息初始化权限验证异常
        /// </summary>
        /// <param name="message">错误消息</param>
        public AuthorizationException(string message) : base(message) { }

        /// <summary>
        /// 使用指定的错误消息和内部异常初始化权限验证异常
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public AuthorizationException(string message, Exception innerException)
            : base(message, innerException) { }
    }
}