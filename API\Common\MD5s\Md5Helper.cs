using System.Security.Cryptography;
using System.Text;

namespace Common.MD5s
{
    /// <summary>
    /// MD5哈希计算帮助类
    /// </summary>
    public class MD5Helper
    {
        /// <summary>
        /// 计算字符串的MD5哈希值（小写）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>MD5哈希值（32位小写）</returns>
        public static string Md5(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return string.Empty;
            }
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] hashBytes = MD5.HashData(inputBytes);

            StringBuilder sb = new();
            for (int i = 0; i < hashBytes.Length; i++)
            {
                sb.Append(hashBytes[i].ToString("x2"));
            }
            return sb.ToString();
        }

        /// <summary>
        /// 计算字符串的MD5哈希值（大写）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>MD5哈希值（32位大写）</returns>
        public static string Md5Upper(string input)
        {
            return Md5(input).ToUpper();
        }

        /// <summary>
        /// 计算文件的MD5哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MD5哈希值（小写）</returns>
        /// <exception cref="Exception">当文件操作失败时抛出</exception>
        public static string Md5File(string filePath)
        {
            try
            {
                using FileStream fs = new(filePath, FileMode.Open, FileAccess.Read);
                using MD5 md5 = MD5.Create();
                byte[] hashBytes = md5.ComputeHash(fs);
                StringBuilder sb = new();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("x2"));
                }
                return sb.ToString();
            }
            catch (Exception ex)
            {
                throw new Exceptions.BusinessException($"计算文件MD5时发生错误: {ex.Message}", ex);
            }
        }
    }
}