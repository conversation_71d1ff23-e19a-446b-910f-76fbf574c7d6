using DAL.Databases;
using System.Linq.Expressions;
using System.Reflection;

namespace DAL.Databases
{

    /// <summary>
    /// 查询帮助类
    /// 用于构建动态查询表达式,支持多种查询操作符和条件组合
    /// 主要用于将查询模型转换为Lambda表达式,实现动态查询功能
    /// </summary>
    public static class QueryHelper
    {
        /// <summary>
        /// 构建查询条件
        /// 扩展方法,用于IQueryable<T>,根据查询模型构建查询条件
        /// </summary>
        /// <typeparam name="T">实体类型,如User、Order等</typeparam>
        /// <param name="query">原始查询对象,如db.Users.AsQueryable()</param>
        /// <param name="queryable">查询条件模型,包含查询参数</param>
        /// <returns>添加查询条件后的IQueryable<T>,可继续链式调用</returns>
        public static IQueryable<T> BuildQuery<T>(this IQueryable<T> query, object queryable)
        {
            PropertyInfo? currentProperty = null;

            try
            {
                // 获取所有查询属性
                var properties = queryable.GetType().GetProperties();

                // 先处理查询条件
                foreach (var prop in properties)
                {
                    currentProperty = prop;

                    // 过滤 PageIndex 和 PageSize
                    if (prop.Name == "PageIndex" || prop.Name == "PageSize") continue;

                    var attr = prop.GetCustomAttribute<QueryAttribute>();
                    if (attr == null || attr.Operator == QueryOperator.排序) continue;

                    var value = prop.GetValue(queryable);

                    // 检查是否为空字符串
                    if (typeof(string).IsAssignableFrom(prop.PropertyType) && string.IsNullOrEmpty(value?.ToString())) continue;

                    // 检查是否为null
                    if (attr.IgnoreNull && value == null) continue;

                    // 检查int类型是否为-999(表示不查询)
                    if (typeof(int).IsAssignableFrom(prop.PropertyType) && value != null && (int)value == -999) continue;

                    // 检查int类型是否为-1(表示不查询)
                    if (typeof(int).IsAssignableFrom(prop.PropertyType) && value != null && (int)value == -1) continue;

                    // 检查可空int类型是否为-1(表示不查询)
                    if (Nullable.GetUnderlyingType(prop.PropertyType) == typeof(int) && value != null && (int)value == -1) continue;

                    query = BuildExpression(query, prop, value, attr, queryable);
                }


                return query;
            }
            catch (Exception ex)
            {
                string propertyInfo = currentProperty != null
                    ? $"属性名: {currentProperty.Name}, 属性类型: {currentProperty.PropertyType.Name}"
                    : "未知属性";

                throw new Exception($"构建查询条件失败 - {propertyInfo} - {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 构建查询表达式
        /// 根据属性信息和查询特性构建Lambda表达式
        /// 支持Equal、Contains、DateRange等多种查询方式
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="query">原始查询对象</param>
        /// <param name="prop">属性信息,包含属性名、类型等</param>
        /// <param name="value">属性值,用于构建查询条件</param>
        /// <param name="attr">查询特性,定义查询方式</param>
        /// <param name="queryable">查询对象</param>
        /// <returns>添加查询条件后的IQueryable<T></returns>
        private static IQueryable<T> BuildExpression<T>(
            IQueryable<T> query,
            PropertyInfo prop,
            object? value,
            QueryAttribute attr,
            object queryable)
        {
            if (value == null && !attr.IgnoreNull)
            {
                // 如果值为 null 且不忽略 null，返回原始查询
                return query;
            }

            var parameter = Expression.Parameter(typeof(T), "x");
            var memberName = string.IsNullOrEmpty(attr.ColumnName) ? prop.Name : attr.ColumnName;
            var member = Expression.Property(parameter, memberName);

            Expression predicate = attr.Operator switch
            {
                QueryOperator.等于 => BuildEqualExpression(member, value),
                QueryOperator.包含 => BuildContainsExpression(member, value),
                QueryOperator.日期范围 => BuildDateRangeExpression(member, value, attr.RelatedProperty, queryable),
                QueryOperator.包含于 => BuildInExpression(member, value),
                QueryOperator.不包含于 => BuildNotInExpression(member, value),
                QueryOperator.大于 => BuildComparisonExpression(member, value, Expression.GreaterThan),
                QueryOperator.小于 => BuildComparisonExpression(member, value, Expression.LessThan),
                QueryOperator.大于等于 => BuildComparisonExpression(member, value, Expression.GreaterThanOrEqual),
                QueryOperator.小于等于 => BuildComparisonExpression(member, value, Expression.LessThanOrEqual),
                QueryOperator.不等于 => BuildComparisonExpression(member, value, Expression.NotEqual),
                QueryOperator.开头是 => BuildStartsWithExpression(member, value),
                QueryOperator.结尾是 => BuildEndsWithExpression(member, value),
                _ => throw new NotSupportedException($"不支持的查询操作符: {attr.Operator}")
            };

            var lambda = Expression.Lambda<Func<T, bool>>(predicate, parameter);
            return query.Where(lambda);
        }

        /// <summary>
        /// 构建等于表达式
        /// 如: x.PropertyName == value
        /// 用于精确匹配查询
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">比较值</param>
        /// <returns>等于比较表达式</returns>
        private static BinaryExpression BuildEqualExpression(Expression member, object? value)
        {
            if (value == null)
            {
                return Expression.Equal(member, Expression.Constant(null, member.Type));
            }
            var constant = Expression.Constant(value, member.Type);
            return Expression.Equal(member, constant);
        }

        /// <summary>
        /// 构建包含表达式
        /// 如: x.PropertyName.Contains(value)
        /// 用于字符串模糊查询
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">要包含的字符串</param>
        /// <returns>字符串包含表达式</returns>
        private static MethodCallExpression BuildContainsExpression(Expression member, object? value)
        {
            var method = typeof(string).GetMethod("Contains", [typeof(string)])
                ?? throw new InvalidOperationException("未找到 String.Contains 方法");
            var constant = Expression.Constant(value?.ToString(), typeof(string));
            return Expression.Call(member, method, constant);
        }

        /// <summary>
        /// 构建日期范围表达式
        /// 如: x.PropertyName >= startDate && x.PropertyName <= endDate
        /// 用于日期区间查询
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">开始日期</param>
        /// <param name="relatedProperty">结束日期属性名</param>
        /// <param name="queryable">查询对象</param>
        /// <returns>日期范围表达式</returns>
        private static Expression BuildDateRangeExpression(Expression member, object? value, string relatedProperty, object queryable)
        {
            if (value == null) return Expression.Constant(true);

            if (value is not DateTime startDate)
            {
                throw new ArgumentException("日期范围查询的起始值必须是 DateTime 类型");
            }

            var endDate = DateTime.MaxValue;

            if (!string.IsNullOrEmpty(relatedProperty))
            {
                var endDateValue = queryable.GetType().GetProperty(relatedProperty)?.GetValue(queryable);
                if (endDateValue != null && endDateValue is DateTime dateValue)
                {
                    endDate = dateValue;
                }
            }

            var greaterThanOrEqual = Expression.GreaterThanOrEqual(member, Expression.Constant(startDate));
            var lessThanOrEqual = Expression.LessThanOrEqual(member, Expression.Constant(endDate));

            return Expression.AndAlso(greaterThanOrEqual, lessThanOrEqual);
        }

        /// <summary>
        /// 构建In表达式
        /// 使用子查询实现IN语句
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">值集合</param>
        /// <returns>IN表达式</returns>
        private static Expression BuildInExpression(Expression member, object? value)
        {
            if (value == null) return Expression.Constant(false);

            if (value is not System.Collections.IEnumerable enumerable)
            {
                throw new ArgumentException("IN 查询的值必须是集合类型");
            }

            var list = enumerable.Cast<object>().ToList();
            if (list.Count == 0) return Expression.Constant(false);

            // 特殊处理 string[] 类型
            if (value is string[] stringArray && member.Type == typeof(string))
            {
                var valueQuery = Expression.Constant(stringArray);
                var containsMethod = typeof(Enumerable).GetMethods()
                    .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                    .MakeGenericMethod(typeof(string));

                return Expression.Call(null, containsMethod, valueQuery, member);
            }

            try
            {
                // 创建一个包含值列表的查询
                var convertedList = list.Select(x => Convert.ChangeType(x, member.Type));
                var valueQuery = Expression.Constant(convertedList);

                // 创建一个调用Contains()方法的表达式
                var containsMethod = typeof(Enumerable).GetMethods()
                    .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                    .MakeGenericMethod(member.Type);

                return Expression.Call(null, containsMethod, valueQuery, member);
            }
            catch (InvalidCastException)
            {
                // 处理类型转换异常情况
                throw new ArgumentException($"无法将集合元素转换为目标属性类型 {member.Type.Name}");
            }
        }

        /// <summary>
        /// 构建比较表达式
        /// 用于大于、小于、大于等于、小于等于、不等于等比较操作
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">比较值</param>
        /// <param name="comparisonFactory">比较表达式工厂方法</param>
        /// <returns>比较表达式</returns>
        private static Expression BuildComparisonExpression(Expression member, object? value,
            Func<Expression, Expression, BinaryExpression> comparisonFactory)
        {
            if (value == null)
            {
                return Expression.Constant(false);
            }

            try
            {
                var convertedValue = Convert.ChangeType(value, member.Type);
                var constant = Expression.Constant(convertedValue, member.Type);
                return comparisonFactory(member, constant);
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"无法将值 '{value}' 转换为类型 '{member.Type.Name}'", ex);
            }
        }

        /// <summary>
        /// 构建StartsWith表达式
        /// 如: x.PropertyName.StartsWith(value)
        /// 用于字符串前缀匹配
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">前缀字符串</param>
        /// <returns>字符串前缀匹配表达式</returns>
        private static MethodCallExpression BuildStartsWithExpression(Expression member, object? value)
        {
            var method = typeof(string).GetMethod("StartsWith", [typeof(string)])
                ?? throw new InvalidOperationException("未找到 String.StartsWith 方法");
            var constant = Expression.Constant(value?.ToString(), typeof(string));
            return Expression.Call(member, method, constant);
        }

        /// <summary>
        /// 构建EndsWith表达式
        /// 如: x.PropertyName.EndsWith(value)
        /// 用于字符串后缀匹配
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">后缀字符串</param>
        /// <returns>字符串后缀匹配表达式</returns>
        private static MethodCallExpression BuildEndsWithExpression(Expression member, object? value)
        {
            var method = typeof(string).GetMethod("EndsWith", [typeof(string)])
                ?? throw new InvalidOperationException("未找到 String.EndsWith 方法");
            var constant = Expression.Constant(value?.ToString(), typeof(string));
            return Expression.Call(member, method, constant);
        }

        /// <summary>
        /// 构建排序表达式
        /// </summary>
        /// <param name="query">原始查询对象</param>
        /// <param name="prop">属性信息</param>
        /// <param name="attr">查询特性</param>
        /// <returns>添加排序后的查询对象</returns>
        /// <exception cref="InvalidOperationException">当找不到排序方法时抛出</exception>
        private static IQueryable<T> BuildOrderExpression<T>(
            IQueryable<T> query,
            PropertyInfo prop,
            QueryAttribute attr)
        {
            // 创建参数表达式：x => x.PropertyName
            var parameter = Expression.Parameter(typeof(T), "x");
            var memberName = string.IsNullOrEmpty(attr.ColumnName) ? prop.Name : attr.ColumnName;
            var member = Expression.Property(parameter, memberName);

            // 创建排序Lambda表达式
            var lambda = Expression.Lambda(member, parameter);

            // 根据排序方向选择排序方法
            string methodName = attr.OrderDirection == OrderDirection.升序 ?
                query.Expression.Type == typeof(IOrderedQueryable<T>) ? "ThenBy" : "OrderBy" :
                query.Expression.Type == typeof(IOrderedQueryable<T>) ? "ThenByDescending" : "OrderByDescending";

            // 查找并验证排序方法
            var method = typeof(Queryable).GetMethods()
                .FirstOrDefault(m => m.Name == methodName && m.GetParameters().Length == 2)
                ?? throw new InvalidOperationException($"未找到排序方法: {methodName}");

            // 创建泛型方法
            var genericMethod = method.MakeGenericMethod(typeof(T), prop.PropertyType);

            // 应用排序并验证结果
            var result = genericMethod.Invoke(null, [query, lambda]);

            if (result is not IQueryable<T> queryResult)
            {
                throw new InvalidOperationException($"排序方法返回了意外的类型: {result?.GetType().Name ?? "null"}");
            }

            return queryResult;
        }

        /// <summary>
        /// 构建NotIn表达式
        /// 使用子查询实现NOT IN语句
        /// </summary>
        /// <param name="member">属性表达式</param>
        /// <param name="value">值集合</param>
        /// <returns>NOT IN表达式</returns>
        private static Expression BuildNotInExpression(Expression member, object? value)
        {
            if (value == null) return Expression.Constant(true);

            if (value is not System.Collections.IEnumerable enumerable)
            {
                throw new ArgumentException("NOT IN 查询的值必须是集合类型");
            }

            var list = enumerable.Cast<object>().ToList();
            if (list.Count == 0) return Expression.Constant(true);

            // 特殊处理 string[] 类型
            if (value is string[] stringArray && member.Type == typeof(string))
            {
                var valueQuery = Expression.Constant(stringArray);
                var containsMethod = typeof(Enumerable).GetMethods()
                    .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                    .MakeGenericMethod(typeof(string));

                return Expression.Not(Expression.Call(null, containsMethod, valueQuery, member));
            }

            try
            {
                // 创建一个包含值列表的查询
                var convertedList = list.Select(x => Convert.ChangeType(x, member.Type));
                var valueQuery = Expression.Constant(convertedList);

                // 创建一个调用Contains()方法的表达式
                var containsMethod = typeof(Enumerable).GetMethods()
                    .First(m => m.Name == "Contains" && m.GetParameters().Length == 2)
                    .MakeGenericMethod(member.Type);

                var notContainsExpression = Expression.Not(Expression.Call(null, containsMethod, valueQuery, member));
                return notContainsExpression;
            }
            catch (InvalidCastException)
            {
                // 处理类型转换异常情况
                throw new ArgumentException($"无法将集合元素转换为目标属性类型 {member.Type.Name}");
            }
        }
    }
}