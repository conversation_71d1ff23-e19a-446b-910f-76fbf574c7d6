using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 抽奖记录数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class DrawRecordDAL(MyContext context) : BaseQueryDLL<ActivityDrawRecord, DrawRecordDAL.DrawRecordQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 抽奖记录查询条件模型类
        /// </summary>
        public class DrawRecordQuery : PageQueryEntity
        {
            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ActivityId { get; set; }

            /// <summary>
            /// 玩家游戏用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? PlayerGameUserId { get; set; }

            /// <summary>
            /// 奖励ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? RewardId { get; set; }

            /// <summary>
            /// 抽奖时间开始
            /// </summary>
            [Query(QueryOperator.大于等于, "DrawTime")]
            public DateTime? DrawTimeStart { get; set; }

            /// <summary>
            /// 抽奖时间结束
            /// </summary>
            [Query(QueryOperator.小于等于, "DrawTime")]
            public DateTime? DrawTimeEnd { get; set; }
        }

        /// <summary>
        /// 获取活动抽奖记录
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID（可选）</param>
        /// <param name="limit">记录数限制</param>
        /// <returns>抽奖记录列表</returns>
        public async Task<List<ActivityDrawRecord>> GetActivityDrawRecordsAsync(int activityId, string? playerGameUserId = null, int limit = 50)
        {
            var query = _context.DrawRecords
                .Where(d => d.ActivityId == activityId);

            if (!string.IsNullOrEmpty(playerGameUserId))
            {
                query = query.Where(d => d.PlayerGameUserId == playerGameUserId);
            }

            return await query
                .OrderByDescending(d => d.DrawTime)
                .Take(limit)
                .ToListAsync();
        }

        /// <summary>
        /// 获取玩家抽奖统计
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>抽奖统计信息</returns>
        public async Task<PlayerDrawStats> GetPlayerDrawStatsAsync(int activityId, string playerGameUserId)
        {
            var records = await _context.DrawRecords
                .Where(d => d.ActivityId == activityId && d.PlayerGameUserId == playerGameUserId)
                .ToListAsync();

            return new PlayerDrawStats
            {
                TotalDraws = records.Count,
                LastDrawTime = records.Count != 0 ? records.Max(d => d.DrawTime) : null
            };
        }

        /// <summary>
        /// 获取活动抽奖统计
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>活动抽奖统计信息</returns>
        public async Task<ActivityDrawStats> GetActivityDrawStatsAsync(int activityId)
        {
            var records = await _context.DrawRecords
                .Where(d => d.ActivityId == activityId)
                .ToListAsync();

            var uniquePlayers = records.Select(d => d.PlayerGameUserId).Distinct().Count();

            return new ActivityDrawStats
            {
                TotalDraws = records.Count,
                UniquePlayers = uniquePlayers,
                LastDrawTime = records.Count != 0 ? records.Max(d => d.DrawTime) : null
            };
        }

        /// <summary>
        /// 批量添加抽奖记录
        /// </summary>
        /// <param name="records">抽奖记录列表</param>
        /// <returns>添加的记录数</returns>
        public async Task<int> BatchAddAsync(List<ActivityDrawRecord> records)
        {
            if (records.Count == 0)
                return 0;

            _context.DrawRecords.AddRange(records);
            return await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// 玩家抽奖统计信息
    /// </summary>
    public class PlayerDrawStats
    {
        /// <summary>
        /// 总抽奖次数
        /// </summary>
        public int TotalDraws { get; set; }

        /// <summary>
        /// 最后抽奖时间
        /// </summary>
        public DateTime? LastDrawTime { get; set; }
    }

    /// <summary>
    /// 活动抽奖统计信息
    /// </summary>
    public class ActivityDrawStats
    {
        /// <summary>
        /// 总抽奖次数
        /// </summary>
        public int TotalDraws { get; set; }

        /// <summary>
        /// 参与抽奖的唯一玩家数
        /// </summary>
        public int UniquePlayers { get; set; }

        /// <summary>
        /// 最后抽奖时间
        /// </summary>
        public DateTime? LastDrawTime { get; set; }
    }
}
