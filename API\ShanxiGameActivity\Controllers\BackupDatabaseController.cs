using BLL.SysService;
using Microsoft.AspNetCore.Mvc;
using System.Data;

namespace ShanxiGameActivity.Controllers
{
    /// <summary>
    /// 备用数据库控制器
    /// 演示如何调用备用数据库的存储过程
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="backupDatabaseService">备用数据库服务</param>
    [ApiController]
    [Route("api/[controller]")]
    public class BackupDatabaseController(BackupDatabaseService backupDatabaseService) : ControllerBase
    {
        private readonly BackupDatabaseService _backupDatabaseService = backupDatabaseService ?? throw new ArgumentNullException(nameof(backupDatabaseService));

        /// <summary>
        /// 调用存储过程1示例
        /// </summary>
        /// <param name="param1">参数1</param>
        /// <param name="param2">参数2</param>
        /// <returns>执行结果</returns>
        [HttpPost("call-procedure-1")]
        public async Task<IActionResult> CallStoredProcedure1([FromQuery] string param1, [FromQuery] int param2)
        {
            try
            {
                var result = await _backupDatabaseService.CallStoredProcedure1Async(param1, param2);
                return Ok(new
                {
                    success = true,
                    message = "存储过程1调用成功",
                    result = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    message = $"存储过程1调用失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 调用存储过程2示例
        /// </summary>
        /// <param name="param1">参数1</param>
        /// <returns>执行结果</returns>
        [HttpPost("call-procedure-2")]
        public async Task<IActionResult> CallStoredProcedure2([FromQuery] string param1)
        {
            try
            {
                var result = await _backupDatabaseService.CallStoredProcedure2Async(param1);
                return Ok(new
                {
                    success = true,
                    message = "存储过程2调用成功",
                    result = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    message = $"存储过程2调用失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 调用存储过程3示例
        /// </summary>
        /// <param name="param1">参数1</param>
        /// <returns>执行结果</returns>
        [HttpPost("call-procedure-3")]
        public async Task<IActionResult> CallStoredProcedure3([FromQuery] string param1)
        {
            try
            {
                var dataTable = await _backupDatabaseService.CallStoredProcedure3Async(param1);

                // 将DataTable转换为更友好的格式
                var rows = new List<Dictionary<string, object>>();
                foreach (DataRow row in dataTable.Rows)
                {
                    var dict = new Dictionary<string, object>();
                    foreach (DataColumn column in dataTable.Columns)
                    {
                        dict[column.ColumnName] = row[column] ?? DBNull.Value;
                    }
                    rows.Add(dict);
                }

                return Ok(new
                {
                    success = true,
                    message = "存储过程3调用成功",
                    data = rows,
                    count = rows.Count
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    message = $"存储过程3调用失败: {ex.Message}"
                });
            }
        }
    }
}
