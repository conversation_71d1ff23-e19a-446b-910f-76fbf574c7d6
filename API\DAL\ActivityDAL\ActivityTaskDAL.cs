using Common.Autofac;
using DAL.Databases;
using Entity;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 活动任务数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class ActivityTaskDAL(MyContext context) : BaseQueryDLL<ActivityTask, ActivityTaskDAL.ActivityTaskQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 活动任务查询条件模型类
        /// </summary>
        public class ActivityTaskQuery : PageQueryEntity
        {
            /// <summary>
            /// 任务ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? Id { get; set; }

            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ActivityId { get; set; }

            /// <summary>
            /// 任务类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public TaskType? TaskType { get; set; }
        }

        /// <summary>
        /// 获取活动的所有任务配置
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>任务配置列表</returns>
        public async Task<List<ActivityTask>> GetActivityTasksAsync(int activityId)
        {
            return await _context.ActivityTasks
                .Where(x => x.ActivityId == activityId)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 根据任务类型获取任务
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="taskType">任务类型</param>
        /// <returns>任务配置</returns>
        public async Task<ActivityTask?> GetTaskByTypeAsync(int activityId, TaskType taskType)
        {
            return await _context.ActivityTasks
                .FirstOrDefaultAsync(x => x.ActivityId == activityId && x.TaskType == taskType);
        }

        /// <summary>
        /// 批量创建活动任务
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>创建的任务列表</returns>
        public async Task<List<ActivityTask>> CreateBatchAsync(List<ActivityTask> tasks)
        {
            _context.ActivityTasks.AddRange(tasks);
            await _context.SaveChangesAsync();
            return tasks;
        }

        /// <summary>
        /// 获取需要刷新的任务列表
        /// </summary>
        /// <param name="refreshType">刷新类型</param>
        /// <returns>需要刷新的任务列表</returns>
        public async Task<List<ActivityTask>> GetTasksNeedRefreshAsync(RefreshType refreshType)
        {
            return await (from task in _context.ActivityTasks
                          join activity in _context.Activities on task.ActivityId equals activity.Id
                          where task.RefreshType == refreshType && activity.Status == ActivityStatus.running
                          select task)
                .ToListAsync();
        }
    }
}
