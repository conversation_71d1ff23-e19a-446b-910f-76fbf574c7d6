using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 抽奖记录表
/// </summary>
[Table("activity_draw_records")]
public class ActivityDrawRecord : BaseEntity_ID
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [Required]
    [Column("activity_id")]
    [Comment("活动ID")]
    public int ActivityId { get; set; }

    /// <summary>
    /// 玩家游戏用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("player_game_user_id")]
    [Comment("玩家游戏用户ID")]
    public string PlayerGameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 玩家昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("player_nickname")]
    [Comment("玩家昵称")]
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 中奖奖励ID
    /// </summary>
    [Required]
    [Column("reward_id")]
    [Comment("中奖奖励ID")]
    public int RewardId { get; set; }

    /// <summary>
    /// 抽奖时间
    /// </summary>
    [Column("draw_time")]
    [Comment("抽奖时间")]
    public DateTime DrawTime { get; set; } = DateTime.Now;

}
