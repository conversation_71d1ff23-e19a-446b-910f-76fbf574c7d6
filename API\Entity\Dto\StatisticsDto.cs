namespace Entity.Dto;

/// <summary>
/// 活动统计数据响应DTO
/// </summary>
public class ActivityStatisticsResponseDto
{
    /// <summary>
    /// 参与人数
    /// </summary>
    public int ParticipantsCount { get; set; }

    /// <summary>
    /// 总抽奖次数
    /// </summary>
    public int TotalDraws { get; set; }

    /// <summary>
    /// 总奖励发放金额
    /// </summary>
    public decimal TotalRewardsDistributed { get; set; }

    /// <summary>
    /// 玩家统计数据
    /// </summary>
    public List<PlayerStatisticsDto> PlayerStatistics { get; set; } = [];
}

/// <summary>
/// 玩家统计数据DTO
/// </summary>
public class PlayerStatisticsDto
{
    /// <summary>
    /// 玩家ID
    /// </summary>
    public int PlayerId { get; set; }

    /// <summary>
    /// 玩家昵称
    /// </summary>
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 总抽奖次数
    /// </summary>
    public int TotalDraws { get; set; }

    /// <summary>
    /// 总获得通宝
    /// </summary>
    public decimal TotalTongbaoWon { get; set; }

    /// <summary>
    /// 实物奖励
    /// </summary>
    public List<PhysicalRewardDto> PhysicalRewards { get; set; } = [];
}

/// <summary>
/// 实物奖励DTO
/// </summary>
public class PhysicalRewardDto
{
    /// <summary>
    /// 奖励名称
    /// </summary>
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }
}

/// <summary>
/// 跑马灯消息响应DTO
/// </summary>
public class MarqueeMessageResponseDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 玩家昵称
    /// </summary>
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 奖励名称
    /// </summary>
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    public string MessageContent { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
