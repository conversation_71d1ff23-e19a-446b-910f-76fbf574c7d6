---
description: project-structure
globs: 
alwaysApply: false
---
# 项目结构指南

## 项目层次结构
本项目遵循标准的N层架构：
- **DataMgrSystem**: Web API项目，包含控制器和程序入口
- **BLL**: 业务逻辑层，包含各种服务实现
- **DAL**: 数据访问层，负责数据库交互
- **Entity**: 实体层，定义数据模型和DTO
- **Common**: 通用工具和辅助类

## 关键组件

### 控制器
控制器位于 [DataMgrSystem/Controllers/](mdc:DataMgrSystem/Controllers) 目录下，分为：
- 系统相关控制器: [SysControllers/](mdc:DataMgrSystem/Controllers/SysControllers)
- 卡片相关控制器: [CardControllers/](mdc:DataMgrSystem/Controllers/CardControllers)
- 基础控制器: [BasisController/](mdc:DataMgrSystem/Controllers/BasisController)

### 业务逻辑层
业务逻辑实现在 [BLL/](mdc:BLL) 项目中：
- 系统服务: [SysService/](mdc:BLL/SysService)
- 卡片服务: [CardService/](mdc:BLL/CardService)
- 基础服务: [BaseService/](mdc:BLL/BaseService)

### 数据访问层
数据访问实现在 [DAL/](mdc:DAL) 项目中：
- 系统数据访问: [SysDAL/](mdc:DAL/SysDAL)
- 卡片数据访问: [CardDAL/](mdc:DAL/CardDAL)
- 数据库相关: [Databases/](mdc:DAL/Databases)

### 实体和DTO
- 实体定义: [Entity/Entitys/](mdc:Entity/Entitys)
- DTO定义: [Entity/Dto/](mdc:Entity/Dto)

### 通用工具
通用功能实现在 [Common/](mdc:Common) 项目中：
- 缓存: [Caches/](mdc:Common/Caches)
- JWT认证: [JWT/](mdc:Common/JWT)
- 辅助工具: [Helper/](mdc:Common/Helper)
- 日志: [Log4Net/](mdc:Common/Log4Net)
- Redis工具: [redis/](mdc:Common/redis)

