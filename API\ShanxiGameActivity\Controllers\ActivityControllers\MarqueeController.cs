using DAL.ActivityDAL;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.ActivityControllers
{
    /// <summary>
    /// 跑马灯控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class MarqueeController(MarqueeMessageDAL marqueeMessageDAL) : BaseController
    {
        private readonly MarqueeMessageDAL _marqueeMessageDAL = marqueeMessageDAL;

        /// <summary>
        /// 获取活动跑马灯消息
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="limit">消息数量限制</param>
        /// <param name="onlyUndisplayed">是否只获取未显示的消息</param>
        /// <returns>跑马灯消息列表</returns>
        [HttpGet("messages/{activityId}")]
        public async Task<Result<List<object>>> GetMarqueeMessagesAsync(int activityId, int limit = 20, bool onlyUndisplayed = false)
        {
            try
            {
                var messages = await _marqueeMessageDAL.GetActivityMarqueeMessagesAsync(activityId, limit, onlyUndisplayed);

                var messageDtos = messages.Select(m => new
                {
                    m.Id,
                    m.ActivityId,
                    m.PlayerGameUserId,
                    m.PlayerNickname,
                    m.RewardName,
                    m.IsDisplayed,
                    m.DisplayTime,
                    m.CreateTime,
                    FormattedText = MarqueeMessageDAL.FormatMarqueeText(m)
                }).ToList();

                return Success(messageDtos.Cast<object>().ToList(), "获取跑马灯消息成功");
            }
            catch (Exception ex)
            {
                return Fail<List<object>>($"获取跑马灯消息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取最新的跑马灯消息
        /// </summary>
        /// <param name="activityId">活动ID（可选）</param>
        /// <param name="limit">消息数量限制</param>
        /// <returns>最新跑马灯消息列表</returns>
        [HttpGet("latest")]
        public async Task<Result<List<object>>> GetLatestMarqueeMessagesAsync(int? activityId = null, int limit = 10)
        {
            try
            {
                var messages = await _marqueeMessageDAL.GetLatestMarqueeMessagesAsync(activityId, limit);

                var messageDtos = messages.Select(m => new
                {
                    m.Id,
                    m.ActivityId,
                    ActivityName = "", // 需要手动查询活动名称
                    m.PlayerGameUserId,
                    m.PlayerNickname,
                    m.RewardName,
                    m.IsDisplayed,
                    m.DisplayTime,
                    m.CreateTime,
                    FormattedText = MarqueeMessageDAL.FormatMarqueeText(m)
                }).ToList();

                return Success(messageDtos.Cast<object>().ToList(), "获取最新跑马灯消息成功");
            }
            catch (Exception ex)
            {
                return Fail<List<object>>($"获取最新跑马灯消息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取格式化的跑马灯消息文本
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="limit">消息数量限制</param>
        /// <returns>格式化的消息文本列表</returns>
        [HttpGet("formatted/{activityId}")]
        public async Task<Result<List<string>>> GetFormattedMarqueeMessagesAsync(int activityId, int limit = 10)
        {
            try
            {
                var messages = await _marqueeMessageDAL.GetFormattedMarqueeMessagesAsync(activityId, limit);
                return Success(messages, "获取格式化跑马灯消息成功");
            }
            catch (Exception ex)
            {
                return Fail<List<string>>($"获取格式化跑马灯消息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 标记消息为已显示
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <returns>更新结果</returns>
        [HttpPost("mark-displayed/{messageId}")]
        public async Task<Result<string>> MarkAsDisplayedAsync(int messageId)
        {
            try
            {
                var success = await _marqueeMessageDAL.MarkAsDisplayedAsync(messageId);

                if (success)
                {
                    return Success("消息已标记为已显示", "标记成功");
                }
                else
                {
                    return Fail<string>("消息不存在或标记失败");
                }
            }
            catch (Exception ex)
            {
                return Fail<string>($"标记消息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量标记消息为已显示
        /// </summary>
        /// <param name="messageIds">消息ID列表</param>
        /// <returns>更新结果</returns>
        [HttpPost("batch-mark-displayed")]
        public async Task<Result<string>> BatchMarkAsDisplayedAsync([FromBody] List<int> messageIds)
        {
            try
            {
                var updatedCount = await _marqueeMessageDAL.BatchMarkAsDisplayedAsync(messageIds);
                return Success($"成功标记 {updatedCount} 条消息为已显示", "批量标记成功");
            }
            catch (Exception ex)
            {
                return Fail<string>($"批量标记消息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取跑马灯统计信息
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>跑马灯统计信息</returns>
        [HttpGet("stats/{activityId}")]
        public async Task<Result<object>> GetMarqueeStatsAsync(int activityId)
        {
            try
            {
                var stats = await _marqueeMessageDAL.GetMarqueeStatsAsync(activityId);

                var statsDto = new
                {
                    stats.TotalMessages,
                    stats.DisplayedMessages,
                    stats.PendingMessages,
                    stats.LastMessageTime
                };

                return Success((object)statsDto, "获取跑马灯统计成功");
            }
            catch (Exception ex)
            {
                return Fail<object>($"获取跑马灯统计失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清理过期的跑马灯消息
        /// </summary>
        /// <param name="expireDays">过期天数</param>
        /// <returns>清理结果</returns>
        [HttpPost("clean-expired")]
        public async Task<Result<string>> CleanExpiredMessagesAsync(int expireDays = 7)
        {
            try
            {
                var cleanedCount = await _marqueeMessageDAL.CleanExpiredMessagesAsync(expireDays);
                return Success($"成功清理 {cleanedCount} 条过期消息", "清理成功");
            }
            catch (Exception ex)
            {
                return Fail<string>($"清理过期消息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 手动创建跑马灯消息（用于测试）
        /// </summary>
        /// <param name="request">创建消息请求</param>
        /// <returns>创建结果</returns>
        [HttpPost("create-message")]
        public async Task<Result<object>> CreateMarqueeMessageAsync([FromBody] CreateMarqueeMessageRequest request)
        {
            try
            {
                var message = await _marqueeMessageDAL.CreateWinMessageAsync(
                    request.ActivityId,
                    request.PlayerGameUserId,
                    request.PlayerNickname,
                    request.RewardName);

                var messageDto = new
                {
                    message.Id,
                    message.ActivityId,
                    message.PlayerGameUserId,
                    message.PlayerNickname,
                    message.RewardName,
                    message.IsDisplayed,
                    message.CreateTime,
                    FormattedText = MarqueeMessageDAL.FormatMarqueeText(message)
                };

                return Success((object)messageDto, "跑马灯消息创建成功");
            }
            catch (Exception ex)
            {
                return Fail<object>($"创建跑马灯消息失败：{ex.Message}");
            }
        }
    }

    /// <summary>
    /// 创建跑马灯消息请求
    /// </summary>
    public class CreateMarqueeMessageRequest
    {
        /// <summary>
        /// 活动ID
        /// </summary>
        public int ActivityId { get; set; }

        /// <summary>
        /// 玩家游戏用户ID
        /// </summary>
        public string PlayerGameUserId { get; set; } = string.Empty;

        /// <summary>
        /// 玩家昵称
        /// </summary>
        public string PlayerNickname { get; set; } = string.Empty;

        /// <summary>
        /// 奖励名称
        /// </summary>
        public string RewardName { get; set; } = string.Empty;
    }
}
