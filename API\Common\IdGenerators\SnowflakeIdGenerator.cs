namespace Common.IdGenerators
{
    /// <summary>
    /// 雪花ID生成器
    /// 一个基于雪花算法的分布式ID生成器
    /// 64位ID结构：42位时间戳 + 5位机器ID + 5位数据中心ID + 12位序列号
    /// 时间戳部分使用从2020-01-01开始的毫秒数
    /// </summary>
    public class SnowflakeIdGenerator
    {
        // 用于线程同步的锁对象
        private static readonly object _lock = new();

        // 时间戳的起始时间点（2020年1月1日 UTC时间）
        private static readonly DateTime _epoch = new(2020, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        // 定义各个部分的位数
        private const long SEQUENCE_BITS = 12L;       // 序列号占12位
        private const long MACHINE_ID_BITS = 5L;      // 机器ID占5位
        private const long DATACENTER_ID_BITS = 5L;   // 数据中心ID占5位

        // 计算各个部分的最大值
        private const long MAX_SEQUENCE = (-1L ^ (-1L << (int)SEQUENCE_BITS));        // 序列号最大值：4095
        private const long MAX_MACHINE_ID = (-1L ^ (-1L << (int)MACHINE_ID_BITS));    // 机器ID最大值：31
        private const long MAX_DATACENTER_ID = (-1L ^ (-1L << (int)DATACENTER_ID_BITS)); // 数据中心ID最大值：31

        // 定义各个部分的位移量
        private const long MACHINE_ID_SHIFT = SEQUENCE_BITS;                          // 机器ID左移12位
        private const long DATACENTER_ID_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS;     // 数据中心ID左移17位
        private const long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS + DATACENTER_ID_BITS; // 时间戳左移22位

        // 当前实例的状态变量
        private static long _machineId = 0;       // 当前机器ID
        private static long _datacenterId = 0;    // 当前数据中心ID
        private static long _sequence = 0L;      // 当前序列号
        private static long _lastTimestamp = -1L; // 上次生成ID的时间戳

        /// <summary>
        /// 初始化雪花ID生成器
        /// </summary>
        /// <param name="machineId">机器ID，范围0-31</param>
        /// <param name="datacenterId">数据中心ID，范围0-31</param>
        /// <exception cref="ArgumentException">当machineId或datacenterId超出范围时抛出</exception>
        public static void Initialize(long machineId = 0, long datacenterId = 0)
        {
            if (machineId > MAX_MACHINE_ID || machineId < 0)
            {
                throw new ArgumentException($"机器ID不能大于{MAX_MACHINE_ID}或小于0");
            }

            if (datacenterId > MAX_DATACENTER_ID || datacenterId < 0)
            {
                throw new ArgumentException($"数据中心ID不能大于{MAX_DATACENTER_ID}或小于0");
            }

            _machineId = machineId;
            _datacenterId = datacenterId;
        }

        /// <summary>
        /// 生成下一个唯一的64位ID
        /// </summary>
        /// <returns>生成的64位ID</returns>
        /// <exception cref="Exception">当检测到时钟回拨时抛出</exception>
        public static long NewId()
        {
            lock (_lock)  // 确保线程安全
            {
                var timestamp = GetTimestamp();

                // 检查时钟回拨
                if (timestamp < _lastTimestamp)
                {
                    throw new Exception("时钟回拨，拒绝生成ID");
                }

                // 处理同一毫秒内的多个ID请求
                if (_lastTimestamp == timestamp)
                {
                    _sequence = (_sequence + 1) & MAX_SEQUENCE;
                    if (_sequence == 0)  // 如果序列号溢出，等待下一毫秒
                    {
                        timestamp = WaitNextMillis(_lastTimestamp);
                    }
                }
                else
                {
                    _sequence = 0;  // 新时间戳，重置序列号
                }

                _lastTimestamp = timestamp;

                // 组合各个部分生成最终ID
                return ((timestamp) << (int)TIMESTAMP_LEFT_SHIFT) |
                       (_datacenterId << (int)DATACENTER_ID_SHIFT) |
                       (_machineId << (int)MACHINE_ID_SHIFT) |
                       _sequence;
            }
        }

        /// <summary>
        /// 获取当前时间戳（从epoch开始的毫秒数）
        /// </summary>
        /// <returns>当前时间戳</returns>
        private static long GetTimestamp()
        {
            return (long)(DateTime.UtcNow - _epoch).TotalMilliseconds;
        }

        /// <summary>
        /// 等待直到下一毫秒
        /// </summary>
        /// <param name="lastTimestamp">上次生成ID的时间戳</param>
        /// <returns>新的时间戳</returns>
        private static long WaitNextMillis(long lastTimestamp)
        {
            var timestamp = GetTimestamp();
            while (timestamp <= lastTimestamp)  // 自旋等待直到时间戳增加
            {
                timestamp = GetTimestamp();
            }
            return timestamp;
        }
    }
}