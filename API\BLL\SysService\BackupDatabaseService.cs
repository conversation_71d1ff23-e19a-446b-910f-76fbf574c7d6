using Common;
using Common.Autofac;
using MySqlConnector;
using System.Data;

namespace BLL.SysService
{
    /// <summary>
    /// 备用数据库服务
    /// 专门用于调用备用数据库的存储过程
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class BackupDatabaseService
    {
        /// <summary>
        /// 获取备用数据库连接字符串
        /// </summary>
        private static string ConnectionString => DataBaseConnectionStrings.BackupDatabaseConnectionString
            ?? throw new InvalidOperationException("备用数据库连接字符串未配置");

        /// <summary>
        /// 调用存储过程（无返回值）
        /// </summary>
        /// <param name="procedureName">存储过程名称</param>
        /// <param name="parameters">参数</param>
        /// <returns>影响的行数</returns>
        public static async Task<int> ExecuteStoredProcedureAsync(string procedureName, params MySqlParameter[] parameters)
        {
            using var connection = new MySqlConnection(ConnectionString);
            using var command = new MySqlCommand(procedureName, connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            if (parameters != null && parameters.Length > 0)
            {
                command.Parameters.AddRange(parameters);
            }

            await connection.OpenAsync();
            return await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// 调用存储过程并返回单个值
        /// </summary>
        /// <param name="procedureName">存储过程名称</param>
        /// <param name="parameters">参数</param>
        /// <returns>返回值</returns>
        public static async Task<object?> ExecuteStoredProcedureScalarAsync(string procedureName, params MySqlParameter[] parameters)
        {
            using var connection = new MySqlConnection(ConnectionString);
            using var command = new MySqlCommand(procedureName, connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            if (parameters != null && parameters.Length > 0)
            {
                command.Parameters.AddRange(parameters);
            }

            await connection.OpenAsync();
            return await command.ExecuteScalarAsync();
        }

        /// <summary>
        /// 调用存储过程并返回数据表
        /// </summary>
        /// <param name="procedureName">存储过程名称</param>
        /// <param name="parameters">参数</param>
        /// <returns>数据表</returns>
        public static async Task<DataTable> ExecuteStoredProcedureDataTableAsync(string procedureName, params MySqlParameter[] parameters)
        {
            using var connection = new MySqlConnection(ConnectionString);
            using var command = new MySqlCommand(procedureName, connection)
            {
                CommandType = CommandType.StoredProcedure
            };

            if (parameters != null && parameters.Length > 0)
            {
                command.Parameters.AddRange(parameters);
            }

            await connection.OpenAsync();
            using var adapter = new MySqlDataAdapter(command);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);
            return dataTable;
        }

        /// <summary>
        /// 示例：调用存储过程1
        /// </summary>
        /// <param name="param1">参数1</param>
        /// <param name="param2">参数2</param>
        /// <returns>执行结果</returns>
        public async Task<int> CallStoredProcedure1Async(string param1, int param2)
        {
            var parameters = new[]
            {
                new MySqlParameter("@param1", param1),
                new MySqlParameter("@param2", param2)
            };

            return await ExecuteStoredProcedureAsync("your_stored_procedure_1", parameters);
        }

        /// <summary>
        /// 示例：调用存储过程2
        /// </summary>
        /// <param name="param1">参数1</param>
        /// <returns>返回值</returns>
        public async Task<object?> CallStoredProcedure2Async(string param1)
        {
            var parameters = new[]
            {
                new MySqlParameter("@param1", param1)
            };

            return await ExecuteStoredProcedureScalarAsync("your_stored_procedure_2", parameters);
        }

        /// <summary>
        /// 示例：调用存储过程3
        /// </summary>
        /// <param name="param1">参数1</param>
        /// <returns>数据表</returns>
        public async Task<DataTable> CallStoredProcedure3Async(string param1)
        {
            var parameters = new[]
            {
                new MySqlParameter("@param1", param1)
            };

            return await ExecuteStoredProcedureDataTableAsync("your_stored_procedure_3", parameters);
        }
    }
}
