using BLL.ActivityService;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.ActivityControllers
{
    /// <summary>
    /// 用户信息缓存控制器
    /// </summary>
    [ApiController]
    [Route("api/users")]
    public class UserCacheController(UserCacheService userCacheService) : BaseController
    {
        private readonly UserCacheService _userCacheService = userCacheService;

        /// <summary>
        /// 缓存用户昵称
        /// </summary>
        /// <param name="request">缓存请求</param>
        /// <returns>缓存结果</returns>
        [HttpPost("cache-nickname")]
        public async Task<Result<UserCacheResponseDto>> CacheNicknameAsync([FromBody] CacheUserNicknameDto request)
        {
            var result = await _userCacheService.CacheUserNicknameAsync(request);
            return Success(result, "用户昵称缓存成功");
        }

        /// <summary>
        /// 获取用户昵称
        /// </summary>
        /// <param name="gameUserId">游戏用户ID</param>
        /// <returns>用户昵称</returns>
        [HttpGet("{gameUserId}/nickname")]
        public async Task<Result<string>> GetNicknameAsync(string gameUserId)
        {
            var nickname = await _userCacheService.GetUserNicknameAsync(gameUserId);
            if (nickname == null)
            {
                return Fail<string>("用户昵称未找到", 400);
            }
            return Success(nickname, "获取用户昵称成功");
        }

        /// <summary>
        /// 批量获取用户昵称
        /// </summary>
        /// <param name="gameUserIds">游戏用户ID列表</param>
        /// <returns>用户昵称字典</returns>
        [HttpPost("nicknames")]
        public async Task<Result<Dictionary<string, string>>> GetNicknamesAsync([FromBody] List<string> gameUserIds)
        {
            var result = await _userCacheService.GetUserNicknamesAsync(gameUserIds);
            return Success(result, "获取用户昵称成功");
        }
    }
}
