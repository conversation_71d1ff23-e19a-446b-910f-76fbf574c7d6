using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text;

namespace BLL.SysService.Exports
{
    /// <summary>
    /// 导出服务实现
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="logger">日志记录器</param>
    public class ExportService(ILogger<ExportService> logger)
    {
        private readonly ILogger<ExportService> _logger = logger;

        /// <summary>
        /// 导出CSV文件
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要导出的数据</param>
        /// <param name="columns">要导出的列配置</param>
        /// <returns>文件字节数组</returns>
        public byte[] ExportToCsv<T>(IEnumerable<T>? data, List<ExportColumn>? columns)
        {
            try
            {
                //_logger.LogInformation("开始导出CSV文件");

                // 处理null参数
                data ??= [];
                columns ??= [];

                // 按排序顺序排列列
                columns = [.. columns.OrderBy(c => c.Order)];

                using var memoryStream = new MemoryStream();
                // 使用带BOM的UTF8编码
                using var writer = new StreamWriter(memoryStream, new UTF8Encoding(true));

                // 写入表头
                var headerLine = string.Join(",", columns.Select(c => EscapeCsvField(c.Title)));
                writer.WriteLine(headerLine);

                // 写入数据行
                foreach (var item in data)
                {
                    var values = new List<string>();

                    foreach (var column in columns)
                    {
                        var value = GetPropertyValue(item, column.PropertyName, column.Format);
                        values.Add(EscapeCsvField(value));
                    }

                    writer.WriteLine(string.Join(",", values));
                }

                writer.Flush();
                //_logger.LogInformation("CSV文件导出完成");

                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出CSV文件失败");
                throw new ApplicationException("导出CSV文件失败", ex);
            }
        }

        /// <summary>
        /// 通用导出方法
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要导出的数据</param>
        /// <param name="exportRequest">导出配置</param>
        /// <param name="defaultFileName">默认文件名</param>
        /// <returns>文件结果</returns>
        public FileContentResult ExportToFile<T>(IEnumerable<T>? data, ExportRequestDto exportRequest, string defaultFileName)
        {
            try
            {
                _logger.LogInformation("开始通用导出文件");

                // 如果没有提供导出列配置，则返回错误
                if (exportRequest.Columns == null || exportRequest.Columns.Count == 0)
                {
                    throw new ArgumentException("导出列配置不能为空");
                }

                // 创建ExportColumn列表
                var columns = exportRequest.Columns.Select(c => new ExportColumn
                {
                    Title = c.Title ?? string.Empty,
                    PropertyName = c.PropertyName ?? string.Empty,
                    Order = c.Order,
                    Format = c.Format
                }).ToList();

                // 生成CSV数据
                byte[] csvData = ExportToCsv(data, columns);

                // 简化文件名逻辑
                var fileName = !string.IsNullOrEmpty(exportRequest.FileName)
                    ? $"{exportRequest.FileName}.csv"
                    : $"{defaultFileName}_{DateTime.Now:yyyyMMddHHmmss}.csv";

                _logger.LogInformation("通用导出文件完成");

                // 返回文件内容结果
                return new FileContentResult(
                    csvData,
                    "text/csv; charset=gb2312")
                {
                    FileDownloadName = fileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "通用导出文件失败");
                throw new ApplicationException("通用导出文件失败", ex);
            }
        }

        /// <summary>
        /// 获取属性值
        /// </summary>
        /// <param name="obj">数据项</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="format">格式化字符串</param>
        /// <returns>格式化后的属性值</returns>
        private string GetPropertyValue(object? obj, string propertyName, string? format)
        {
            try
            {
                if (obj == null || string.IsNullOrEmpty(propertyName))
                {
                    return string.Empty;
                }

                // 支持嵌套属性 如 User.Department.Name
                string[] properties = propertyName.Split('.');
                object? value = obj;

                foreach (var property in properties)
                {
                    if (value == null)
                    {
                        return string.Empty;
                    }

                    // 不区分大小写查找属性
                    PropertyInfo? propertyInfo = value.GetType().GetProperties()
                        .FirstOrDefault(p => string.Equals(p.Name, property, StringComparison.OrdinalIgnoreCase));

                    if (propertyInfo == null)
                    {
                        return string.Empty;
                    }

                    value = propertyInfo.GetValue(value);
                }

                if (value == null)
                {
                    return string.Empty;
                }

                // 特殊类型处理
                if (value is DateTime dateTime)
                {
                    return !string.IsNullOrEmpty(format)
                        ? dateTime.ToString(format)
                        : dateTime.ToString("yyyy-MM-dd HH:mm:ss");
                }

                if (value is bool boolean)
                {
                    return boolean ? "是" : "否";
                }

                if (value is Enum)
                {
                    return value.ToString() ?? string.Empty;
                }

                // 其他类型转换为字符串
                return value.ToString() ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取属性值时出错: {PropertyName}", propertyName);
                return string.Empty;
            }
        }

        /// <summary>
        /// 转义CSV字段
        /// </summary>
        /// <param name="field">字段值</param>
        /// <returns>转义后的字段值</returns>
        private static string EscapeCsvField(string? field)
        {
            if (string.IsNullOrEmpty(field))
                return "\"\"";

            // 如果字段包含逗号、引号、换行符或回车符，需要用引号包围并转义内部引号
            bool needsQuotes = field.Contains(',') || field.Contains('"') ||
                             field.Contains('\n') || field.Contains('\r');

            if (needsQuotes)
            {
                // 将字段中的引号替换为两个引号
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }
    }
}