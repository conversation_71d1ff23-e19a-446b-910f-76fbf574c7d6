# 卡片管理系统 API 文档（简化版）

## API 概览

### 1. 卡片批次管理

- `POST /api/card-batch/create` - 创建卡片批次
- `POST /api/card-batch/activate` - 批次激活
- `GET /api/card-batch/query` - 批次查询

### 2. 卡片管理

- `GET /api/card/query` - 卡片查询
- `POST /api/card/use` - 卡片核销

### 3. 卡片流转管理

- `POST /api/card/transfer` - 卡片转让
- `GET /api/card/transfer/records` - 流转记录查询

### 4. 出货管理

- `POST /api/shipping/create` - 创建出货单
- `POST /api/shipping-target/create` - 创建出货目标
- `GET /api/shipping/records` - 出货记录查询

### 5. 渠道配置管理

- `POST /api/channel-config/create` - 添加渠道配置
- `PUT /api/channel-config/update` - 修改渠道配置
- `GET /api/channel-config/query` - 查询渠道配置

## API 详细说明

### 1.1 创建卡片批次

```
POST /api/card-batch/create
```

请求参数：

```json
{
  "channel": "string", // 卡片所属渠道
  "type": "string", // 卡片类型
  "faceValue": "int", // 卡片面额
  "price": "decimal", // 卡片售价
  "quantity": "int", // 卡片数量
  "remark": "string" // 备注信息（可选）
}
```

### 1.2 批次激活

```
POST /api/card-batch/activate
```

请求参数：

```json
{
  "batchNo": "string" // 批次号
}
```

### 2.1 卡片查询

```
GET /api/card/query
```

请求参数：

```json
{
  "cardNo": "string", // 卡号（可选）
  "batchNo": "string", // 批次号（可选）
  "status": "string", // 状态（可选）
  "holderId": "int" // 持有人ID（可选）
}
```

### 2.2 卡片核销

```
POST /api/card/use
```

请求参数：

```json
{
  "cardNo": "string", // 卡号
  "cardPwd": "string", // 卡片密码
  "playerId": "int", // 玩家ID
  "ip": "string" // 操作IP
}
```

### 3.1 卡片转让

```
POST /api/card/transfer
```

请求参数：

```json
{
  "cardNo": "string", // 卡号
  "fromId": "int", // 转出人ID
  "toId": "int", // 接收人ID
  "amount": "decimal", // 交易金额（可选）
  "transferType": "string" // 流转类型
}
```

### 4.1 创建出货单

```
POST /api/shipping/create
```

请求参数：

```json
{
  "batchNo": "string", // 批次号
  "targetId": "int", // 出货目标ID
  "quantity": "int", // 出货数量
  "remark": "string" // 备注信息（可选）
}
```

### 5.1 添加渠道配置

```
POST /api/channel-config/create
```

请求参数：

```json
{
  "channel": "string", // 渠道
  "cardType": "string", // 卡片类型
  "faceValue": "int", // 面额
  "price": "decimal" // 售价
}
```

## 通用说明

### 状态码

- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

### 请求头

```
Content-Type: application/json
Authorization: Bearer {token}
```

### 分页参数

所有查询接口都支持以下分页参数：

```json
{
  "pageSize": "int", // 每页数量
  "pageNum": "int" // 页码
}
```

### 响应格式

所有接口统一返回格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  }
}
```
