using Entity.Entitys.SysEntity;

namespace Entity.Dto
{
    /// <summary>
    /// 用户详细信息 DTO
    /// </summary>
    public class UserDetailDto
    {
        /// <summary>
        /// 用户基本信息
        /// </summary>
        public SysUser User { get; set; } = null!;

        /// <summary>
        /// 用户角色关联列表
        /// </summary>
        public List<SysUserRole> UserRoles { get; set; } = [];

        /// <summary>
        /// 角色列表
        /// </summary>
        public List<SysRole> Roles { get; set; } = [];

        /// <summary>
        /// 菜单列表
        /// </summary>
        public List<SysMenu> Menus { get; set; } = [];
    }
}