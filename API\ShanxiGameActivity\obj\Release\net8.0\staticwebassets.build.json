{"Version": 1, "Hash": "gPUJsLKvPYDSwK/z5Q3f8uNIWsrV6/EpaZ7TXyL4S/A=", "Source": "DataMgrSystem", "BasePath": "_content/DataMgrSystem", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DataMgrSystem\\wwwroot", "Source": "DataMgrSystem", "ContentRoot": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\", "BasePath": "_content/DataMgrSystem", "Pattern": "**"}], "Assets": [{"Identity": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\build-app.bat", "SourceId": "DataMgrSystem", "SourceType": "Discovered", "ContentRoot": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\", "BasePath": "_content/DataMgrSystem", "RelativePath": "build-app#[.{fingerprint}]?.bat", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "np9u4khkac", "Integrity": "VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\build-app.bat", "FileLength": 672, "LastWriteTime": "2025-05-07T02:41:29+00:00"}, {"Identity": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-app.bat", "SourceId": "DataMgrSystem", "SourceType": "Discovered", "ContentRoot": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\", "BasePath": "_content/DataMgrSystem", "RelativePath": "start-app#[.{fingerprint}]?.bat", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mqzi4oyyqd", "Integrity": "bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\start-app.bat", "FileLength": 142, "LastWriteTime": "2025-05-07T02:41:29+00:00"}, {"Identity": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-without-debug.bat", "SourceId": "DataMgrSystem", "SourceType": "Discovered", "ContentRoot": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\", "BasePath": "_content/DataMgrSystem", "RelativePath": "start-without-debug#[.{fingerprint}]?.bat", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7rvjcizg5g", "Integrity": "fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\start-without-debug.bat", "FileLength": 953, "LastWriteTime": "2025-05-07T02:41:29+00:00"}], "Endpoints": [{"Route": "build-app.bat", "AssetFile": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\build-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "672"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E="}]}, {"Route": "build-app.np9u4khkac.bat", "AssetFile": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\build-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "672"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "np9u4khkac"}, {"Name": "label", "Value": "build-app.bat"}, {"Name": "integrity", "Value": "sha256-VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E="}]}, {"Route": "start-app.bat", "AssetFile": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "142"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ="}]}, {"Route": "start-app.mqzi4oyyqd.bat", "AssetFile": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "142"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mqzi4oyyqd"}, {"Name": "label", "Value": "start-app.bat"}, {"Name": "integrity", "Value": "sha256-bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ="}]}, {"Route": "start-without-debug.7rvjcizg5g.bat", "AssetFile": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-without-debug.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "953"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7rvjcizg5g"}, {"Name": "label", "Value": "start-without-debug.bat"}, {"Name": "integrity", "Value": "sha256-fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8="}]}, {"Route": "start-without-debug.bat", "AssetFile": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-without-debug.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "953"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8="}]}]}