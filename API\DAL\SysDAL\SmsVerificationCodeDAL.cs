
using DAL.Databases;
using Entity.Entitys;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.SysDAL
{
    /// <summary>
    /// 短信验证码数据访问层
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="context">数据库上下文</param>
    public class SmsVerificationCodeDAL(MyContext context) : BaseQueryDLL<SmsVerificationCode, SmsVerificationCodeDAL.SmsCodeQueryable>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 验证码查询实体
        /// </summary>
        public class SmsCodeQueryable : PageQueryEntity
        {
            /// <summary>
            /// 手机号码
            /// </summary>
            [Query(QueryOperator.等于)]
            public string PhoneNumber { get; set; } = string.Empty;

            /// <summary>
            /// 业务类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public string BusinessType { get; set; } = string.Empty;

            /// <summary>
            /// 是否已使用
            /// </summary>
            [Query(QueryOperator.等于)]
            public bool? IsUsed { get; set; }

            /// <summary>
            /// 创建时间排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "SendTime", orderDirection: OrderDirection.降序, orderPriority: 1)]
            public int? OrderBySendTime { get; set; } = 1; // 默认按发送时间降序排序
        }

        /// <summary>
        /// 新增验证码记录
        /// </summary>
        /// <param name="verificationCode">验证码记录</param>
        /// <returns>受影响的行数</returns>
        public async Task<bool> AddVerificationCodeAsync(SmsVerificationCode verificationCode)
        {
            try
            {
                return await AddAsync(verificationCode);
            }
            catch (Exception ex)
            {
                throw new Exception("添加验证码记录异常", ex);
            }
        }

        /// <summary>
        /// 获取最新的未使用的验证码
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>验证码记录</returns>
        public async Task<SmsVerificationCode> GetLatestValidCodeAsync(string phoneNumber, string businessType)
        => await _context.Set<SmsVerificationCode>()
            .Where(v =>
                v.PhoneNumber == phoneNumber &&
                v.BusinessType == businessType &&
                !v.IsUsed &&
                v.ExpireTime > DateTime.Now)
            .OrderByDescending(v => v.SendTime)
            .FirstAsync();

        /// <summary>
        /// 标记验证码为已使用
        /// </summary>
        /// <param name="id">验证码记录ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> MarkAsUsedAsync(int id)
        {
            try
            {
                var code = await _context.Set<SmsVerificationCode>().FindAsync(id);
                if (code == null) return false;


                code.IsUsed = true;
                code.UsedTime = DateTime.Now;

                _context.Set<SmsVerificationCode>().Update(code);
                return await _context.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception("标记验证码已使用异常", ex);
            }
        }

        /// <summary>
        /// 检查验证码是否有效
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="code">验证码</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="markAsUsed">验证成功后是否标记为已使用</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateCodeAsync(string phoneNumber, string code, string businessType, bool markAsUsed = true)
        {
            try
            {
                var verificationCode = await _context.Set<SmsVerificationCode>()
                    .Where(v =>
                        v.PhoneNumber == phoneNumber &&
                        v.BusinessType == businessType &&
                        !v.IsUsed &&
                        v.ExpireTime > DateTime.Now)
                    .OrderByDescending(v => v.SendTime)
                    .FirstOrDefaultAsync() ?? throw new Exception("验证码不存在或已过期");
                bool isValid = string.Equals(verificationCode.Code, code, StringComparison.OrdinalIgnoreCase);



                // 如果验证成功并且需要标记为已使用
                if (isValid && markAsUsed)
                {
                    verificationCode.IsUsed = true;
                    verificationCode.UsedTime = DateTime.Now;

                    await UpdateAsync(verificationCode);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                throw new Exception("验证码校验异常", ex);
            }
        }

        /// <summary>
        /// 清除指定手机号和业务类型的所有未使用验证码
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ClearUnusedCodesAsync(string phoneNumber, string businessType)
        {
            try
            {
                var codes = await _context.Set<SmsVerificationCode>()
                    .Where(v =>
                        v.PhoneNumber == phoneNumber &&
                        v.BusinessType == businessType &&
                        !v.IsUsed)
                    .ToListAsync();

                if (codes.Count == 0) return true;


                foreach (var c in codes)
                {
                    c.IsUsed = true;
                    c.UsedTime = DateTime.Now;
                }

                _context.Set<SmsVerificationCode>().UpdateRange(codes);
                return await _context.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception("清除未使用验证码异常", ex);
            }
        }
    }
}