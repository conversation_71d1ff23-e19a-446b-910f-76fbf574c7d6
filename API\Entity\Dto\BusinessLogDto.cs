using Microsoft.Extensions.Logging;

namespace Entity.Dto
{
    /// <summary>
    /// 业务操作日志DTO
    /// </summary>
    public class BusinessLogDto
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        public string Module { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// 业务对象名称
        /// </summary>
        public string BusinessObject { get; set; } = string.Empty;

        /// <summary>
        /// 对象ID
        /// </summary>
        public string ObjectId { get; set; } = string.Empty;

        /// <summary>
        /// 详细信息
        /// </summary>
        public string DetailedInfo { get; set; } = string.Empty;

        /// <summary>
        /// 操作前数据
        /// </summary>
        public object? BeforeData { get; set; }

        /// <summary>
        /// 操作后数据
        /// </summary>
        public object? AfterData { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? Username { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; } = LogLevel.Information;

        /// <summary>
        /// IP地址
        /// </summary>
        public string? Ip { get; set; }

        /// <summary>
        /// 请求路径
        /// </summary>
        public string? Path { get; set; }
    }
}
