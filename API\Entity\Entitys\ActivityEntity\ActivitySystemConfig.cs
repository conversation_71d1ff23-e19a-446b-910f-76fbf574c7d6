using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 系统配置表
/// </summary>
[Table("activity_system_config")]
public class ActivitySystemConfig : BaseEntity_ID
{
    /// <summary>
    /// 配置键
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("config_key")]
    [Comment("配置键")]
    public string ConfigKey { get; set; } = string.Empty;

    /// <summary>
    /// 配置值
    /// </summary>
    [Required]
    [Column("config_value", TypeName = "TEXT")]
    [Comment("配置值")]
    public string ConfigValue { get; set; } = string.Empty;

    /// <summary>
    /// 配置描述
    /// </summary>
    [MaxLength(500)]
    [Column("description")]
    [Comment("配置描述")]
    public string? Description { get; set; }
}
