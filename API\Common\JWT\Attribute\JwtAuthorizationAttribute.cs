﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;

namespace Common.JWT.Attribute
{
    /// <summary>
    /// JWT 授权验证
    /// </summary>
    public class JwtAuthorizationAttribute : ActionFilterAttribute
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public JwtAuthorizationAttribute() { }
        /// <summary>
        /// 是否需要验证
        /// </summary>
        /// <param name="enabled"></param>
        public JwtAuthorizationAttribute(bool enabled)
        {
            _enabled = enabled;
        }

        /// <summary>
        /// 是否需要验证
        /// </summary>
        private readonly bool _enabled = true;
        /// <summary>
        /// 验证Token 是否合法
        /// </summary>
        /// <param name="context"></param>
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            //判断是否需要验证
            if (!_enabled) return;

            #region 获取Token
            var request = context.HttpContext.Request.Headers.Authorization.ToString();
            var requests = request.Split(' ');
            if (requests.Length > 1) { request = requests[1]; }
            #endregion
            try
            {
                JWTHelper.ValidateJwtToken(request);
            }
            catch (Exception)
            {
                context.Result = new ContentResult()
                {
                    Content = JsonConvert.SerializeObject(new { code = 401, msg = "身份验证失败/过期" }),
                    ContentType = "application/json",
                };
            }
        }

    }
}
