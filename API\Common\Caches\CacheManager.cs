﻿using Common.Redis;

namespace Common.Caches
{
    public class CacheManager
    {

        #region 获取缓存值 [多级缓存]
        /// <summary>
        /// 获取缓存值 [多级缓存]
        /// 
        /// 优先查询内存缓存，若没有则去全局缓存 若全局没有则返回空值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">缓存Key</param>
        /// <param name="time">缓存时间</param>
        /// <returns></returns>
        public static T? MultilevelCacheGetOrSet<T>(string key, Func<T> func, TimeSpan time)
        {
            // 查询本地缓存
            var res = MemoryCacheHelper.Get<T>(key);
            if (res == null)
            {
                // 查询全局缓存
                res = RedisHelper.Get<T>(key);
                //如果没有则设置缓存值
                if (res == null) return MultilevelCacheSet(key, func, time);
                // 如果全局缓存存在 获取全局缓存  存入本地缓存
                MemoryCacheHelper.Set(key, res, time);
            }
            return res;
        }

        #endregion

        #region 设置缓存值 [多级缓存]

        public static T MultilevelCacheSet<T>(string key, T value, TimeSpan time)
        {
            // 设置本地缓存
            MemoryCacheHelper.Set(key, value, time);
            // 设置全局缓存
            RedisHelper.Set(key, value, time);

            return value;
        }

        public static T MultilevelCacheSet<T>(string key, Func<T> func, TimeSpan time)
        {
            var value = func();
            // 设置本地缓存
            MemoryCacheHelper.Set(key, value, time);
            // 设置全局缓存
            RedisHelper.Set(key, value, time);
            return value;
        }

        public static async Task<T> MultilevelCacheSetAsync<T>(string key, Func<T> func, TimeSpan time) =>
            await Task.Run(() =>
             {
                 var value = func();
                 // 设置本地缓存
                 MemoryCacheHelper.Set(key, value, time);
                 // 设置全局缓存
                 RedisHelper.Set(key, value, time);

                 return value;
             });

        public static async Task<T> MultilevelCacheSetAsync<T>(string key, T value, TimeSpan time) =>

            await Task.Run(() =>
            {
                // 设置本地缓存
                MemoryCacheHelper.Set(key, value, time);
                // 设置全局缓存
                RedisHelper.Set(key, value, time);
                return value;
            });


        #endregion


    }
}
