using Common.Caches;
using Common.Redis;
using ShanxiGameActivity.Controllers.Attributes;
using System.Security.Cryptography;
using System.Text;

namespace ShanxiGameActivity.Controllers.Middleware
{
    /// <summary>
    /// 缓存中间件
    /// </summary>
    /// <param name="next"></param>
    public class CachingMiddleware(RequestDelegate next)
    {
        private readonly RequestDelegate _next = next;

        /// <summary>
        /// 中间件处理方法
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        public async Task InvokeAsync(HttpContext context)
        {
            // 1. 获取当前Endpoint
            var endpoint = context.GetEndpoint();
            if (endpoint == null)
            {
                await _next(context);
                return;
            }

            // 2. 检查是否有缓存特性
            var cacheAttribute = endpoint.Metadata.GetMetadata<ActionCacheAttribute>();
            if (cacheAttribute == null)
            {
                await _next(context);
                return;
            }

            // 3. 生成缓存键
            var (baseKey, hashField) = await GenerateCacheKey(context, cacheAttribute);

            // 4. 尝试获取缓存并返回
            if (await TryGetAndReturnCachedResponse(context, cacheAttribute, baseKey, hashField)) return; // 已从缓存返回响应


            // 5. 捕获响应
            var originalBodyStream = context.Response.Body;
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            // 6. 继续处理请求
            await _next(context);

            // 7. 如果响应成功，则缓存
            if (context.Response.StatusCode >= 200 && context.Response.StatusCode < 300)
            {
                responseBody.Seek(0, SeekOrigin.Begin);
                var responseContent = await new StreamReader(responseBody).ReadToEndAsync();

                // 缓存响应
                SetCache(baseKey, hashField, responseContent, cacheAttribute);

                // 重置响应流位置以便返回
                responseBody.Seek(0, SeekOrigin.Begin);
            }

            // 8. 将捕获的响应复制回原始stream
            await responseBody.CopyToAsync(originalBodyStream);
        }

        /// <summary>
        /// 生成缓存键
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="attribute">缓存特性</param>
        /// <returns>基础键和哈希字段</returns>
        private static async Task<(string BaseKey, string HashField)> GenerateCacheKey(HttpContext context, ActionCacheAttribute attribute)
        {
            // 1. 获取控制器和动作名
            var routeData = context.GetRouteData();
            var controller = routeData?.Values["controller"]?.ToString() ?? "Unknown";
            var action = routeData?.Values["action"]?.ToString() ?? "Unknown";

            // 2. 构建基础键
            string baseKey = $"{attribute.CachePrefix}{controller}:{action}";

            // 3. 获取请求信息
            var request = context.Request;
            string method = request.Method;
            string queryString = request.QueryString.ToString();

            // 4. 获取请求体内容
            string bodyContent = string.Empty;
            if (request.Body.CanRead && (method == "POST" || method == "PUT"))
            {
                request.EnableBuffering(); // 允许多次读取
                bodyContent = await new StreamReader(request.Body).ReadToEndAsync();
                request.Body.Position = 0; // 重置位置以便后续处理
            }

            // 5. 生成参数哈希
            string paramsHash = GenerateShortCode($"{method}:{queryString}:{bodyContent}");

            // 6. 根据范围添加用户标识
            string scopePrefix = string.Empty;
            if (attribute.CacheScope != CacheScope.全局 || attribute.IsToken == Whether.是)
            {
                // 获取认证信息
                if (request.Headers.TryGetValue("Authorization", out Microsoft.Extensions.Primitives.StringValues value))
                {
                    scopePrefix = value.ToString();
                }
            }

            // 7. 返回基础键和哈希字段值
            return (attribute.UseHashStructure ? baseKey : $"{baseKey}{scopePrefix}:{paramsHash}",
                    attribute.UseHashStructure ? $"{scopePrefix}:{paramsHash}" : string.Empty);
        }

        /// <summary>
        /// 生成短码
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>生成的短码</returns>
        private static string GenerateShortCode(string input)
        {
            byte[] hash = MD5.HashData(Encoding.UTF8.GetBytes(input));
            return Convert.ToBase64String(hash)[..8].Replace('/', '_').Replace('+', '-');
        }

        /// <summary>
        /// 尝试获取缓存并返回响应
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="attribute">缓存特性</param>
        /// <param name="baseKey">基础键</param>
        /// <param name="hashField">哈希字段</param>
        /// <returns>是否成功获取缓存</returns>
        private static async Task<bool> TryGetAndReturnCachedResponse(HttpContext context, ActionCacheAttribute attribute, string baseKey, string hashField)
        {
            string? cachedResponse;

            // 1. 根据缓存类型获取缓存
            if (attribute.CacheType == CacheType.内存)
                cachedResponse = MemoryCacheHelper.Get<string>(baseKey);
            else // Redis缓存
            {
                if (attribute.UseHashStructure)
                {
                    cachedResponse = RedisHelper.HashGet<string>(baseKey, hashField, attribute.RedisInstance);

                    // 滑动过期处理
                    if (cachedResponse != null && attribute.ExpireType == CacheExpireType.滑动过期)
                        RedisHelper.GetDatabase(0, attribute.RedisInstance).KeyExpire(baseKey, TimeSpan.FromSeconds(attribute.CacheSeconds));

                }
                else
                    cachedResponse = RedisHelper.Get<string>(baseKey, attribute.RedisInstance);
            }

            // 2. 如果找到缓存，返回缓存的响应
            if (!string.IsNullOrEmpty(cachedResponse))
            {
                context.Response.ContentType = "application/json; charset=utf-8";
                await context.Response.WriteAsync(cachedResponse);
                return true;
            }

            return false;
        }

        /// <summary>
        /// 设置缓存
        /// </summary>
        /// <param name="baseKey">基础键</param>
        /// <param name="hashField">哈希字段</param>
        /// <param name="responseContent">响应内容</param>
        /// <param name="attribute">缓存特性</param>
        private static void SetCache(string baseKey, string hashField, string responseContent, ActionCacheAttribute attribute)
        {
            // 根据缓存类型设置
            if (attribute.CacheType == CacheType.内存)
                MemoryCacheHelper.Set(baseKey, responseContent, TimeSpan.FromSeconds(attribute.CacheSeconds));
            else // Redis缓存
            {
                if (attribute.UseHashStructure)
                {
                    // 使用Hash结构
                    var db = RedisHelper.GetDatabase(0, attribute.RedisInstance);
                    db.HashSet(baseKey, hashField, responseContent);

                    // 设置过期时间
                    SetRedisExpiry(baseKey, attribute);
                }
                else
                {
                    // 使用String结构
                    if (attribute.ExpireType == CacheExpireType.绝对时间)
                        RedisHelper.GetOrSet(baseKey, () => responseContent, attribute.TimeScope, attribute.TimeScopeCount, true, attribute.RedisInstance);
                    else
                        RedisHelper.Set(baseKey, responseContent, TimeSpan.FromSeconds(attribute.CacheSeconds), attribute.RedisInstance);
                }
            }
        }

        /// <summary>
        /// 设置Redis缓存过期时间
        /// </summary>
        private static void SetRedisExpiry(string key, ActionCacheAttribute attribute)
        {
            var db = RedisHelper.GetDatabase(0, attribute.RedisInstance);

            switch (attribute.ExpireType)
            {
                case CacheExpireType.固定时间:
                    db.KeyExpire(key, TimeSpan.FromSeconds(attribute.CacheSeconds));
                    break;
                case CacheExpireType.绝对时间:
                    db.KeyExpire(key, RedisHelper.GetTargetTimeDifferenceTimeSpan(attribute.TimeScope, attribute.TimeScopeCount));
                    break;
                case CacheExpireType.滑动过期:
                    // 设置初始过期时间，滑动过期在访问时更新
                    db.KeyExpire(key, TimeSpan.FromSeconds(attribute.CacheSeconds));
                    break;
            }
        }
    }

    /// <summary>
    /// 中间件扩展方法
    /// </summary>
    public static class CachingMiddlewareExtensions
    {
        /// <summary>
        /// 添加缓存中间件到应用程序管道
        /// </summary>
        public static IApplicationBuilder UseCaching(this IApplicationBuilder builder)
        => builder.UseMiddleware<CachingMiddleware>();
    }
}