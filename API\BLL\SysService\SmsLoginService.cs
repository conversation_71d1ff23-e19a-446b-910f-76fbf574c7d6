using DAL.SysDAL;
using Entity.Dto;

namespace BLL.SysService
{
    /// <summary>
    /// 短信验证码登录服务
    /// </summary>
    public class SmsLoginService(
        SysUserDAL userDAL,
        SmsVerificationService smsVerificationService,
        SysUserService sysUserService)
    {
        private readonly SysUserDAL _userDAL = userDAL;
        private readonly SmsVerificationService _smsVerificationService = smsVerificationService;
        private readonly SysUserService _sysUserService = sysUserService;

        // 登录业务类型
        private const string LOGIN_BUSINESS_TYPE = "LOGIN";

        /// <summary>
        /// 发送登录验证码
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <returns>发送结果</returns>
        public async Task<(bool Success, string Message)> SendLoginVerificationCodeAsync(string phoneNumber)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(phoneNumber))
                    return (false, "手机号码不能为空");


                // 检查手机号是否已注册
                var user = await _userDAL.GetFirstAsync(new SysUserDAL.UserDALQuery { Mobile = phoneNumber });
                if (user == null)
                    return (false, "未找到手机号");

                // 发送验证码
                var (Success, ErrorMessage) = await _smsVerificationService.SendVerificationCodeAsync(phoneNumber, LOGIN_BUSINESS_TYPE);
                return (Success, Success ? "验证码发送成功" : $"验证码发送失败: {ErrorMessage}");
            }
            catch (Exception ex)
            {
                throw new Exception("发送登录验证码异常", ex);
            }
        }

        /// <summary>
        /// 使用短信验证码登录
        /// </summary>
        /// <param name="dto">登录信息</param>
        /// <returns>登录结果</returns>
        public async Task<(bool Success, string Message, LoginResponseDto? Data)> LoginWithSmsAsync(SmsLoginDto dto)
        {
            try
            {
                // 参数验证
                if (dto == null || string.IsNullOrEmpty(dto.PhoneNumber) || string.IsNullOrEmpty(dto.VerificationCode))
                    return (false, "手机号或验证码不能为空", null);

                // 验证验证码
                bool isCodeValid = await _smsVerificationService.ValidateCodeAsync(dto.PhoneNumber, dto.VerificationCode, LOGIN_BUSINESS_TYPE);
                if (!isCodeValid) return (false, "验证码错误或已失效", null);

                // 查找用户
                var user = await _userDAL.GetFirstAsync(new SysUserDAL.UserDALQuery { Mobile = dto.PhoneNumber });
                if (user == null) return (false, "用户不存在", null);

                return (true, "成功", await _sysUserService.GenerateLoginResponseAsync(user));
            }
            catch (Exception ex)
            {
                throw new Exception("短信验证码登录异常", ex);
            }
        }
    }
}