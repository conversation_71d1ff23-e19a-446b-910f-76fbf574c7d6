﻿using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.BasisController
{
    [Route("api/[controller]")]
    [ApiController]
    public class BasisController : BaseController
    {

        /// <summary>
        /// 本地单文件上传
        /// </summary>
        /// <param name="file">文件</param>
        /// <returns></returns>
        [HttpPost("UploadFileAsync")]
        public async Task<Result> UploadFileAsync(IFormFile file)
        {
            var res = new Result();
            if (file.Length <= 0)
            {
                res.Success = false;
                res.Msg = $"文件上传失败，失败原因：文件大小不能为 0";
                return res;
            }

            string path = Environment.CurrentDirectory + "/UploadFile/" + DateTime.Now.ToString("yyyy-MM-dd");
            try
            {
                if (!Directory.Exists(path))//判断文件夹是否存在
                    Directory.CreateDirectory(path);//不存在则创建文件夹


                #region 生成新文件名
                var fileNames = file.FileName.Split('.');
                string fileName = string.Empty;
                //判断文件是否有扩展名
                fileName = fileNames.Length > 1
                    ? $"{fileNames[0]}{DateTime.Now:HHmmssfff}.{fileNames[1]}"
                    : $"{fileNames[0]}{DateTime.Now:HHmmssfff}";
                #endregion

                using var stream = System.IO.File.Create($"{path}/{fileName}");
                await file.CopyToAsync(stream);
            }
            catch (Exception ex)
            {
                res.Success = false;
                res.Msg = $"文件上传失败，请联系管理员，失败原因：{ex.Message}";
                return res;
            }

            res.Msg = "文件上传成功!";
            return res;
        }

        /// <summary>
        /// 本地多文件上传
        /// </summary>
        /// <param name="files">文件列表</param>
        /// <returns></returns>
        [HttpPost("UploadFilesAsync")]
        public async Task<Result> UploadFilesAsync(List<IFormFile> files)
        {
            var res = new Result();
            if (files.Count <= 0)
            {
                res.Success = false;
                res.Msg = $"文件上传失败，未传入文件";
                return res;
            }
            foreach (var file in files)
            {
                if (file.Length <= 0)
                {
                    res.Success = false;
                    res.Msg += $"文件{file.FileName}上传失败，失败原因：文件大小不能为 0";
                    continue;
                }

                string path = Environment.CurrentDirectory + "/UploadFile/" + DateTime.Now.ToString("yyyy-MM-dd");
                try
                {
                    if (!Directory.Exists(path))//判断文件夹是否存在
                        Directory.CreateDirectory(path);//不存在则创建文件夹

                    #region 生成新文件名
                    var fileNames = file.FileName.Split('.');
                    string fileName = string.Empty;
                    //判断文件是否有扩展名
                    fileName = fileNames.Length > 1
                        ? $"{fileNames[0]}{DateTime.Now:HHmmssfff}.{fileNames[1]}"
                        : $"{fileNames[0]}{DateTime.Now:HHmmssfff}";
                    #endregion

                    using var stream = System.IO.File.Create($"{path}/{fileName}");
                    await file.CopyToAsync(stream);
                }
                catch (Exception ex)
                {
                    res.Success = false;
                    res.Msg += $"文件{file.FileName}上传失败，请联系管理员，失败原因：{ex.Message}";
                    continue;
                }
            }
            //如果有一个文件上传失败  返回失败信息
            if (!res.Success) return res;

            res.Msg = "文件上传成功!";
            return res;
        }

    }
}
