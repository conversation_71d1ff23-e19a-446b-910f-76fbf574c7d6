using System.ComponentModel.DataAnnotations;

namespace Entity.Dto;

/// <summary>
/// 同步通宝余额请求DTO
/// </summary>
public class SyncTongbaoBalanceDto
{
    /// <summary>
    /// 余额
    /// </summary>
    [Required(ErrorMessage = "余额不能为空")]
    public decimal Balance { get; set; }

    /// <summary>
    /// 变化金额
    /// </summary>
    [Required(ErrorMessage = "变化金额不能为空")]
    public decimal ChangeAmount { get; set; }

    /// <summary>
    /// 变化原因
    /// </summary>
    [Required(ErrorMessage = "变化原因不能为空")]
    public string ChangeReason { get; set; } = string.Empty;

    /// <summary>
    /// 活动ID
    /// </summary>
    public int? ActivityId { get; set; }
}

/// <summary>
/// 通宝余额响应DTO
/// </summary>
public class TongbaoBalanceResponseDto
{
    /// <summary>
    /// 余额
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// 冻结金额
    /// </summary>
    public decimal FrozenAmount { get; set; }

    /// <summary>
    /// 可用金额
    /// </summary>
    public decimal AvailableAmount { get; set; }
}

/// <summary>
/// 通宝余额同步响应DTO
/// </summary>
public class SyncTongbaoResponseDto
{
    /// <summary>
    /// 原余额
    /// </summary>
    public decimal OldBalance { get; set; }

    /// <summary>
    /// 新余额
    /// </summary>
    public decimal NewBalance { get; set; }

    /// <summary>
    /// 交易ID
    /// </summary>
    public int TransactionId { get; set; }
}

/// <summary>
/// 通宝流水查询DTO
/// </summary>
public class TongbaoTransactionQueryDto : BaseQueryDto
{
    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    public TransactionType? Type { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 每页数量
    /// </summary>
    public int Limit { get; set; } = 10;
}

/// <summary>
/// 通宝流水响应DTO
/// </summary>
public class TongbaoTransactionResponseDto
{
    /// <summary>
    /// 流水ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    public TransactionType TransactionType { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 交易前余额
    /// </summary>
    public decimal BalanceBefore { get; set; }

    /// <summary>
    /// 交易后余额
    /// </summary>
    public decimal BalanceAfter { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
