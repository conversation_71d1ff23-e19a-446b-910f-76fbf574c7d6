using BLL.ActivityService;
using DAL.ActivityDAL;

namespace ShanxiGameActivity.Services.BackgroundServices
{
    /// <summary>
    /// 活动状态管理后台服务
    /// </summary>
    public class ActivityStatusBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<ActivityStatusBackgroundService> logger,
        IConfiguration configuration) : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider = serviceProvider;
        private readonly ILogger<ActivityStatusBackgroundService> _logger = logger;
        private readonly IConfiguration _configuration = configuration;

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("活动状态管理后台服务已启动");

            // 从配置中读取检查间隔，默认1分钟
            var checkIntervalMinutes = _configuration.GetValue<int>("ActivityStatus:CheckIntervalMinutes", 1);
            var checkInterval = TimeSpan.FromMinutes(checkIntervalMinutes);

            // 从配置中读取每日重置时间，默认0点
            var dailyResetHour = _configuration.GetValue<int>("ActivityStatus:DailyResetHour", 0);
            var lastResetDate = DateTime.Today.AddDays(-1); // 初始化为昨天，确保首次运行时会执行重置

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 执行活动状态更新
                    await UpdateActivityStatusAsync();

                    // 检查是否需要执行每日重置
                    var now = DateTime.Now;
                    var todayResetTime = DateTime.Today.AddHours(dailyResetHour);

                    if (now >= todayResetTime && lastResetDate < DateTime.Today)
                    {
                        await PerformDailyResetAsync();
                        lastResetDate = DateTime.Today;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行活动状态管理时发生异常");
                }

                // 等待下次检查
                try
                {
                    await Task.Delay(checkInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // 服务正在停止
                    break;
                }
            }

            _logger.LogInformation("活动状态管理后台服务已停止");
        }

        /// <summary>
        /// 更新活动状态
        /// </summary>
        private async Task UpdateActivityStatusAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var activityStatusService = scope.ServiceProvider.GetRequiredService<ActivityStatusService>();

            try
            {
                _logger.LogDebug("开始执行活动状态更新");

                var result = await activityStatusService.UpdateAllActivityStatusAsync();

                if (result.Success)
                {
                    if (result.UpdatedActivities > 0)
                    {
                        _logger.LogInformation($"活动状态更新完成：{result.Message}");
                    }
                    else
                    {
                        _logger.LogDebug($"活动状态检查完成：{result.Message}");
                    }
                }
                else
                {
                    _logger.LogWarning($"活动状态更新失败：{result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "活动状态更新过程中发生异常");
            }
        }

        /// <summary>
        /// 执行每日重置任务
        /// </summary>
        private async Task PerformDailyResetAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var activityStatusService = scope.ServiceProvider.GetRequiredService<ActivityStatusService>();

            try
            {
                _logger.LogInformation("开始执行每日重置任务");

                // 获取所有进行中的活动并重置每日奖励份数
                var activityDAL = scope.ServiceProvider.GetRequiredService<ActivityDAL>();
                var runningActivities = await activityDAL.GetListAsync(new ActivityDAL.ActivityQuery
                {
                    Status = Entity.ActivityStatus.running
                });

                var totalResetCount = 0;
                foreach (var activity in runningActivities)
                {
                    var resetCount = await activityStatusService.ResetDailyRewardQuantityAsync(activity.Id);
                    totalResetCount += resetCount;
                }

                _logger.LogInformation($"每日重置任务完成，共重置了 {totalResetCount} 个奖励的每日份数");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行每日重置任务时发生异常");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("正在停止活动状态管理后台服务...");
            await base.StopAsync(stoppingToken);
        }
    }
}
