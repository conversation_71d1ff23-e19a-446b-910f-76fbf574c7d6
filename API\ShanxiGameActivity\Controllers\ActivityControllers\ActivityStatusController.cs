using BLL.ActivityService;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.ActivityControllers
{
    /// <summary>
    /// 活动状态管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ActivityStatusController(ActivityStatusService activityStatusService) : BaseController
    {
        private readonly ActivityStatusService _activityStatusService = activityStatusService;

        /// <summary>
        /// 手动更新所有活动状态
        /// </summary>
        /// <returns>更新结果</returns>
        [HttpPost("update-all")]
        public async Task<Result<object>> UpdateAllActivityStatusAsync()
        {
            try
            {
                var result = await _activityStatusService.UpdateAllActivityStatusAsync();

                var response = new
                {
                    result.Success,
                    result.Message,
                    result.ProcessedActivities,
                    result.UpdatedActivities,
                    result.FailedActivities,
                    UpdateTime = DateTime.Now
                };

                if (result.Success)
                {
                    return Success((object)response, "活动状态更新成功");
                }
                else
                {
                    return Success((object)response, result.Message);
                }
            }
            catch (Exception ex)
            {
                return Fail<object>($"更新活动状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 手动更新指定活动状态
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>更新结果</returns>
        [HttpPost("update/{activityId}")]
        public async Task<Result<object>> UpdateActivityStatusAsync(int activityId)
        {
            try
            {
                var result = await _activityStatusService.UpdateActivityStatusAsync(activityId);

                var response = new
                {
                    result.Success,
                    result.Message,
                    result.ProcessedActivities,
                    result.UpdatedActivities,
                    result.FailedActivities,
                    UpdateTime = DateTime.Now
                };

                if (result.Success)
                {
                    return Success((object)response, "活动状态更新成功");
                }
                else
                {
                    return Success((object)response, result.Message);
                }
            }
            catch (Exception ex)
            {
                return Fail<object>($"更新活动状态失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 手动停止活动
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="reason">停止原因</param>
        /// <returns>停止结果</returns>
        [HttpPost("stop/{activityId}")]
        public async Task<Result<string>> StopActivityAsync(int activityId, [FromQuery] string reason = "")
        {
            try
            {
                var success = await _activityStatusService.StopActivityAsync(activityId, reason);

                if (success)
                {
                    return Success("活动已成功停止", "停止成功");
                }
                else
                {
                    return Fail<string>("活动不存在或停止失败");
                }
            }
            catch (Exception ex)
            {
                return Fail<string>($"停止活动失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 手动启动活动
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>启动结果</returns>
        [HttpPost("start/{activityId}")]
        public async Task<Result<string>> StartActivityAsync(int activityId)
        {
            try
            {
                var success = await _activityStatusService.StartActivityAsync(activityId);

                if (success)
                {
                    return Success("活动已成功启动", "启动成功");
                }
                else
                {
                    return Fail<string>("活动不存在、不满足启动条件或启动失败");
                }
            }
            catch (Exception ex)
            {
                return Fail<string>($"启动活动失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 重置活动每日奖励份数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>重置结果</returns>
        [HttpPost("reset-daily-rewards/{activityId}")]
        public async Task<Result<string>> ResetDailyRewardQuantityAsync(int activityId)
        {
            try
            {
                var resetCount = await _activityStatusService.ResetDailyRewardQuantityAsync(activityId);
                return Success($"成功重置 {resetCount} 个奖励的每日份数", "重置成功");
            }
            catch (Exception ex)
            {
                return Fail<string>($"重置每日奖励份数失败：{ex.Message}");
            }
        }
    }
}
