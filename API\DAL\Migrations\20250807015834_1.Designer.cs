﻿// <auto-generated />
using System;
using DAL;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DAL.Migrations
{
    [DbContext(typeof(MyContext))]
    [Migration("20250807015834_1")]
    partial class _1
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.Activity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ActivityName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("activity_name")
                        .HasComment("活动名称");

                    b.Property<int>("ActivityType")
                        .HasColumnType("int")
                        .HasColumnName("activity_type")
                        .HasComment("活动类型");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorGameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("creator_game_user_id")
                        .HasComment("创建者游戏用户ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("CreatorNickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("creator_nickname")
                        .HasComment("创建者昵称");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("end_time")
                        .HasComment("结束时间");

                    b.Property<decimal>("RemainingTongbao")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("remaining_tongbao")
                        .HasComment("剩余通宝");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("start_time")
                        .HasComment("开始时间");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("活动状态");

                    b.Property<decimal>("TotalTongbao")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("total_tongbao")
                        .HasComment("投入的总通宝");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("activities");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.ActivityReward", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("活动ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<int>("DailyQuantity")
                        .HasColumnType("int")
                        .HasColumnName("daily_quantity")
                        .HasComment("每日份数");

                    b.Property<DateTime?>("LastResetTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_reset_time")
                        .HasComment("上次重置时间");

                    b.Property<string>("PhysicalItem")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("physical_item")
                        .HasComment("实物描述");

                    b.Property<decimal>("Probability")
                        .HasColumnType("decimal(8,6)")
                        .HasColumnName("probability")
                        .HasComment("中奖概率");

                    b.Property<int>("RemainingQuantity")
                        .HasColumnType("int")
                        .HasColumnName("remaining_quantity")
                        .HasComment("剩余份数");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<string>("RewardName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("reward_name")
                        .HasComment("奖励名称");

                    b.Property<int>("RewardType")
                        .HasColumnType("int")
                        .HasColumnName("reward_type")
                        .HasComment("奖励类型：通宝/实物");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order")
                        .HasComment("排序");

                    b.Property<decimal>("TongbaoAmount")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("tongbao_amount")
                        .HasComment("通宝数量");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<int>("Weight")
                        .HasColumnType("int")
                        .HasColumnName("weight")
                        .HasComment("权重");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.ToTable("activity_rewards");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.ActivityTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("活动ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<int>("RefreshType")
                        .HasColumnType("int")
                        .HasColumnName("refresh_type")
                        .HasComment("刷新类型");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("RewardChances")
                        .HasColumnType("int")
                        .HasColumnName("reward_chances")
                        .HasComment("奖励次数");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order")
                        .HasComment("排序");

                    b.Property<int>("TargetValue")
                        .HasColumnType("int")
                        .HasColumnName("target_value")
                        .HasComment("目标数值");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("task_name")
                        .HasComment("任务名称");

                    b.Property<int>("TaskType")
                        .HasColumnType("int")
                        .HasColumnName("task_type")
                        .HasComment("任务类型");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.ToTable("activity_tasks");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.DrawRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("活动ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<DateTime>("DrawTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("draw_time")
                        .HasComment("抽奖时间");

                    b.Property<string>("PlayerGameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("player_game_user_id")
                        .HasComment("玩家游戏用户ID");

                    b.Property<string>("PlayerNickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("player_nickname")
                        .HasComment("玩家昵称");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("RewardId")
                        .HasColumnType("int")
                        .HasColumnName("reward_id")
                        .HasComment("中奖奖励ID");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.HasIndex("RewardId");

                    b.ToTable("draw_records");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.GameDataSync", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClubId")
                        .HasColumnType("int")
                        .HasColumnName("club_id")
                        .HasComment("联盟ID");

                    b.Property<string>("ClubName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("club_name")
                        .HasComment("联盟名称");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<int>("GameCount")
                        .HasColumnType("int")
                        .HasColumnName("game_count")
                        .HasComment("游戏局数");

                    b.Property<int>("GameDuration")
                        .HasColumnType("int")
                        .HasColumnName("game_duration")
                        .HasComment("游戏时长（秒）");

                    b.Property<string>("Nickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("nickname")
                        .HasComment("昵称");

                    b.Property<int>("OnlineDuration")
                        .HasColumnType("int")
                        .HasColumnName("online_duration")
                        .HasComment("在线时长（秒）");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<decimal>("ServiceFee")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("service_fee")
                        .HasComment("服务费");

                    b.Property<DateTime>("SyncDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("sync_date")
                        .HasComment("同步日期");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Id");

                    b.ToTable("game_data_sync");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.MarqueeMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("活动ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<DateTime?>("DisplayTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("display_time")
                        .HasComment("显示时间");

                    b.Property<bool>("IsDisplayed")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_displayed")
                        .HasComment("是否已显示");

                    b.Property<DateTime?>("LastPlayedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_played_at")
                        .HasComment("最后播放时间");

                    b.Property<string>("MessageContent")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("message_content")
                        .HasComment("消息内容");

                    b.Property<int>("PlayCount")
                        .HasColumnType("int")
                        .HasColumnName("play_count")
                        .HasComment("播放次数");

                    b.Property<string>("PlayerGameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("player_game_user_id")
                        .HasComment("玩家游戏用户ID");

                    b.Property<string>("PlayerNickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("player_nickname")
                        .HasComment("玩家昵称");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<string>("RewardName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("reward_name")
                        .HasComment("奖励名称");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.ToTable("marquee_messages");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.PlayerDrawChance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("活动ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("PlayerGameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("player_game_user_id")
                        .HasComment("玩家游戏用户ID");

                    b.Property<string>("PlayerNickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("player_nickname")
                        .HasComment("玩家昵称");

                    b.Property<int>("RemainingChances")
                        .HasColumnType("int")
                        .HasColumnName("remaining_chances")
                        .HasComment("剩余次数");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("TotalChances")
                        .HasColumnType("int")
                        .HasColumnName("total_chances")
                        .HasComment("总次数");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<int>("UsedChances")
                        .HasColumnType("int")
                        .HasColumnName("used_chances")
                        .HasComment("已使用次数");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.ToTable("player_draw_chances");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.PlayerTaskProgress", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("活动ID");

                    b.Property<int>("CompletedTimes")
                        .HasColumnType("int")
                        .HasColumnName("completed_times")
                        .HasComment("完成次数");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<int>("CurrentProgress")
                        .HasColumnType("int")
                        .HasColumnName("current_progress")
                        .HasComment("当前进度");

                    b.Property<DateTime?>("LastCompletedTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_completed_time")
                        .HasComment("上次完成时间");

                    b.Property<DateTime?>("LastRefreshTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_refresh_time")
                        .HasComment("上次刷新时间");

                    b.Property<string>("PlayerGameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("player_game_user_id")
                        .HasComment("玩家游戏用户ID");

                    b.Property<string>("PlayerNickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("player_nickname")
                        .HasComment("玩家昵称");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("RewardChancesEarned")
                        .HasColumnType("int")
                        .HasColumnName("reward_chances_earned")
                        .HasComment("获得的抽奖次数");

                    b.Property<bool>("RewardClaimed")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("reward_claimed")
                        .HasComment("奖励是否已领取");

                    b.Property<DateTime?>("RewardClaimedTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("reward_claimed_time")
                        .HasComment("奖励领取时间");

                    b.Property<int>("TaskId")
                        .HasColumnType("int")
                        .HasColumnName("task_id")
                        .HasComment("任务ID");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("valid_from")
                        .HasComment("任务有效期开始时间");

                    b.Property<DateTime>("ValidTo")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("valid_to")
                        .HasComment("任务有效期结束时间");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.HasIndex("TaskId");

                    b.ToTable("player_task_progress");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.PrizeRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("活动ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("PhysicalItem")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("physical_item")
                        .HasComment("实物描述");

                    b.Property<string>("PlayerGameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("player_game_user_id")
                        .HasComment("玩家游戏用户ID");

                    b.Property<string>("PlayerNickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("player_nickname")
                        .HasComment("玩家昵称");

                    b.Property<string>("ProcessNote")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("process_note")
                        .HasComment("处理备注");

                    b.Property<DateTime?>("ProcessTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("process_time")
                        .HasComment("处理时间");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("RewardId")
                        .HasColumnType("int")
                        .HasColumnName("reward_id")
                        .HasComment("奖励ID");

                    b.Property<string>("RewardName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("reward_name")
                        .HasComment("奖励名称");

                    b.Property<int>("RewardType")
                        .HasColumnType("int")
                        .HasColumnName("reward_type")
                        .HasComment("奖励类型");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("发放状态");

                    b.Property<decimal>("TongbaoAmount")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("tongbao_amount")
                        .HasComment("通宝数量");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.HasIndex("RewardId");

                    b.ToTable("prize_records");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.SystemConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConfigKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("config_key")
                        .HasComment("配置键");

                    b.Property<string>("ConfigValue")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("config_value")
                        .HasComment("配置值");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("description")
                        .HasComment("配置描述");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("system_config");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.TongbaoTransaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ActivityId")
                        .HasColumnType("int")
                        .HasColumnName("activity_id")
                        .HasComment("关联活动ID");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("amount")
                        .HasComment("金额（正数为收入，负数为支出）");

                    b.Property<decimal>("BalanceAfter")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("balance_after")
                        .HasComment("交易后余额");

                    b.Property<decimal>("BalanceBefore")
                        .HasColumnType("decimal(15,2)")
                        .HasColumnName("balance_before")
                        .HasComment("交易前余额");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("description")
                        .HasComment("描述");

                    b.Property<string>("GameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("game_user_id")
                        .HasComment("游戏用户ID");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("TransactionType")
                        .HasColumnType("int")
                        .HasColumnName("transaction_type")
                        .HasComment("交易类型");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserNickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("user_nickname")
                        .HasComment("用户昵称");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.ToTable("tongbao_transactions");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.UserCache", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasComment("ID");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("GameUserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("game_user_id")
                        .HasComment("游戏系统用户ID");

                    b.Property<DateTime>("LastUpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_update_time")
                        .HasComment("最后更新时间");

                    b.Property<string>("Nickname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("nickname")
                        .HasComment("昵称");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("user_cache");
                });

            modelBuilder.Entity("Entity.Entitys.SmsVerificationCode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BusinessType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("varchar(10)");

                    b.Property<string>("DeviceId")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("ExpireTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("SendTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("UsedTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.ToTable("sms_verification_code");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysButton", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasComment("权限描述");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("按钮名称");

                    b.Property<string>("PermissionCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("权限编码");

                    b.HasKey("Id");

                    b.ToTable("sys_button");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysDictionary", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("DictItemCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DictItemName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("DictTypeCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DictTypeName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ExtendField1")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ExtendField2")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ParentId")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("sys_dictionary");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysLog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("varchar(255)")
                        .HasComment("日志ID");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("Exception")
                        .HasColumnType("longtext")
                        .HasComment("异常信息");

                    b.Property<string>("Ip")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("IP地址");

                    b.Property<string>("LogLevel")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("日志级别");

                    b.Property<string>("LogType")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("日志类型");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("日志内容");

                    b.Property<string>("Method")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasComment("请求方法");

                    b.Property<string>("Operation")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("操作类型");

                    b.Property<string>("Params")
                        .HasColumnType("longtext")
                        .HasComment("请求参数");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(500)")
                        .HasComment("请求路径");

                    b.Property<long>("Time")
                        .HasColumnType("bigint")
                        .HasComment("执行时长(毫秒)");

                    b.Property<string>("UserId")
                        .HasColumnType("varchar(100)")
                        .HasComment("用户ID");

                    b.Property<string>("Username")
                        .HasColumnType("varchar(50)")
                        .HasComment("用户名");

                    b.HasKey("Id");

                    b.ToTable("sys_log");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysMenu", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<string>("Component")
                        .HasColumnType("varchar(200)")
                        .HasComment("组件路径");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Icon")
                        .HasColumnType("varchar(500)")
                        .HasComment("图标");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasComment("菜单名称");

                    b.Property<int>("OrderNum")
                        .HasColumnType("int")
                        .HasComment("排序");

                    b.Property<string>("ParentId")
                        .HasColumnType("varchar(32)")
                        .HasComment("父级ID");

                    b.Property<string>("Path")
                        .HasColumnType("varchar(200)")
                        .HasComment("菜单路径");

                    b.Property<string>("Perms")
                        .HasColumnType("varchar(100)")
                        .HasComment("权限标识");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint")
                        .HasComment("状态（0：禁用，1：启用）");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint")
                        .HasComment("类型（0：目录，1：菜单，2：按钮）");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<sbyte>("Visible")
                        .HasColumnType("tinyint")
                        .HasComment("是否可见（0：隐藏，1：显示）");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("sys_menu");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysPermission", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("ObjectId")
                        .IsRequired()
                        .HasColumnType("varchar(32)")
                        .HasComment("对象ID（菜单ID或按钮ID）");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<string>("SubjectId")
                        .IsRequired()
                        .HasColumnType("varchar(32)")
                        .HasComment("主体ID（用户ID或角色ID）");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("sys_permission");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysRole", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("角色编码");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<sbyte>("DataScope")
                        .HasColumnType("tinyint")
                        .HasComment("数据范围（1：全部数据权限 2：自定义数据权限 3：本部门数据权限 4：本部门及以下数据权限）");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasComment("角色名称");

                    b.Property<int>("OrderNum")
                        .HasColumnType("int")
                        .HasComment("排序号");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint")
                        .HasComment("状态（0：禁用 1：正常）");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.HasKey("Id");

                    b.ToTable("sys_role");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysUser", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("varchar(255)")
                        .HasComment("用户ID");

                    b.Property<string>("Avatar")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasComment("头像");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("电子邮箱");

                    b.Property<string>("LastLoginIp")
                        .HasColumnType("varchar(50)")
                        .HasComment("最后登录IP");

                    b.Property<DateTime?>("LastLoginTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("最后登录时间");

                    b.Property<string>("Mobile")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasComment("手机号码");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("密码");

                    b.Property<string>("RealName")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("真实姓名");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint")
                        .HasComment("用户状态");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasComment("用户名");

                    b.HasKey("UserId");

                    b.ToTable("sys_user");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysUserRole", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("主键ID (32位GUID字符串)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("创建时间");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("创建人ID");

                    b.Property<string>("CreatorName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("创建人");

                    b.Property<string>("Remark")
                        .HasColumnType("longtext")
                        .HasComment("备注");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("varchar(32)")
                        .HasComment("角色ID");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("更新时间");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("更新人ID");

                    b.Property<string>("UpdaterName")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasComment("更新人");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("varchar(32)")
                        .HasComment("用户ID");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.ToTable("sys_user_role");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.ActivityReward", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany("Rewards")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.ActivityTask", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany("Tasks")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.DrawRecord", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Entity.Entitys.ActivityEntity.ActivityReward", "Reward")
                        .WithMany()
                        .HasForeignKey("RewardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("Reward");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.MarqueeMessage", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.PlayerDrawChance", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.PlayerTaskProgress", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Entity.Entitys.ActivityEntity.ActivityTask", "Task")
                        .WithMany("PlayerProgresses")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.PrizeRecord", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Entity.Entitys.ActivityEntity.ActivityReward", "Reward")
                        .WithMany()
                        .HasForeignKey("RewardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("Reward");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.TongbaoTransaction", b =>
                {
                    b.HasOne("Entity.Entitys.ActivityEntity.Activity", "Activity")
                        .WithMany()
                        .HasForeignKey("ActivityId");

                    b.Navigation("Activity");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysUserRole", b =>
                {
                    b.HasOne("Entity.Entitys.SysEntity.SysRole", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Entity.Entitys.SysEntity.SysUser", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.Activity", b =>
                {
                    b.Navigation("Rewards");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("Entity.Entitys.ActivityEntity.ActivityTask", b =>
                {
                    b.Navigation("PlayerProgresses");
                });

            modelBuilder.Entity("Entity.Entitys.SysEntity.SysUser", b =>
                {
                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
