using BLL.ActivityService;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.ActivityControllers
{
    /// <summary>
    /// 抽奖系统控制器
    /// </summary>
    [ApiController]
    [Route("api/activities")]
    public class DrawController(DrawService drawService) : BaseController
    {
        private readonly DrawService _drawService = drawService;

        /// <summary>
        /// 获取抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>抽奖次数信息</returns>
        [HttpGet("{activityId}/draw-chances")]
        public async Task<Result<DrawChancesResponseDto>> GetDrawChancesAsync(int activityId)
        {
            var gameUserId = GetGameUserIdFromHeader();
            if (string.IsNullOrEmpty(gameUserId))
            {
                return Fail<DrawChancesResponseDto>("未授权：缺少用户身份信息", 401);
            }

            var result = await _drawService.GetDrawChancesAsync(activityId, gameUserId);
            return Success(result, "获取抽奖次数成功");
        }

        /// <summary>
        /// 执行抽奖
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>抽奖结果</returns>
        [HttpPost("{activityId}/draw")]
        public async Task<Result<DrawResultResponseDto>> DrawAsync(int activityId)
        {
            var gameUserId = GetGameUserIdFromHeader();
            if (string.IsNullOrEmpty(gameUserId))
            {
                return Fail<DrawResultResponseDto>("未授权：缺少用户身份信息", 401);
            }

            // 这里应该从游戏系统获取用户昵称，暂时使用默认值
            var playerNickname = "玩家" + gameUserId;

            var result = await _drawService.DrawAsync(activityId, gameUserId, playerNickname);
            return Success(result, "抽奖成功");
        }

        /// <summary>
        /// 获取抽奖记录
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="type">记录类型</param>
        /// <param name="limit">数量限制</param>
        /// <returns>抽奖记录</returns>
        [HttpGet("{activityId}/draw-records")]
        public object GetDrawRecords(
            int activityId,
            [FromQuery] string type = "all",
            [FromQuery] int limit = 50)
        {
            var gameUserId = GetGameUserIdFromHeader();
            if (string.IsNullOrEmpty(gameUserId))
            {
                return Fail<object>("未授权：缺少用户身份信息", 401);
            }

            // 这里需要实现获取抽奖记录的逻辑
            // 暂时返回空列表
            var result = new
            {
                records = new List<DrawRecordResponseDto>(),
                total = 0
            };

            return Success(result, "获取抽奖记录成功");
        }

        /// <summary>
        /// 增加抽奖次数（内部接口，由任务系统调用）
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="gameUserId">游戏用户ID</param>
        /// <param name="playerNickname">玩家昵称</param>
        /// <param name="chances">增加的次数</param>
        /// <returns>更新后的抽奖次数</returns>
        [HttpPost("{activityId}/add-chances")]
        public async Task<Result<DrawChancesResponseDto>> AddDrawChancesAsync(
            int activityId,
            [FromQuery] string gameUserId,
            [FromQuery] string playerNickname,
            [FromQuery] int chances)
        {
            if (string.IsNullOrEmpty(gameUserId) || string.IsNullOrEmpty(playerNickname) || chances <= 0)
            {
                return Fail<DrawChancesResponseDto>("参数错误", 400);
            }

            var result = await _drawService.AddDrawChancesAsync(activityId, gameUserId, playerNickname, chances);
            return Success(result, "增加抽奖次数成功");
        }

        /// <summary>
        /// 从请求头获取游戏用户ID
        /// </summary>
        /// <returns>游戏用户ID</returns>
        private string? GetGameUserIdFromHeader()
        {
            return Request.Headers["X-Game-User-Id"].FirstOrDefault();
        }
    }
}
