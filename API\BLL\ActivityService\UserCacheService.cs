using Common.Autofac;
using DAL.ActivityDAL;
using Entity.Dto;

namespace BLL.ActivityService
{
    /// <summary>
    /// 用户缓存服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserCacheService(UserCacheDAL userCacheDAL)
    {
        private readonly UserCacheDAL _userCacheDAL = userCacheDAL;

        /// <summary>
        /// 缓存用户昵称
        /// </summary>
        /// <param name="request">缓存请求</param>
        /// <returns>缓存结果</returns>
        public async Task<UserCacheResponseDto> CacheUserNicknameAsync(CacheUserNicknameDto request)
        {
            if (string.IsNullOrWhiteSpace(request.GameUserId))
                throw new ArgumentException("游戏用户ID不能为空");

            if (string.IsNullOrWhiteSpace(request.Nickname))
                throw new ArgumentException("昵称不能为空");

            var userCache = await _userCacheDAL.CacheOrUpdateNicknameAsync(request.GameUserId, request.Nickname);

            return new UserCacheResponseDto
            {
                GameUserId = userCache.GameUserId,
                Nickname = userCache.Nickname
            };
        }

        /// <summary>
        /// 获取用户昵称
        /// </summary>
        /// <param name="gameUserId">游戏用户ID</param>
        /// <returns>用户昵称</returns>
        public async Task<string?> GetUserNicknameAsync(string gameUserId)
        {
            if (string.IsNullOrWhiteSpace(gameUserId))
                return null;

            var userCache = await _userCacheDAL.GetByGameUserIdAsync(gameUserId);
            return userCache?.Nickname;
        }

        /// <summary>
        /// 批量获取用户昵称
        /// </summary>
        /// <param name="gameUserIds">游戏用户ID列表</param>
        /// <returns>用户昵称字典</returns>
        public async Task<Dictionary<string, string>> GetUserNicknamesAsync(List<string> gameUserIds)
        {
            if (gameUserIds == null || gameUserIds.Count == 0)
                return [];

            return await _userCacheDAL.GetNicknamesByGameUserIdsAsync(gameUserIds);
        }

        /// <summary>
        /// 确保用户昵称已缓存
        /// </summary>
        /// <param name="gameUserId">游戏用户ID</param>
        /// <param name="nickname">昵称</param>
        /// <returns>缓存的昵称</returns>
        public async Task<string> EnsureUserNicknameCachedAsync(string gameUserId, string nickname)
        {
            if (string.IsNullOrWhiteSpace(gameUserId))
                throw new ArgumentException("游戏用户ID不能为空");

            if (string.IsNullOrWhiteSpace(nickname))
                throw new ArgumentException("昵称不能为空");

            var existingCache = await _userCacheDAL.GetByGameUserIdAsync(gameUserId);

            if (existingCache == null)
            {
                // 如果缓存不存在，创建新缓存
                await _userCacheDAL.CacheOrUpdateNicknameAsync(gameUserId, nickname);
                return nickname;
            }

            // 如果昵称不同，更新缓存
            if (existingCache.Nickname != nickname)
            {
                await _userCacheDAL.CacheOrUpdateNicknameAsync(gameUserId, nickname);
                return nickname;
            }

            return existingCache.Nickname;
        }
    }
}
