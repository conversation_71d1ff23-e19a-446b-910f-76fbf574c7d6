using Common.Autofac;
using DAL.Databases;
using Entity;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using System.Data;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 活动数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class ActivityDAL(MyContext context) : BaseQueryDLL<ActivityInfo, ActivityDAL.ActivityQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 活动查询条件模型类
        /// </summary>
        public class ActivityQuery : PageQueryEntity
        {
            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? Id { get; set; }

            /// <summary>
            /// 创建者游戏用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? CreatorGameUserId { get; set; }

            /// <summary>
            /// 活动名称
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? ActivityName { get; set; }

            /// <summary>
            /// 活动状态
            /// </summary>
            [Query(QueryOperator.等于)]
            public ActivityStatus? Status { get; set; }

            /// <summary>
            /// 活动类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public ActivityType? ActivityType { get; set; }
        }

        /// <summary>
        /// 获取活动详情（包含奖励和任务）
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>活动详情</returns>
        public async Task<ActivityInfo?> GetActivityWithDetailsAsync(int activityId)
        {
            return await _context.Activities
                .FirstOrDefaultAsync(x => x.Id == activityId);
        }

        /// <summary>
        /// 获取用户创建的活动列表
        /// </summary>
        /// <param name="creatorGameUserId">创建者游戏用户ID</param>
        /// <param name="status">活动状态</param>
        /// <param name="page">页码</param>
        /// <param name="limit">每页数量</param>
        /// <returns>活动列表</returns>
        public async Task<(List<ActivityInfo> activities, int total)> GetUserActivitiesAsync(
            string creatorGameUserId,
            ActivityStatus? status = null,
            int page = 1,
            int limit = 10)
        {
            var query = _context.Activities
                .Where(x => x.CreatorGameUserId == creatorGameUserId);

            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            var total = await query.CountAsync();
            var activities = await query
                .OrderByDescending(x => x.CreateTime)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync();

            return (activities, total);
        }

        /// <summary>
        /// 获取所有活动列表（仅盟主可用）
        /// </summary>
        /// <param name="status">活动状态</param>
        /// <param name="page">页码</param>
        /// <param name="limit">每页数量</param>
        /// <returns>活动列表</returns>
        public async Task<(List<ActivityInfo> activities, int total)> GetAllActivitiesAsync(
            ActivityStatus? status = null,
            int page = 1,
            int limit = 10)
        {
            var query = _context.Activities.AsQueryable();

            if (status.HasValue)
            {
                query = query.Where(x => x.Status == status.Value);
            }

            var total = await query.CountAsync();
            var activities = await query
                .OrderByDescending(x => x.CreateTime)
                .Skip((page - 1) * limit)
                .Take(limit)
                .ToListAsync();

            return (activities, total);
        }

        /// <summary>
        /// 更新活动状态
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="status">新状态</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateActivityStatusAsync(int activityId, ActivityStatus status)
        {
            var activity = await _context.Activities.FindAsync(activityId);
            if (activity == null)
                return false;

            activity.Status = status;
            activity.UpdateTime = DateTime.Now;

            _context.Activities.Update(activity);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 更新活动剩余通宝
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="amount">扣减金额</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateRemainingTongbaoAsync(int activityId, decimal amount)
        {
            var activity = await _context.Activities.FindAsync(activityId);
            if (activity == null || activity.RemainingTongbao < amount)
                return false;

            activity.RemainingTongbao -= amount;
            activity.UpdateTime = DateTime.Now;

            _context.Activities.Update(activity);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取正在进行的活动列表
        /// </summary>
        /// <returns>正在进行的活动列表</returns>
        public async Task<List<ActivityInfo>> GetRunningActivitiesAsync()
        {
            var now = DateTime.Now;
            return await _context.Activities
                .Where(x => x.Status == ActivityStatus.running &&
                           x.StartTime <= now &&
                           x.EndTime >= now)
                .ToListAsync();
        }

        /// <summary>
        /// 获取需要更新状态的活动列表
        /// </summary>
        /// <returns>需要更新状态的活动列表</returns>
        public async Task<List<ActivityInfo>> GetActivitiesNeedStatusUpdateAsync()
        {
            var now = DateTime.Now;
            return await _context.Activities
                .Where(x => (x.Status == ActivityStatus.pending && x.StartTime <= now) ||
                           (x.Status == ActivityStatus.running && x.EndTime < now))
                .ToListAsync();
        }

        /// <summary>
        /// 创建活动
        /// </summary>
        /// <param name="activity">活动实体</param>
        /// <returns>创建的活动</returns>
        public async Task<ActivityInfo> CreateAsync(ActivityInfo activity)
        {
            _context.Activities.Add(activity);
            await _context.SaveChangesAsync();
            return activity;
        }

        /// <summary>
        /// 根据ID获取活动
        /// </summary>
        /// <param name="id">活动ID</param>
        /// <returns>活动实体</returns>
        public async Task<ActivityInfo?> GetByIdAsync(int id)
        {
            return await _context.Activities.FindAsync(id);
        }

        #region 神秘盒子活动存储过程

        /// <summary>
        /// 判断用户是否为下级用户
        /// 调用存储过程：api_isDownUser
        /// </summary>
        /// <param name="clubId">活动创建者所属俱乐部ID</param>
        /// <param name="creatorId">活动创建者ID</param>
        /// <param name="targetUserId">要验证的下级ID</param>
        /// <returns>返回判断结果</returns>
        public async Task<int> IsDownUserAsync(int clubId, int creatorId, int targetUserId)
        {
            try
            {
                var connection = _context.Database.GetDbConnection();
                using var command = connection.CreateCommand();

                command.CommandText = "api_isDownUser";
                command.CommandType = System.Data.CommandType.StoredProcedure;

                command.Parameters.Add(new MySqlParameter("@i_hall_id", clubId));
                command.Parameters.Add(new MySqlParameter("@i_manager", creatorId));
                command.Parameters.Add(new MySqlParameter("@i_userid", targetUserId));

                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                var result = await command.ExecuteScalarAsync();
                var count = Convert.ToInt32(result);

                // 添加调试日志
                Console.WriteLine($"api_isDownUser调用参数: clubId={clubId}, creatorId={creatorId}, targetUserId={targetUserId}, 返回值={count}");

                return count;
            }
            catch (Exception ex)
            {
                throw new Exception($"调用存储过程 api_isDownUser 失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取今日用户游戏数据
        /// 调用存储过程：api_getTodayUsersGameData
        /// </summary>
        /// <param name="clubId">活动创建者所属俱乐部ID</param>
        /// <param name="creatorId">活动创建者ID</param>
        /// <returns>返回今日游戏数据列表</returns>
        public async Task<List<TodayGameDataResult>> GetTodayUsersGameDataAsync(int clubId, int creatorId)
        {
            try
            {
                var results = new List<TodayGameDataResult>();
                var connection = _context.Database.GetDbConnection();

                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                using var command = connection.CreateCommand();
                command.CommandText = "api_getTodayUsersGameData";
                command.CommandType = System.Data.CommandType.StoredProcedure;

                command.Parameters.Add(new MySqlParameter("@i_hall_id", clubId));
                command.Parameters.Add(new MySqlParameter("@i_hall_userid", creatorId));

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var result = new TodayGameDataResult
                    {
                        Date = Convert.ToDateTime(reader["日期"]),
                        UserId = Convert.ToInt32(reader["用户ID"]),
                        Nickname = Convert.ToString(reader["昵称"]) ?? string.Empty,
                        ClubId = Convert.ToInt32(reader["联盟ID"]),
                        ClubName = Convert.ToString(reader["联盟名称"]) ?? string.Empty,
                        GameCount = Convert.ToInt32(reader["游戏局数"]),
                        ServiceFee = Convert.ToDecimal(reader["服务费"]),
                        OnlineDuration = Convert.ToInt32(reader["在线时长"]),
                        GameDuration = Convert.ToInt32(reader["游戏时长"])
                    };
                    results.Add(result);
                }

                return results;
            }
            catch (Exception ex)
            {
                throw new Exception($"调用存储过程 api_getTodayUsersGameData 失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新用户通宝
        /// 调用存储过程：api_updateUserPoint
        /// </summary>
        /// <param name="clubId">活动创建者所属俱乐部ID</param>
        /// <param name="creatorId">活动创建者ID</param>
        /// <param name="operationType">操作类型：1-从用户身上扣除通宝注入奖池，2-从奖池中扣除通宝</param>
        /// <param name="targetUserId">被操作者ID</param>
        /// <param name="pointAmount">本次操作的通宝数量</param>
        /// <returns>返回操作结果</returns>
        public async Task<List<UpdatePointResult>> UpdateUserPointAsync(int clubId, int creatorId, int operationType, int targetUserId, int pointAmount)
        {
            try
            {
                var results = new List<UpdatePointResult>();
                var connection = _context.Database.GetDbConnection();

                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                using var command = connection.CreateCommand();
                command.CommandText = "api_updateUserPoint";
                command.CommandType = System.Data.CommandType.StoredProcedure;

                command.Parameters.Add(new MySqlParameter("@i_hall_id", clubId));
                command.Parameters.Add(new MySqlParameter("@i_owner", creatorId));
                command.Parameters.Add(new MySqlParameter("@i_operate_type", operationType));
                command.Parameters.Add(new MySqlParameter("@i_userid", targetUserId));
                command.Parameters.Add(new MySqlParameter("@i_num", pointAmount));

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var result = new UpdatePointResult
                    {
                        Date = reader.GetDateTime("date"),
                        UserId = reader.GetInt32("user_id"),
                        Nickname = reader.GetString("nickname"),
                        ClubId = reader.GetInt32("club_id"),
                        ClubName = reader.GetString("club_name"),
                        GameCount = reader.GetInt32("game_count"),
                        ServiceFee = reader.GetDecimal("service_fee"),
                        OnlineDuration = TimeSpan.FromSeconds(reader.GetInt32("online_duration")),
                        GameDuration = TimeSpan.FromSeconds(reader.GetInt32("game_duration"))
                    };
                    results.Add(result);
                }

                return results;
            }
            catch (Exception ex)
            {
                throw new Exception($"调用存储过程 api_updateUserPoint 失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取所有进行中活动的创建者ID列表（去重）
        /// </summary>
        /// <returns>创建者ID和俱乐部ID的列表</returns>
        public async Task<List<(int CreatorId, int ClubId)>> GetActiveActivityCreatorsAsync()
        {
            try
            {
                var now = DateTime.Now;
                var creators = await _context.Activities
                    .Where(a => a.Status == ActivityStatus.running &&
                               a.StartTime <= now &&
                               a.EndTime >= now)
                    .Select(a => new
                    {
                        CreatorId = Convert.ToInt32(a.CreatorGameUserId),
                        ClubId = 0 // 需要从其他地方获取ClubId，暂时设为0
                    })
                    .Distinct()
                    .ToListAsync();

                return [.. creators.Select(c => (c.CreatorId, c.ClubId))];
            }
            catch (Exception ex)
            {
                throw new Exception($"获取活动创建者列表失败: {ex.Message}", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// 今日游戏数据结果模型
    /// </summary>
    public class TodayGameDataResult
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string Nickname { get; set; } = string.Empty;

        /// <summary>
        /// 联盟ID
        /// </summary>
        public int ClubId { get; set; }

        /// <summary>
        /// 联盟名称
        /// </summary>
        public string ClubName { get; set; } = string.Empty;

        /// <summary>
        /// 游戏局数
        /// </summary>
        public int GameCount { get; set; }

        /// <summary>
        /// 服务费
        /// </summary>
        public decimal ServiceFee { get; set; }

        /// <summary>
        /// 在线时长（秒）
        /// </summary>
        public int OnlineDuration { get; set; }

        /// <summary>
        /// 游戏时长（秒）
        /// </summary>
        public int GameDuration { get; set; }
    }

    /// <summary>
    /// 更新通宝操作结果模型
    /// </summary>
    public class UpdatePointResult
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string Nickname { get; set; } = string.Empty;

        /// <summary>
        /// 联盟ID
        /// </summary>
        public int ClubId { get; set; }

        /// <summary>
        /// 联盟名称
        /// </summary>
        public string ClubName { get; set; } = string.Empty;

        /// <summary>
        /// 游戏局数
        /// </summary>
        public int GameCount { get; set; }

        /// <summary>
        /// 服务费
        /// </summary>
        public decimal ServiceFee { get; set; }

        /// <summary>
        /// 在线时长
        /// </summary>
        public TimeSpan OnlineDuration { get; set; }

        /// <summary>
        /// 游戏时长
        /// </summary>
        public TimeSpan GameDuration { get; set; }
    }
}
