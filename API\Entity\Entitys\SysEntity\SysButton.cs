using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 系统按钮实体
    /// </summary>
    [Table("sys_button")]
    public class SysButton
    {
        /// <summary>
        /// 主键ID (32位GUID字符串)
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [MaxLength(32)]
        [Comment("主键ID (32位GUID字符串)")]
        public string Id { get; set; }


        /// <summary>
        /// 按钮名称
        /// </summary>
        [MaxLength(50)]
        [Comment("按钮名称")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 权限编码
        /// </summary>
        [MaxLength(50)]
        [Comment("权限编码")]
        public string PermissionCode { get; set; } = string.Empty;

        /// <summary>
        /// 权限描述
        /// </summary>
        [MaxLength(500)]
        [Comment("权限描述")]
        public string Description { get; set; } = string.Empty;

        public SysButton()
        {
            // 生成32位的GUID（去除连字符'-'）
            Id = Guid.NewGuid().ToString("N");
        }
    }


}