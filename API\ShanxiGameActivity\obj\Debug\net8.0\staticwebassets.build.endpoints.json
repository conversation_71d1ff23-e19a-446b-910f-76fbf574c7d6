{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "build-app.bat", "AssetFile": "build-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "672"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E="}]}, {"Route": "build-app.np9u4khkac.bat", "AssetFile": "build-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "672"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "np9u4khkac"}, {"Name": "integrity", "Value": "sha256-VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E="}, {"Name": "label", "Value": "build-app.bat"}]}, {"Route": "start-app.bat", "AssetFile": "start-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "142"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ="}]}, {"Route": "start-app.mqzi4oyyqd.bat", "AssetFile": "start-app.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "142"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mqzi4oyyqd"}, {"Name": "integrity", "Value": "sha256-bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ="}, {"Name": "label", "Value": "start-app.bat"}]}, {"Route": "start-without-debug.7rvjcizg5g.bat", "AssetFile": "start-without-debug.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "953"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7rvjcizg5g"}, {"Name": "integrity", "Value": "sha256-fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8="}, {"Name": "label", "Value": "start-without-debug.bat"}]}, {"Route": "start-without-debug.bat", "AssetFile": "start-without-debug.bat", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "953"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8=\""}, {"Name": "Last-Modified", "Value": "Wed, 07 May 2025 02:41:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8="}]}, {"Route": "test-mystery-box.html", "AssetFile": "test-mystery-box.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14886"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Yhm3VSs0ghQsYQ4agt2OYiZS1kR/mVQzDlSVfs4SkSk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 07:37:23 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yhm3VSs0ghQsYQ4agt2OYiZS1kR/mVQzDlSVfs4SkSk="}]}, {"Route": "test-mystery-box.q24fjdx0ac.html", "AssetFile": "test-mystery-box.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14886"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Yhm3VSs0ghQsYQ4agt2OYiZS1kR/mVQzDlSVfs4SkSk=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 07:37:23 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q24fjdx0ac"}, {"Name": "integrity", "Value": "sha256-Yhm3VSs0ghQsYQ4agt2OYiZS1kR/mVQzDlSVfs4SkSk="}, {"Name": "label", "Value": "test-mystery-box.html"}]}]}