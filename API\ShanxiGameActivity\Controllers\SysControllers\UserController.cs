using BLL.SysService;
using BLL.SysService.Exports;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;
using static DAL.Databases.EFHelper;
using static DAL.SysDAL.SysUserDAL;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 用户管理控制器
    /// 提供用户的增删改查、角色分配、密码管理等功能
    /// </summary>
    [Permission]
    public class UserController(SysUserService userService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 用户服务接口
        /// </summary>
        private readonly SysUserService _userService = userService;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        #region 基础CRUD操作

        /// <summary>
        /// 创建新用户
        /// </summary>
        /// <param name="input">创建用户请求</param>
        /// <returns>创建结果</returns>
        [FunctionPermission("user:create", "创建新用户")]
        [HttpPost("create")]
        public async Task<Result<string>> CreateAsync(CreateUserDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行业务操作
            var userId = await _userService.CreateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "创建用户",
                BusinessObject = "SysUser",
                ObjectId = userId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功创建了新用户 {input.UserName}，用户ID: {userId}",
                AfterData = new { UserId = userId, input.UserName, input.RealName },
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(userId, "创建成功");
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="input">更新用户请求</param>
        /// <returns>更新结果</returns>
        [FunctionPermission("user:update", "更新用户")]
        [HttpPost("update")]
        public async Task<Result> UpdateAsync([FromBody] UpdateUserDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取更新前的用户信息，用于日志记录
            var beforeUser = await _userService.GetAsync(input.UserId);

            // 执行业务操作
            await _userService.UpdateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "更新用户",
                BusinessObject = "SysUser",
                ObjectId = input.UserId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功更新了用户 {input.UserId} 的信息",
                BeforeData = beforeUser,
                AfterData = input,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("更新成功");
        }

        /// <summary>
        /// 删除指定用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>删除结果</returns>
        [FunctionPermission("user:delete", "删除用户")]
        [HttpPost("delete/{id}")]
        public async Task<Result> DeleteAsync(string id)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取删除前的用户信息，用于日志记录
            var beforeUser = await _userService.GetAsync(id);

            // 执行业务操作
            await _userService.DeleteAsync(id);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "删除用户",
                BusinessObject = "SysUser",
                ObjectId = id,
                DetailedInfo = $"用户 {currentUser.UserName} 成功删除了用户 {beforeUser.UserName}，用户ID: {id}",
                BeforeData = beforeUser,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("删除成功");
        }

        /// <summary>
        /// 获取用户详细信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户详细信息</returns>
        [HttpGet("{id}")]
        public async Task<Result<UserDto>> GetAsync(string id)
        => Success(await _userService.GetAsync(id), "获取成功");

        /// <summary>
        /// 分页查询用户列表
        /// </summary>
        /// <param name="queryable">分页查询请求</param>
        /// <returns>用户列表</returns>

        [HttpGet("query")]
        public async Task<Result<PageEntity<UserDto>>> QueryAsync([FromQuery] UserDALQuery queryable)
        => Success(await _userService.GetPageAsync(queryable), "获取成功");

        #endregion

        #region 密码管理

        /// <summary>
        /// 修改用户密码
        /// </summary>
        /// <param name="input">修改密码请求</param>
        /// <returns>修改结果</returns>
        [HttpPost("change-password")]
        public async Task<Result> ChangePasswordAsync(ChangePasswordDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行业务操作
            await _userService.ChangePasswordAsync(input);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "修改密码",
                BusinessObject = "SysUser",
                ObjectId = input.UserId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功修改了密码",
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("密码修改成功");
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="input">重置密码请求</param>
        /// <returns>重置结果</returns>
        [FunctionPermission("user:reset-password", "重置密码")]
        [HttpPost("reset-password")]
        public async Task<Result> ResetPasswordAsync(ResetPasswordDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取重置密码前的用户信息，用于日志记录
            var targetUser = await _userService.GetAsync(input.UserId);

            // 执行业务操作
            await _userService.ResetPasswordAsync(input);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "重置密码",
                BusinessObject = "SysUser",
                ObjectId = input.UserId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功重置了用户 {targetUser.UserName} 的密码",
                BeforeData = new { UserId = targetUser.Id, targetUser.UserName },
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("密码重置成功");
        }

        #endregion

        #region 角色管理

        /// <summary>
        /// 为用户分配角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="roleIds">角色ID列表</param>
        /// <returns>分配结果</returns>
        [FunctionPermission("user:assign-roles", "分配角色")]
        [HttpPost("{userId}/assign-roles")]
        public async Task<Result> AssignRolesAsync(string userId, [FromBody] List<string> roleIds)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取分配前的角色信息，用于日志记录
            var beforeRoles = await _userService.GetUserRolesAsync(userId);
            var targetUser = await _userService.GetAsync(userId);

            // 执行业务操作
            await _userService.AssignRolesAsync(userId, roleIds, currentUser);

            // 获取分配后的角色信息，用于日志记录
            var afterRoles = await _userService.GetUserRolesAsync(userId);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "分配角色",
                BusinessObject = "SysUser",
                ObjectId = userId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功为用户 {targetUser.UserName} 分配了角色",
                BeforeData = beforeRoles.Select(r => new { r.Id, r.Name }).ToList(),
                AfterData = afterRoles.Select(r => new { r.Id, r.Name }).ToList(),
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("角色分配成功");
        }

        /// <summary>
        /// 获取用户已分配的角色列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户角色列表</returns>
        [HttpGet("{userId}/roles")]
        public async Task<Result<List<SysRole>>> GetUserRolesAsync(string userId)
        => Success(await _userService.GetUserRolesAsync(userId), "获取成功");

        /// <summary>
        /// 导出用户列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <param name="exportRequest">导出配置</param>
        /// <returns>CSV文件</returns>

        [FunctionPermission("user:export", "导出用户列表")]
        [HttpPost("export")]
        public async Task<IActionResult> ExportUsersAsync(
            [FromQuery] UserDALQuery query,
            [FromBody] ExportRequestDto exportRequest)
        {
            query.PageIndex = 1;
            query.PageSize = 1000; // 限制导出的最大数量

            // 使用分页查询方法获取用户
            var pageResult = await _userService.GetPageAsync(query);

            if (pageResult == null || pageResult.List == null || pageResult.List.Count == 0)
                return BadRequestResult("未找到符合条件的用户数据");

            var users = pageResult.List;

            // 如果没有提供导出列配置，则使用默认配置
            if (exportRequest.Columns == null || exportRequest.Columns.Count == 0)
                exportRequest.Columns =
                [
                    new() { Title = "用户名", PropertyName = "Username", Order = 1 },
                    new() { Title = "姓名", PropertyName = "RealName", Order = 2 },
                    new() { Title = "邮箱", PropertyName = "Email", Order = 3 },
                    new() { Title = "手机号", PropertyName = "PhoneNumber", Order = 4 },
                    new() { Title = "状态", PropertyName = "Status", Order = 5 },
                    new() { Title = "角色", PropertyName = "RoleName", Order = 6 },
                    new() { Title = "部门", PropertyName = "DepartmentName", Order = 7 },
                    new() { Title = "最后登录时间", PropertyName = "LastLoginTime", Order = 8, Format = "yyyy-MM-dd HH:mm:ss" },
                    new() { Title = "创建时间", PropertyName = "CreateTime", Order = 9, Format = "yyyy-MM-dd HH:mm:ss" },
                    new() { Title = "修改时间", PropertyName = "UpdateTime", Order = 10, Format = "yyyy-MM-dd HH:mm:ss" }
                ];

            // 使用导出服务导出数据
            var exportService = HttpContext.RequestServices.GetRequiredService<ExportService>();
            var fileContentResult = exportService.ExportToFile(users, exportRequest, "用户列表");

            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "导出用户列表",
                BusinessObject = "SysUser",
                ObjectId = "N/A",
                DetailedInfo = $"用户 {currentUser.UserName} 成功导出了用户列表，共 {users.Count} 条记录",
                BeforeData = query,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return fileContentResult;
        }

        #endregion
    }
}