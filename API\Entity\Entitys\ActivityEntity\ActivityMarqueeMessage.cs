using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 跑马灯消息表
/// </summary>
[Table("activity_marquee_messages")]
public class ActivityMarqueeMessage : BaseEntity_ID
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [Required]
    [Column("activity_id")]
    [Comment("活动ID")]
    public int ActivityId { get; set; }

    /// <summary>
    /// 玩家游戏用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("player_game_user_id")]
    [Comment("玩家游戏用户ID")]
    public string PlayerGameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 玩家昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("player_nickname")]
    [Comment("玩家昵称")]
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 奖励名称
    /// </summary>
    [Required]
    [MaxLength(200)]
    [Column("reward_name")]
    [Comment("奖励名称")]
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 消息内容
    /// </summary>
    [Required]
    [Column("message_content", TypeName = "TEXT")]
    [Comment("消息内容")]
    public string MessageContent { get; set; } = string.Empty;

    /// <summary>
    /// 播放次数
    /// </summary>
    [Column("play_count")]
    [Comment("播放次数")]
    public int PlayCount { get; set; } = 0;

    /// <summary>
    /// 最后播放时间
    /// </summary>
    [Column("last_played_at")]
    [Comment("最后播放时间")]
    public DateTime? LastPlayedAt { get; set; }

    /// <summary>
    /// 是否已显示
    /// </summary>
    [Column("is_displayed")]
    [Comment("是否已显示")]
    public bool IsDisplayed { get; set; } = false;

    /// <summary>
    /// 显示时间
    /// </summary>
    [Column("display_time")]
    [Comment("显示时间")]
    public DateTime? DisplayTime { get; set; }

}
