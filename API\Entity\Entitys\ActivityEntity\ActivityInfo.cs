using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 活动基本信息表
/// </summary>
[Table("activity_activities")]
public class ActivityInfo : BaseEntity_ID
{
    /// <summary>
    /// 创建者游戏用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("creator_game_user_id")]
    [Comment("创建者游戏用户ID")]
    public string CreatorGameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 创建者昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("creator_nickname")]
    [Comment("创建者昵称")]
    public string CreatorNickname { get; set; } = string.Empty;

    /// <summary>
    /// 活动名称
    /// </summary>
    [Required]
    [MaxLength(200)]
    [Column("activity_name")]
    [Comment("活动名称")]
    public string ActivityName { get; set; } = string.Empty;

    /// <summary>
    /// 活动类型
    /// </summary>
    [Required]
    [Column("activity_type")]
    [Comment("活动类型")]
    public ActivityType ActivityType { get; set; } = ActivityType.blind_box;

    /// <summary>
    /// 投入的总通宝
    /// </summary>
    [Required]
    [Column("total_tongbao", TypeName = "decimal(15,2)")]
    [Comment("投入的总通宝")]
    public decimal TotalTongbao { get; set; }

    /// <summary>
    /// 剩余通宝
    /// </summary>
    [Required]
    [Column("remaining_tongbao", TypeName = "decimal(15,2)")]
    [Comment("剩余通宝")]
    public decimal RemainingTongbao { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [Required]
    [Column("start_time")]
    [Comment("开始时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [Required]
    [Column("end_time")]
    [Comment("结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 活动状态
    /// </summary>
    [Required]
    [Column("status")]
    [Comment("活动状态")]
    public ActivityStatus Status { get; set; } = ActivityStatus.pending;

}
