using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 用户缓存数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserCacheDAL(MyContext context) : BaseQueryDLL<ActivityUserCache, UserCacheDAL.UserCacheQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 用户缓存查询条件模型类
        /// </summary>
        public class UserCacheQuery : PageQueryEntity
        {
            /// <summary>
            /// 游戏用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? GameUserId { get; set; }

            /// <summary>
            /// 昵称
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Nickname { get; set; }
        }

        /// <summary>
        /// 根据游戏用户ID获取用户缓存
        /// </summary>
        /// <param name="gameUserId">游戏用户ID</param>
        /// <returns>用户缓存信息</returns>
        public async Task<ActivityUserCache?> GetByGameUserIdAsync(string gameUserId)
        {
            return await _context.UserCaches
                .FirstOrDefaultAsync(x => x.GameUserId == gameUserId);
        }

        /// <summary>
        /// 缓存或更新用户昵称
        /// </summary>
        /// <param name="gameUserId">游戏用户ID</param>
        /// <param name="nickname">昵称</param>
        /// <returns>用户缓存信息</returns>
        public async Task<ActivityUserCache> CacheOrUpdateNicknameAsync(string gameUserId, string nickname)
        {
            var existingCache = await GetByGameUserIdAsync(gameUserId);

            if (existingCache != null)
            {
                // 更新现有缓存
                existingCache.Nickname = nickname;
                existingCache.LastUpdateTime = DateTime.Now;
                existingCache.UpdateTime = DateTime.Now;

                _context.UserCaches.Update(existingCache);
                await _context.SaveChangesAsync();

                return existingCache;
            }
            else
            {
                // 创建新缓存
                var newCache = new ActivityUserCache
                {
                    GameUserId = gameUserId,
                    Nickname = nickname,
                    LastUpdateTime = DateTime.Now,
                    CreateTime = DateTime.Now
                };

                _context.UserCaches.Add(newCache);
                await _context.SaveChangesAsync();

                return newCache;
            }
        }

        /// <summary>
        /// 批量获取用户昵称
        /// </summary>
        /// <param name="gameUserIds">游戏用户ID列表</param>
        /// <returns>用户昵称字典</returns>
        public async Task<Dictionary<string, string>> GetNicknamesByGameUserIdsAsync(List<string> gameUserIds)
        {
            var caches = await _context.UserCaches
                .Where(x => gameUserIds.Contains(x.GameUserId))
                .Select(x => new { x.GameUserId, x.Nickname })
                .ToListAsync();

            return caches.ToDictionary(x => x.GameUserId, x => x.Nickname);
        }
    }
}
