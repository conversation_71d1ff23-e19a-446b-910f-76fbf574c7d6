# 陕西平台活动开放工具 - 后端API接口设计文档

## 1. 概述

本文档描述了陕西平台活动开放工具的完整后端API接口设计，包括用户数据同步、活动管理、任务系统、抽奖系统等核心功能的接口规范。

**系统集成说明**：
- 本系统作为游戏系统的活动模块，不独立处理用户认证
- 用户身份通过游戏系统传递的 `X-Game-User-Id` 请求头进行识别
- 用户数据（昵称、通宝余额、层级关系等）从游戏系统同步
- 任务进度更新由游戏系统在玩家行为发生时主动调用

## 2. 接口设计规范

### 2.1 基础规范
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时间格式**: ISO 8601 (YYYY-MM-DDTHH:mm:ssZ)

### 2.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-07-31T10:30:00Z"
}
```

### 2.3 错误码定义
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 通宝余额不足 |
| 1002 | 活动未开始 |
| 1003 | 活动已结束 |
| 1004 | 抽奖次数不足 |
| 1005 | 奖品份数不足 |

## 3. 用户信息缓存模块

### 3.1 缓存用户昵称
**接口**: `POST /api/users/cache-nickname`

**说明**: 缓存用户昵称信息，用于活动中显示玩家昵称

**请求参数**:
```json
{
  "game_user_id": "game_123",
  "nickname": "测试用户"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "用户昵称缓存成功",
  "data": {
    "game_user_id": "game_123",
    "nickname": "测试用户"
  }
}
```

## 4. 活动管理模块

### 4.1 创建活动
**接口**: `POST /api/activities`

**请求头**: `X-Game-User-Id: {game_user_id}` (游戏系统传递的用户ID)

**请求参数**:
```json
{
  "activity_name": "春节盲盒活动",
  "activity_type": "blind_box",
  "total_tongbao": 50000.00,
  "start_time": "2025-08-01T00:00:00Z",
  "end_time": "2025-08-07T23:59:59Z",
  "rewards": [
    {
      "reward_name": "一等奖 1000通宝",
      "reward_type": "tongbao",
      "tongbao_amount": 1000.00,
      "weight": 1,
      "daily_quantity": 5
    },
    {
      "reward_name": "二等奖 500通宝",
      "reward_type": "tongbao",
      "tongbao_amount": 500.00,
      "weight": 5,
      "daily_quantity": 10
    }
  ],
  "tasks": [
    {
      "task_type": "login",
      "task_name": "每日登录",
      "target_value": 1,
      "reward_chances": 1,
      "refresh_type": "daily"
    },
    {
      "task_type": "game_rounds",
      "task_name": "完成10局游戏",
      "target_value": 10,
      "reward_chances": 2,
      "refresh_type": "daily"
    }
  ]
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "活动创建成功",
  "data": {
    "activity_id": 123,
    "activity_name": "春节盲盒活动",
    "status": "not_started",
    "participants_count": 0
  }
}
```

### 4.2 获取活动列表
**接口**: `GET /api/activities`

**请求头**: `X-Game-User-Id: {game_user_id}`

**查询参数**:
- `status`: 活动状态 (not_started/running/ended/closed)
- `scope`: 查询范围 (my/all) - my表示只查看自己创建的，all表示查看所有(仅盟主可用)
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认10)

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "activities": [
      {
        "id": 123,
        "activity_name": "春节盲盒活动",
        "activity_type": "blind_box",
        "total_tongbao": 50000.00,
        "remaining_tongbao": 45000.00,
        "start_time": "2025-08-01T00:00:00Z",
        "end_time": "2025-08-07T23:59:59Z",
        "status": "running",
        "participants_count": 156,
        "creator_nickname": "盟主001"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

### 4.3 获取活动详情
**接口**: `GET /api/activities/{activity_id}`

**请求头**: `X-Game-User-Id: {game_user_id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "activity_name": "春节盲盒活动",
    "activity_type": "blind_box",
    "total_tongbao": 50000.00,
    "remaining_tongbao": 45000.00,
    "start_time": "2025-08-01T00:00:00Z",
    "end_time": "2025-08-07T23:59:59Z",
    "status": "running",
    "rewards": [
      {
        "id": 1,
        "reward_name": "一等奖 1000通宝",
        "reward_type": "tongbao",
        "tongbao_amount": 1000.00,
        "probability": 0.05,
        "remaining_quantity": 3
      }
    ],
    "tasks": [
      {
        "id": 1,
        "task_type": "login",
        "task_name": "每日登录",
        "target_value": 1,
        "reward_chances": 1,
        "refresh_type": "daily"
      }
    ]
  }
}
```

### 4.4 关闭活动
**接口**: `PUT /api/activities/{activity_id}/close`

**请求头**: `X-Game-User-Id: {game_user_id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "活动已关闭",
  "data": {
    "activity_id": 123,
    "status": "closed"
  }
}
```

## 5. 任务系统模块

### 5.1 获取玩家任务列表
**接口**: `GET /api/activities/{activity_id}/tasks`

**请求头**: `X-Game-User-Id: {game_user_id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tasks": [
      {
        "id": 1,
        "task_type": "login",
        "task_name": "每日登录",
        "target_value": 1,
        "reward_chances": 1,
        "refresh_type": "daily",
        "current_progress": 1,
        "completed_times": 1,
        "is_completed": true,
        "next_refresh_time": "2025-08-02T00:00:00Z"
      },
      {
        "id": 2,
        "task_type": "game_rounds",
        "task_name": "完成10局游戏",
        "target_value": 10,
        "reward_chances": 2,
        "refresh_type": "daily",
        "current_progress": 7,
        "completed_times": 0,
        "is_completed": false,
        "next_refresh_time": "2025-08-02T00:00:00Z"
      }
    ]
  }
}
```

### 5.2 更新任务进度
**接口**: `POST /api/tasks/{task_id}/progress`

**请求头**: `X-Game-User-Id: {game_user_id}`

**说明**: 由游戏系统调用，当玩家完成相关行为时更新任务进度

**请求参数**:
```json
{
  "game_user_id": "game_123",
  "progress_increment": 1,
  "action_type": "login|game_round|service_fee",
  "action_value": 1
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "任务进度已更新",
  "data": {
    "task_id": 2,
    "current_progress": 8,
    "is_completed": false,
    "reward_chances_gained": 0
  }
}
```

## 6. 抽奖系统模块

### 6.1 获取抽奖次数
**接口**: `GET /api/activities/{activity_id}/draw-chances`

**请求头**: `X-Game-User-Id: {game_user_id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_chances": 5,
    "used_chances": 2,
    "remaining_chances": 3
  }
}
```

### 6.2 执行抽奖
**接口**: `POST /api/activities/{activity_id}/draw`

**请求头**: `X-Game-User-Id: {game_user_id}`

**响应数据**:
```json
{
  "code": 200,
  "message": "抽奖成功",
  "data": {
    "draw_id": 12345,
    "reward": {
      "id": 1,
      "reward_name": "二等奖 500通宝",
      "reward_type": "tongbao",
      "tongbao_amount": 500.00
    },
    "remaining_chances": 2
  }
}
```

### 6.3 获取抽奖记录
**接口**: `GET /api/activities/{activity_id}/draw-records`

**请求头**: `X-Game-User-Id: {game_user_id}`

**查询参数**:
- `type`: 记录类型 (all/mine)
- `limit`: 数量限制 (默认50)

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 12345,
        "player_nickname": "玩家001",
        "reward_name": "二等奖 500通宝",
        "draw_time": "2025-08-01T15:30:00Z"
      }
    ],
    "total": 1
  }
}
```

## 7. 数据统计模块

### 7.1 获取活动统计数据
**接口**: `GET /api/activities/{activity_id}/statistics`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "participants_count": 156,
    "total_draws": 523,
    "total_rewards_distributed": 25000.00,
    "player_statistics": [
      {
        "player_id": 1001,
        "player_nickname": "玩家001",
        "total_draws": 15,
        "total_tongbao_won": 2500.00,
        "physical_rewards": [
          {
            "reward_name": "京东卡",
            "quantity": 1
          }
        ]
      }
    ]
  }
}
```

### 7.2 获取跑马灯消息
**接口**: `GET /api/activities/{activity_id}/marquee`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "messages": [
      {
        "id": 1,
        "player_nickname": "玩家001",
        "reward_name": "一等奖 1000通宝",
        "message_content": "恭喜玩家001获得一等奖 1000通宝！",
        "created_at": "2025-08-01T15:30:00Z"
      }
    ]
  }
}
```

## 8. 通宝管理模块

### 8.1 同步通宝余额
**接口**: `POST /api/users/{game_user_id}/tongbao/sync`

**说明**: 由游戏系统调用，同步玩家的通宝余额变化

**请求参数**:
```json
{
  "balance": 10000.00,
  "change_amount": -5000.00,
  "change_reason": "activity_invest|prize_reward|system_adjust",
  "activity_id": 123
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "通宝余额同步成功",
  "data": {
    "old_balance": 15000.00,
    "new_balance": 10000.00,
    "transaction_id": 12345
  }
}
```

### 8.2 获取通宝余额
**接口**: `GET /api/users/{game_user_id}/tongbao/balance`

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "balance": 10000.00,
    "frozen_amount": 5000.00,
    "available_amount": 5000.00
  }
}
```

### 8.3 获取通宝流水
**接口**: `GET /api/users/{game_user_id}/tongbao/transactions`

**请求头**: `X-Game-User-Id: {game_user_id}`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `type`: 交易类型
- `page`: 页码
- `limit`: 每页数量

**响应数据**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "transactions": [
      {
        "id": 1,
        "transaction_type": "activity_invest",
        "amount": -50000.00,
        "balance_before": 60000.00,
        "balance_after": 10000.00,
        "description": "创建活动：春节盲盒活动",
        "created_at": "2025-08-01T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10
  }
}
```

## 9. 权限验证

### 9.1 权限验证机制
系统通过游戏系统传递的用户信息进行权限验证：
- 通过 `X-Game-User-Id` 请求头获取用户身份
- 根据用户类型和层级关系进行权限控制
- 所有接口调用前都需要验证用户权限

### 9.2 权限级别
权限验证由游戏系统负责，活动系统通过 `X-Game-User-Id` 获取用户身份后，根据业务逻辑进行相应的数据过滤：
- **盟主**: 可查看所有活动数据
- **合伙人**: 只能查看自己创建的活动
- **玩家**: 只能参与活动，查看自己的记录

### 9.3 接口权限说明
| 接口分类 | 权限控制方式 |
|----------|-------------|
| 创建活动 | 由游戏系统验证用户是否有创建权限 |
| 查看活动列表 | 根据用户身份过滤返回数据 |
| 参与抽奖 | 由游戏系统验证用户是否可参与该活动 |
| 查看统计 | 根据用户身份和活动创建者过滤数据 |
| 任务进度更新 | 由游戏系统调用，验证用户行为真实性 |

## 10. 接口限流

### 10.1 限流规则
- 抽奖接口: 每秒1次
- 查询接口: 每分钟100次
- 创建接口: 每分钟10次
- 数据同步接口: 每分钟50次

### 10.2 限流响应
```json
{
  "code": 429,
  "message": "请求过于频繁，请稍后再试",
  "data": {
    "retry_after": 60
  }
}
```

## 11. WebSocket实时通信

### 11.1 连接建立
**连接地址**: `wss://api.example.com/ws`

**连接参数**:
```javascript
{
  "game_user_id": "game_123",
  "activity_id": 123
}
```

### 11.2 消息格式
```json
{
  "type": "marquee_message",
  "data": {
    "player_nickname": "玩家001",
    "reward_name": "一等奖 1000通宝",
    "message": "恭喜玩家001获得一等奖 1000通宝！"
  },
  "timestamp": "2025-08-01T15:30:00Z"
}
```

### 11.3 消息类型
- `marquee_message`: 跑马灯消息
- `activity_status_change`: 活动状态变更
- `task_completed`: 任务完成通知
- `draw_result`: 抽奖结果通知

## 12. 接口测试用例

### 12.1 创建活动测试
```bash
curl -X POST "https://api.example.com/api/activities" \
  -H "X-Game-User-Id: game_123" \
  -H "Content-Type: application/json" \
  -d '{
    "activity_name": "测试活动",
    "activity_type": "blind_box",
    "total_tongbao": 10000.00,
    "start_time": "2025-08-01T00:00:00Z",
    "end_time": "2025-08-07T23:59:59Z",
    "rewards": [
      {
        "reward_name": "测试奖励",
        "reward_type": "tongbao",
        "tongbao_amount": 100.00,
        "weight": 10,
        "daily_quantity": 100
      }
    ],
    "tasks": [
      {
        "task_type": "login",
        "task_name": "每日登录",
        "target_value": 1,
        "reward_chances": 1,
        "refresh_type": "daily"
      }
    ]
  }'
```

### 12.2 执行抽奖测试
```bash
curl -X POST "https://api.example.com/api/activities/123/draw" \
  -H "X-Game-User-Id: game_123" \
  -H "Content-Type: application/json"
```

### 12.3 缓存用户昵称测试
```bash
curl -X POST "https://api.example.com/api/users/cache-nickname" \
  -H "Content-Type: application/json" \
  -d '{
    "game_user_id": "game_123",
    "nickname": "测试用户"
  }'
```

---

**文档版本**: v1.0
**最后更新**: 2025-07-31
**维护人员**: 开发团队
