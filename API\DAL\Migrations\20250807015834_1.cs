﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DAL.Migrations
{
    /// <inheritdoc />
    public partial class _1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "activities",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    creator_game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "创建者游戏用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    creator_nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "创建者昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    activity_name = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, comment: "活动名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    activity_type = table.Column<int>(type: "int", nullable: false, comment: "活动类型"),
                    total_tongbao = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "投入的总通宝"),
                    remaining_tongbao = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "剩余通宝"),
                    start_time = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "开始时间"),
                    end_time = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "结束时间"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "活动状态"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_activities", x => x.id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "game_data_sync",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    sync_date = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "同步日期"),
                    user_id = table.Column<int>(type: "int", nullable: false, comment: "用户ID"),
                    nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    club_id = table.Column<int>(type: "int", nullable: false, comment: "联盟ID"),
                    club_name = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, comment: "联盟名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    game_count = table.Column<int>(type: "int", nullable: false, comment: "游戏局数"),
                    service_fee = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "服务费"),
                    online_duration = table.Column<int>(type: "int", nullable: false, comment: "在线时长（秒）"),
                    game_duration = table.Column<int>(type: "int", nullable: false, comment: "游戏时长（秒）"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_game_data_sync", x => x.id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sms_verification_code",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    PhoneNumber = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Code = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    BusinessType = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SendTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    ExpireTime = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    IsUsed = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    UsedTime = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IpAddress = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DeviceId = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sms_verification_code", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_button",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false, comment: "主键ID (32位GUID字符串)")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Name = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "按钮名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PermissionCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "权限编码")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Description = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: false, comment: "权限描述")
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_button", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_dictionary",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false, comment: "主键ID (32位GUID字符串)")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DictTypeCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DictTypeName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DictItemCode = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DictItemName = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    IsEnabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ParentId = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ExtendField1 = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ExtendField2 = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_dictionary", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_log",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(255)", nullable: false, comment: "日志ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UserId = table.Column<string>(type: "varchar(100)", nullable: true, comment: "用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Username = table.Column<string>(type: "varchar(50)", nullable: true, comment: "用户名")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Operation = table.Column<string>(type: "varchar(50)", nullable: false, comment: "操作类型")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Method = table.Column<string>(type: "varchar(200)", nullable: false, comment: "请求方法")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Params = table.Column<string>(type: "longtext", nullable: true, comment: "请求参数")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Time = table.Column<long>(type: "bigint", nullable: false, comment: "执行时长(毫秒)"),
                    Ip = table.Column<string>(type: "varchar(50)", nullable: false, comment: "IP地址")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Path = table.Column<string>(type: "varchar(500)", nullable: false, comment: "请求路径")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LogType = table.Column<string>(type: "varchar(50)", nullable: false, comment: "日志类型")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LogLevel = table.Column<string>(type: "varchar(50)", nullable: false, comment: "日志级别")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Message = table.Column<string>(type: "longtext", nullable: false, comment: "日志内容")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Exception = table.Column<string>(type: "longtext", nullable: true, comment: "异常信息")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_log", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_menu",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false, comment: "主键ID (32位GUID字符串)")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ParentId = table.Column<string>(type: "varchar(32)", nullable: true, comment: "父级ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Name = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "菜单名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Path = table.Column<string>(type: "varchar(200)", nullable: true, comment: "菜单路径")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Component = table.Column<string>(type: "varchar(200)", nullable: true, comment: "组件路径")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Perms = table.Column<string>(type: "varchar(100)", nullable: true, comment: "权限标识")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Icon = table.Column<string>(type: "varchar(500)", nullable: true, comment: "图标")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Type = table.Column<sbyte>(type: "tinyint", nullable: false, comment: "类型（0：目录，1：菜单，2：按钮）"),
                    OrderNum = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    Visible = table.Column<sbyte>(type: "tinyint", nullable: false, comment: "是否可见（0：隐藏，1：显示）"),
                    Status = table.Column<sbyte>(type: "tinyint", nullable: false, comment: "状态（0：禁用，1：启用）"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_menu", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_permission",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false, comment: "主键ID (32位GUID字符串)")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SubjectId = table.Column<string>(type: "varchar(32)", nullable: false, comment: "主体ID（用户ID或角色ID）")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ObjectId = table.Column<string>(type: "varchar(32)", nullable: false, comment: "对象ID（菜单ID或按钮ID）")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_permission", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_role",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false, comment: "主键ID (32位GUID字符串)")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Name = table.Column<string>(type: "varchar(50)", nullable: false, comment: "角色名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Code = table.Column<string>(type: "varchar(50)", nullable: false, comment: "角色编码")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    OrderNum = table.Column<int>(type: "int", nullable: false, comment: "排序号"),
                    DataScope = table.Column<sbyte>(type: "tinyint", nullable: false, comment: "数据范围（1：全部数据权限 2：自定义数据权限 3：本部门数据权限 4：本部门及以下数据权限）"),
                    Status = table.Column<sbyte>(type: "tinyint", nullable: false, comment: "状态（0：禁用 1：正常）"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_role", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_user",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "varchar(255)", nullable: false, comment: "用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UserName = table.Column<string>(type: "varchar(100)", nullable: false, comment: "用户名")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Password = table.Column<string>(type: "varchar(100)", nullable: false, comment: "密码")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RealName = table.Column<string>(type: "varchar(100)", nullable: false, comment: "真实姓名")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Avatar = table.Column<string>(type: "varchar(200)", nullable: false, comment: "头像")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Email = table.Column<string>(type: "varchar(100)", nullable: false, comment: "电子邮箱")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Mobile = table.Column<string>(type: "varchar(20)", nullable: false, comment: "手机号码")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Status = table.Column<sbyte>(type: "tinyint", nullable: false, comment: "用户状态"),
                    LastLoginTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "最后登录时间"),
                    LastLoginIp = table.Column<string>(type: "varchar(50)", nullable: true, comment: "最后登录IP")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_user", x => x.UserId);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "system_config",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    config_key = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "配置键")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    config_value = table.Column<string>(type: "TEXT", nullable: false, comment: "配置值")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    description = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, comment: "配置描述")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_system_config", x => x.id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "user_cache",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "游戏系统用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    last_update_time = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "最后更新时间"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_cache", x => x.id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "activity_rewards",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    activity_id = table.Column<int>(type: "int", nullable: false, comment: "活动ID"),
                    reward_name = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, comment: "奖励名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    reward_type = table.Column<int>(type: "int", nullable: false, comment: "奖励类型：通宝/实物"),
                    tongbao_amount = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "通宝数量"),
                    physical_item = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, comment: "实物描述")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    weight = table.Column<int>(type: "int", nullable: false, comment: "权重"),
                    probability = table.Column<decimal>(type: "decimal(8,6)", nullable: false, comment: "中奖概率"),
                    daily_quantity = table.Column<int>(type: "int", nullable: false, comment: "每日份数"),
                    remaining_quantity = table.Column<int>(type: "int", nullable: false, comment: "剩余份数"),
                    sort_order = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    last_reset_time = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "上次重置时间"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_activity_rewards", x => x.id);
                    table.ForeignKey(
                        name: "FK_activity_rewards_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "activity_tasks",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    activity_id = table.Column<int>(type: "int", nullable: false, comment: "活动ID"),
                    task_type = table.Column<int>(type: "int", nullable: false, comment: "任务类型"),
                    task_name = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, comment: "任务名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    target_value = table.Column<int>(type: "int", nullable: false, comment: "目标数值"),
                    reward_chances = table.Column<int>(type: "int", nullable: false, comment: "奖励次数"),
                    refresh_type = table.Column<int>(type: "int", nullable: false, comment: "刷新类型"),
                    sort_order = table.Column<int>(type: "int", nullable: false, comment: "排序"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_activity_tasks", x => x.id);
                    table.ForeignKey(
                        name: "FK_activity_tasks_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "marquee_messages",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    activity_id = table.Column<int>(type: "int", nullable: false, comment: "活动ID"),
                    player_game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "玩家游戏用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    player_nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "玩家昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    reward_name = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, comment: "奖励名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    message_content = table.Column<string>(type: "TEXT", nullable: false, comment: "消息内容")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    play_count = table.Column<int>(type: "int", nullable: false, comment: "播放次数"),
                    last_played_at = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "最后播放时间"),
                    is_displayed = table.Column<bool>(type: "tinyint(1)", nullable: false, comment: "是否已显示"),
                    display_time = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "显示时间"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_marquee_messages", x => x.id);
                    table.ForeignKey(
                        name: "FK_marquee_messages_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "player_draw_chances",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    activity_id = table.Column<int>(type: "int", nullable: false, comment: "活动ID"),
                    player_game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "玩家游戏用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    player_nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "玩家昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    total_chances = table.Column<int>(type: "int", nullable: false, comment: "总次数"),
                    used_chances = table.Column<int>(type: "int", nullable: false, comment: "已使用次数"),
                    remaining_chances = table.Column<int>(type: "int", nullable: false, comment: "剩余次数"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_player_draw_chances", x => x.id);
                    table.ForeignKey(
                        name: "FK_player_draw_chances_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "tongbao_transactions",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "游戏用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    user_nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "用户昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    activity_id = table.Column<int>(type: "int", nullable: true, comment: "关联活动ID"),
                    transaction_type = table.Column<int>(type: "int", nullable: false, comment: "交易类型"),
                    amount = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "金额（正数为收入，负数为支出）"),
                    balance_before = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "交易前余额"),
                    balance_after = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "交易后余额"),
                    description = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, comment: "描述")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tongbao_transactions", x => x.id);
                    table.ForeignKey(
                        name: "FK_tongbao_transactions_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "sys_user_role",
                columns: table => new
                {
                    Id = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: false, comment: "主键ID (32位GUID字符串)")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UserId = table.Column<string>(type: "varchar(32)", nullable: false, comment: "用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RoleId = table.Column<string>(type: "varchar(32)", nullable: false, comment: "角色ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_sys_user_role", x => x.Id);
                    table.ForeignKey(
                        name: "FK_sys_user_role_sys_role_RoleId",
                        column: x => x.RoleId,
                        principalTable: "sys_role",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_sys_user_role_sys_user_UserId",
                        column: x => x.UserId,
                        principalTable: "sys_user",
                        principalColumn: "UserId",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "draw_records",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    activity_id = table.Column<int>(type: "int", nullable: false, comment: "活动ID"),
                    player_game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "玩家游戏用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    player_nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "玩家昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    reward_id = table.Column<int>(type: "int", nullable: false, comment: "中奖奖励ID"),
                    draw_time = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "抽奖时间"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_draw_records", x => x.id);
                    table.ForeignKey(
                        name: "FK_draw_records_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_draw_records_activity_rewards_reward_id",
                        column: x => x.reward_id,
                        principalTable: "activity_rewards",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "prize_records",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    activity_id = table.Column<int>(type: "int", nullable: false, comment: "活动ID"),
                    player_game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "玩家游戏用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    player_nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "玩家昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    reward_id = table.Column<int>(type: "int", nullable: false, comment: "奖励ID"),
                    reward_name = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: false, comment: "奖励名称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    reward_type = table.Column<int>(type: "int", nullable: false, comment: "奖励类型"),
                    tongbao_amount = table.Column<decimal>(type: "decimal(15,2)", nullable: false, comment: "通宝数量"),
                    physical_item = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, comment: "实物描述")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    status = table.Column<int>(type: "int", nullable: false, comment: "发放状态"),
                    process_note = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true, comment: "处理备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    process_time = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "处理时间"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_prize_records", x => x.id);
                    table.ForeignKey(
                        name: "FK_prize_records_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_prize_records_activity_rewards_reward_id",
                        column: x => x.reward_id,
                        principalTable: "activity_rewards",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "player_task_progress",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false, comment: "ID")
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    activity_id = table.Column<int>(type: "int", nullable: false, comment: "活动ID"),
                    task_id = table.Column<int>(type: "int", nullable: false, comment: "任务ID"),
                    player_game_user_id = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: false, comment: "玩家游戏用户ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    player_nickname = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false, comment: "玩家昵称")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    current_progress = table.Column<int>(type: "int", nullable: false, comment: "当前进度"),
                    completed_times = table.Column<int>(type: "int", nullable: false, comment: "完成次数"),
                    last_refresh_time = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "上次刷新时间"),
                    last_completed_time = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "上次完成时间"),
                    reward_claimed = table.Column<bool>(type: "tinyint(1)", nullable: false, comment: "奖励是否已领取"),
                    reward_claimed_time = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "奖励领取时间"),
                    reward_chances_earned = table.Column<int>(type: "int", nullable: false, comment: "获得的抽奖次数"),
                    valid_from = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "任务有效期开始时间"),
                    valid_to = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "任务有效期结束时间"),
                    Remark = table.Column<string>(type: "longtext", nullable: true, comment: "备注")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "创建人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatorName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "创建人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime(6)", nullable: false, comment: "创建时间"),
                    UpdatedBy = table.Column<string>(type: "varchar(32)", maxLength: 32, nullable: true, comment: "更新人ID")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdaterName = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true, comment: "更新人")
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UpdateTime = table.Column<DateTime>(type: "datetime(6)", nullable: true, comment: "更新时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_player_task_progress", x => x.id);
                    table.ForeignKey(
                        name: "FK_player_task_progress_activities_activity_id",
                        column: x => x.activity_id,
                        principalTable: "activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_player_task_progress_activity_tasks_task_id",
                        column: x => x.task_id,
                        principalTable: "activity_tasks",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_activity_rewards_activity_id",
                table: "activity_rewards",
                column: "activity_id");

            migrationBuilder.CreateIndex(
                name: "IX_activity_tasks_activity_id",
                table: "activity_tasks",
                column: "activity_id");

            migrationBuilder.CreateIndex(
                name: "IX_draw_records_activity_id",
                table: "draw_records",
                column: "activity_id");

            migrationBuilder.CreateIndex(
                name: "IX_draw_records_reward_id",
                table: "draw_records",
                column: "reward_id");

            migrationBuilder.CreateIndex(
                name: "IX_marquee_messages_activity_id",
                table: "marquee_messages",
                column: "activity_id");

            migrationBuilder.CreateIndex(
                name: "IX_player_draw_chances_activity_id",
                table: "player_draw_chances",
                column: "activity_id");

            migrationBuilder.CreateIndex(
                name: "IX_player_task_progress_activity_id",
                table: "player_task_progress",
                column: "activity_id");

            migrationBuilder.CreateIndex(
                name: "IX_player_task_progress_task_id",
                table: "player_task_progress",
                column: "task_id");

            migrationBuilder.CreateIndex(
                name: "IX_prize_records_activity_id",
                table: "prize_records",
                column: "activity_id");

            migrationBuilder.CreateIndex(
                name: "IX_prize_records_reward_id",
                table: "prize_records",
                column: "reward_id");

            migrationBuilder.CreateIndex(
                name: "IX_sys_menu_Name",
                table: "sys_menu",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_sys_user_role_RoleId",
                table: "sys_user_role",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_sys_user_role_UserId",
                table: "sys_user_role",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_tongbao_transactions_activity_id",
                table: "tongbao_transactions",
                column: "activity_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "draw_records");

            migrationBuilder.DropTable(
                name: "game_data_sync");

            migrationBuilder.DropTable(
                name: "marquee_messages");

            migrationBuilder.DropTable(
                name: "player_draw_chances");

            migrationBuilder.DropTable(
                name: "player_task_progress");

            migrationBuilder.DropTable(
                name: "prize_records");

            migrationBuilder.DropTable(
                name: "sms_verification_code");

            migrationBuilder.DropTable(
                name: "sys_button");

            migrationBuilder.DropTable(
                name: "sys_dictionary");

            migrationBuilder.DropTable(
                name: "sys_log");

            migrationBuilder.DropTable(
                name: "sys_menu");

            migrationBuilder.DropTable(
                name: "sys_permission");

            migrationBuilder.DropTable(
                name: "sys_user_role");

            migrationBuilder.DropTable(
                name: "system_config");

            migrationBuilder.DropTable(
                name: "tongbao_transactions");

            migrationBuilder.DropTable(
                name: "user_cache");

            migrationBuilder.DropTable(
                name: "activity_tasks");

            migrationBuilder.DropTable(
                name: "activity_rewards");

            migrationBuilder.DropTable(
                name: "sys_role");

            migrationBuilder.DropTable(
                name: "sys_user");

            migrationBuilder.DropTable(
                name: "activities");
        }
    }
}
