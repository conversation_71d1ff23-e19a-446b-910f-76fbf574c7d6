using static DAL.Databases.EFHelper;

namespace DAL.Databases;

public interface IBaseQueryDLL<TEntity, TQueryable>
{

    /// <summary>
    /// 获取分页数据列表
    /// </summary>
    /// <param name="page">分页参数</param>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>分页结果</returns>
    Task<PageEntity<TEntity>> GetPageDataAsync(TQueryable queryable);

    /// <summary>
    /// 获取所有数据列表
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>数据列表</returns>
    Task<List<TEntity>> GetListAsync(
       TQueryable queryable);


    /// <summary>
    /// 获取单条记录
    /// 根据查询条件返回第一条匹配的记录
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>返回单个实体，如果没有匹配的记录则返回null</returns>
    Task<TEntity?> GetFirstAsync(TQueryable queryable);
    /// <summary>
    /// 获取单条记录，支持默认排序
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <param name="defaultOrderBy">默认排序表达式，如：q => q.OrderByDescending(x => x.CreateTime)</param>
    /// <returns>返回单个实体，如果没有匹配的记录则返回null</returns>
    Task<TEntity?> GetFirstAsync(
       TQueryable queryable,
       Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? defaultOrderBy = null);

    /// <summary>
    /// 添加单条记录
    /// </summary>
    /// <param name="entity">要添加的实体对象</param>
    /// <returns>添加是否成功</returns>
    Task<bool> AddAsync(TEntity entity);

    /// <summary>
    /// 批量添加记录
    /// </summary>
    /// <param name="entities">要添加的实体对象集合</param>
    /// <returns>添加是否成功</returns>
    Task<bool> AddRangeAsync(List<TEntity> entities);
    /// <summary>
    /// 更新单条记录
    /// </summary>
    /// <param name="entity">要更新的实体对象</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateAsync(TEntity entity);

    /// <summary>
    /// 批量更新记录
    /// </summary>
    /// <param name="entities">要更新的实体对象集合</param>
    /// <returns>更新是否成功</returns>
    Task<bool> UpdateRangeAsync(IEnumerable<TEntity> entities);

    /// <summary>
    /// 批量删除记录
    /// </summary>
    /// <param name="entities">要删除的实体对象集合</param>
    /// <returns>删除是否成功</returns>
    Task<bool> DeleteRangeAsync(IEnumerable<TEntity> entities);

    /// <summary>
    /// 根据条件删除记录
    /// 先查询出符合条件的记录，然后批量删除
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>删除是否成功</returns>
    Task<bool> DeleteByConditionAsync(TQueryable queryable);

    /// <summary>
    /// 根据主键ID删除记录
    /// </summary>
    /// <param name="id">记录的主键ID</param>
    /// <returns>删除是否成功</returns>
    Task<bool> DeleteByIdAsync<TKey>(TKey id);
}
