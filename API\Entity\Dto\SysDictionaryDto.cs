namespace Entity.Dto
{
    /// <summary>
    /// 字典项DTO
    /// </summary>
    public class SysDictionaryDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型码
        /// </summary>
        public string DictTypeCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型名称
        /// </summary>
        public string DictTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 字典项编码
        /// </summary>
        public string DictItemCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典项名称
        /// </summary>
        public string DictItemName { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 父级ID
        /// </summary>
        public string ParentId { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 扩展字段1
        /// </summary>
        public string ExtendField1 { get; set; } = string.Empty;

        /// <summary>
        /// 扩展字段2
        /// </summary>
        public string ExtendField2 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 字典项创建DTO
    /// </summary>
    public class SysDictionaryCreateDto
    {
        /// <summary>
        /// 字典类型码
        /// </summary>
        public string DictTypeCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型名称
        /// </summary>
        public string DictTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 字典项编码
        /// </summary>
        public string DictItemCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典项名称
        /// </summary>
        public string DictItemName { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 父级ID
        /// </summary>
        public string ParentId { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 扩展字段1
        /// </summary>
        public string ExtendField1 { get; set; } = string.Empty;

        /// <summary>
        /// 扩展字段2
        /// </summary>
        public string ExtendField2 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 字典项更新DTO
    /// </summary>
    public class SysDictionaryUpdateDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型码
        /// </summary>
        public string DictTypeCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型名称
        /// </summary>
        public string DictTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 字典项编码
        /// </summary>
        public string DictItemCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典项名称
        /// </summary>
        public string DictItemName { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        public int? SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool? IsEnabled { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        // public string ParentId { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 扩展字段1
        /// </summary>
        public string ExtendField1 { get; set; } = string.Empty;

        /// <summary>
        /// 扩展字段2
        /// </summary>
        public string ExtendField2 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 字典类型DTO
    /// </summary>
    public class DictionaryTypeDto
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型码
        /// </summary>
        public string DictTypeCode { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型名称
        /// </summary>
        public string DictTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }

    /// <summary>
    /// 字典项下拉框DTO
    /// </summary>
    public class DictionaryDropdownDto
    {
        /// <summary>
        /// 字典项编码
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 字典项名称
        /// </summary>
        public string Label { get; set; } = string.Empty;

        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool Disabled { get; set; } = false;

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 扩展字段1
        /// </summary>
        public string ExtendField1 { get; set; } = string.Empty;

        /// <summary>
        /// 扩展字段2
        /// </summary>
        public string ExtendField2 { get; set; } = string.Empty;
    }
}