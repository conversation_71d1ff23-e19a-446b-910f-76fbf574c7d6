namespace Entity.Dto
{
    /// <summary>
    /// 任务进度查询请求DTO
    /// </summary>
    public class TaskProgressQueryDto
    {
        /// <summary>
        /// 活动ID
        /// </summary>
        public int ActivityId { get; set; }

        /// <summary>
        /// 玩家游戏用户ID
        /// </summary>
        public string PlayerGameUserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 任务进度详细响应DTO
    /// </summary>
    public class TaskProgressDetailResponseDto
    {
        /// <summary>
        /// 任务进度ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 活动ID
        /// </summary>
        public int ActivityId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 任务类型
        /// </summary>
        public TaskType TaskType { get; set; }

        /// <summary>
        /// 目标数值
        /// </summary>
        public int TargetValue { get; set; }

        /// <summary>
        /// 当前进度
        /// </summary>
        public int CurrentProgress { get; set; }

        /// <summary>
        /// 完成次数
        /// </summary>
        public int CompletedTimes { get; set; }

        /// <summary>
        /// 奖励次数
        /// </summary>
        public int RewardChances { get; set; }

        /// <summary>
        /// 刷新类型
        /// </summary>
        public RefreshType RefreshType { get; set; }

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 奖励是否已领取
        /// </summary>
        public bool RewardClaimed { get; set; }

        /// <summary>
        /// 奖励领取时间
        /// </summary>
        public DateTime? RewardClaimedTime { get; set; }

        /// <summary>
        /// 获得的抽奖次数
        /// </summary>
        public int RewardChancesEarned { get; set; }

        /// <summary>
        /// 任务有效期开始时间
        /// </summary>
        public DateTime ValidFrom { get; set; }

        /// <summary>
        /// 任务有效期结束时间
        /// </summary>
        public DateTime ValidTo { get; set; }

        /// <summary>
        /// 上次完成时间
        /// </summary>
        public DateTime? LastCompletedTime { get; set; }

        /// <summary>
        /// 进度百分比
        /// </summary>
        public decimal ProgressPercentage => TargetValue > 0 ? Math.Min(100, (decimal)CurrentProgress / TargetValue * 100) : 0;
    }

    /// <summary>
    /// 任务奖励领取请求DTO
    /// </summary>
    public class TaskRewardClaimRequestDto
    {
        /// <summary>
        /// 活动ID
        /// </summary>
        public int ActivityId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId { get; set; }

        /// <summary>
        /// 玩家游戏用户ID
        /// </summary>
        public string PlayerGameUserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 任务奖励领取响应DTO
    /// </summary>
    public class TaskRewardClaimResponseDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 获得的抽奖次数
        /// </summary>
        public int RewardChances { get; set; }

        /// <summary>
        /// 领取时间
        /// </summary>
        public DateTime? ClaimedTime { get; set; }
    }

    /// <summary>
    /// 游戏数据同步请求DTO
    /// </summary>
    public class GameDataSyncRequestDto
    {
        /// <summary>
        /// 俱乐部ID（可选，不指定则同步所有活动）
        /// </summary>
        public int? ClubId { get; set; }

        /// <summary>
        /// 创建者ID（可选，不指定则同步所有活动）
        /// </summary>
        public int? CreatorId { get; set; }
    }

    /// <summary>
    /// 游戏数据同步响应DTO
    /// </summary>
    public class GameDataSyncResponseDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 处理的创建者数量
        /// </summary>
        public int ProcessedCreators { get; set; }

        /// <summary>
        /// 失败的创建者数量
        /// </summary>
        public int FailedCreators { get; set; }

        /// <summary>
        /// 同步的记录数
        /// </summary>
        public int SyncedRecords { get; set; }

        /// <summary>
        /// 同步时间
        /// </summary>
        public DateTime SyncTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 活动任务进度汇总DTO
    /// </summary>
    public class ActivityTaskProgressSummaryDto
    {
        /// <summary>
        /// 活动ID
        /// </summary>
        public int ActivityId { get; set; }

        /// <summary>
        /// 活动名称
        /// </summary>
        public string ActivityName { get; set; } = string.Empty;

        /// <summary>
        /// 玩家游戏用户ID
        /// </summary>
        public string PlayerGameUserId { get; set; } = string.Empty;

        /// <summary>
        /// 玩家昵称
        /// </summary>
        public string PlayerNickname { get; set; } = string.Empty;

        /// <summary>
        /// 任务进度列表
        /// </summary>
        public List<TaskProgressDetailResponseDto> TaskProgresses { get; set; } = [];

        /// <summary>
        /// 总任务数
        /// </summary>
        public int TotalTasks => TaskProgresses.Count;

        /// <summary>
        /// 已完成任务数
        /// </summary>
        public int CompletedTasks => TaskProgresses.Count(t => t.IsCompleted);

        /// <summary>
        /// 已领取奖励任务数
        /// </summary>
        public int ClaimedRewardTasks => TaskProgresses.Count(t => t.RewardClaimed);

        /// <summary>
        /// 总获得抽奖次数
        /// </summary>
        public int TotalRewardChances => TaskProgresses.Sum(t => t.RewardChancesEarned);
    }

    /// <summary>
    /// 中奖记录响应DTO
    /// </summary>
    public class PrizeRecordResponseDto
    {
        public int Id { get; set; }
        public int ActivityId { get; set; }
        public string PlayerGameUserId { get; set; } = string.Empty;
        public string PlayerNickname { get; set; } = string.Empty;
        public int RewardId { get; set; }
        public string RewardName { get; set; } = string.Empty;
        public RewardType RewardType { get; set; }
        public decimal TongbaoAmount { get; set; }
        public string? PhysicalItem { get; set; }
        public PrizeStatus Status { get; set; }
        public string? ProcessNote { get; set; }
        public DateTime? ProcessTime { get; set; }
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 更新中奖记录状态请求DTO
    /// </summary>
    public class UpdatePrizeStatusRequestDto
    {
        public int PrizeId { get; set; }
        public PrizeStatus Status { get; set; }
        public string? ProcessNote { get; set; }
    }
}
