using BLL.SysService;
using DAL.SysDAL;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 菜单管理控制器
    /// 提供菜单的CRUD操作、菜单树查询、用户菜单权限管理等功能
    /// </summary>
    /// <remarks>
    /// 构造函数,通过依赖注入注入菜单服务
    /// </remarks>
    /// <param name="menuService">菜单服务实例</param>
    /// <param name="logService">日志服务实例</param>
    [Permission]
    public class MenuController(SysMenuService menuService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 菜单服务接口
        /// </summary>
        private readonly SysMenuService _menuService = menuService;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 创建新菜单
        /// </summary>
        /// <param name="input">创建菜单请求</param>
        /// <returns>创建结果</returns>
        [FunctionPermission("menu:create", "创建菜单")]
        [HttpPost("create")]
        public async Task<Result<string>> CreateAsync(CreateMenuDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行业务操作
            var menuId = await _menuService.CreateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "菜单管理",
                Operation = "创建菜单",
                BusinessObject = "SysMenu",
                ObjectId = menuId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功创建了菜单 {input.Name}，菜单ID: {menuId}",
                AfterData = new { MenuId = menuId, MenuName = input.Name, MenuType = input.Type, input.Path },
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(menuId.ToString(), "创建成功");
        }

        /// <summary>
        /// 更新现有菜单信息
        /// </summary>
        /// <param name="input">更新菜单请求</param>
        /// <returns>更新结果</returns>
        [FunctionPermission("menu:update", "更新菜单")]
        [HttpPost("update")]
        public async Task<Result> UpdateAsync(UpdateMenuDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取更新前的菜单信息，用于日志记录
            var beforeMenu = await _menuService.GetAsync(input.Id);

            // 执行业务操作
            await _menuService.UpdateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "菜单管理",
                Operation = "更新菜单",
                BusinessObject = "SysMenu",
                ObjectId = input.Id,
                DetailedInfo = $"用户 {currentUser.UserName} 成功更新了菜单 {input.Name} 的信息",
                BeforeData = beforeMenu,
                AfterData = input,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("更新成功");
        }

        /// <summary>
        /// 删除指定菜单
        /// </summary>
        /// <param name="id">菜单ID</param>
        /// <returns>删除结果</returns>
        [FunctionPermission("menu:delete", "删除菜单")]
        [HttpPost("delete/{id}")]
        public async Task<Result> DeleteAsync(string id)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取删除前的菜单信息，用于日志记录
            var beforeMenu = await _menuService.GetAsync(id);

            // 执行业务操作
            await _menuService.DeleteAsync(id);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "菜单管理",
                Operation = "删除菜单",
                BusinessObject = "SysMenu",
                ObjectId = id,
                DetailedInfo = $"用户 {currentUser.UserName} 成功删除了菜单 {beforeMenu.Name}，菜单ID: {id}",
                BeforeData = beforeMenu,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("删除成功");
        }


        /// <summary>
        /// 获取指定菜单的详细信息
        /// </summary>
        /// <param name="id">菜单ID</param>
        /// <returns>菜单详细信息</returns>
        [HttpGet("{id}")]
        public async Task<Result<MenuDto>> GetAsync(string id)
        => Success(await _menuService.GetAsync(id), "获取成功");


        /// <summary>
        /// 获取菜单树结构
        /// </summary>
        /// <param name="query">菜单查询条件</param>
        /// <returns>菜单树结构</returns>
        [HttpGet("tree")]
        public async Task<Result<List<MenuDto>>> GetTreeAsync([FromQuery] SysMenuDAL.Queryable query)
        => Success(await _menuService.GetTreeAsync(query), "获取成功");




        /// <summary>
        /// 获取指定用户的菜单树
        /// </summary>
        /// <returns>当前用户菜单树</returns>
        [HttpGet("GetSelfUserMenuTreeAsync")]
        public async Task<Result<List<MenuDto>>> GetSelfUserMenuTreeAsync()
        {
            if (IsAdmin())
                return Success(await _menuService.GetTreeAsync(new SysMenuDAL.Queryable()), "获取成功");
            return Success(await _menuService.GetUserMenuTreeAsync(GetCurrentUserId()), "获取成功");
        }




        /// <summary>
        /// 将菜单树扁平化为列表
        /// </summary>
        /// <param name="menuTree">菜单树</param>
        /// <returns>扁平化的菜单列表</returns>
        private static List<MenuDto> FlattenMenuTree(List<MenuDto> menuTree)
        {
            var result = new List<MenuDto>();

            foreach (var menu in menuTree)
            {
                var menuCopy = new MenuDto
                {
                    Id = menu.Id,
                    ParentId = menu.ParentId,
                    Name = menu.Name,
                    Path = menu.Path,
                    Component = menu.Component,
                    Perms = menu.Perms,
                    Icon = menu.Icon,
                    Type = menu.Type,
                    OrderNum = menu.OrderNum,
                    Visible = menu.Visible,
                    Status = menu.Status,
                    Remark = menu.Remark,
                    CreateTime = menu.CreateTime,
                    UpdateTime = menu.UpdateTime,
                    Children = []
                };

                result.Add(menuCopy);

                if (menu.Children != null && menu.Children.Count > 0)
                    result.AddRange(FlattenMenuTree(menu.Children));

            }

            return result;
        }


    }
}