using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using System.Collections;
using System.Collections.Concurrent;

namespace Common.Helper;

/// <summary>
/// 配置监控服务
/// 提供配置文件的热更新支持和静态访问能力
/// </summary>
public sealed class ConfigurationMonitor
{
    private static readonly ConcurrentDictionary<string, object> _configCache = new();
    private readonly IConfiguration _configuration;
    private static ConfigurationMonitor? _instance;

    /// <summary>
    /// 获取ConfigurationMonitor实例
    /// </summary>
    public static ConfigurationMonitor Instance => _instance ?? throw new InvalidOperationException("ConfigurationMonitor未初始化");

    /// <summary>
    /// 初始化ConfigurationMonitor
    /// </summary>
    /// <param name="configuration">IConfiguration实例</param>
    public ConfigurationMonitor(IConfiguration configuration)
    {
        _configuration = configuration;
        _instance = this;

        // 注册配置变更事件
        ChangeToken.OnChange(
            () => _configuration.GetReloadToken(),
            OnConfigurationChanged);


    }

    /// <summary>
    /// 配置变更事件
    /// </summary>
    private void OnConfigurationChanged()
    {
        try
        {
            // 清除缓存
            _configCache.Clear();

            // 获取 BasisConfigDto 中定义的所有静态属性类型
            var configTypes = typeof(BasisConfigDto)
                .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                .Select(p => p.PropertyType)
                .ToList();

            // 清空所有配置类的静态属性
            foreach (var configType in configTypes)
            {
                ClearStaticConfig(configType);
            }

            // 加载新配置
            var config = _configuration.GetSection("BasisConfig").Get<BasisConfigDto>();
            if (config != null)
            {
                // 使用反射更新所有静态属性
                var properties = typeof(BasisConfigDto).GetProperties();
                foreach (var property in properties)
                {
                    if (!property.CanWrite) continue;

                    var value = property.GetValue(config);
                    if (value == null) continue;

                    property.SetValue(null, value);
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"配置更新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清空静态配置类的所有静态属性
    /// </summary>
    private static void ClearStaticConfig(Type configType)
    {
        var properties = configType.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
        foreach (var property in properties)
        {
            if (!property.CanWrite) continue;

            var propertyType = property.PropertyType;
            if (typeof(IList).IsAssignableFrom(propertyType))
            {
                // 对于集合类型，创建新的空集合
                var newList = Activator.CreateInstance(propertyType);
                property.SetValue(null, newList);
            }
            else if (propertyType == typeof(string))
            {
                // 字符串设置为空
                property.SetValue(null, string.Empty);
            }
            else if (propertyType.IsValueType)
            {
                // 值类型设置为默认值
                property.SetValue(null, Activator.CreateInstance(propertyType));
            }
            else
            {
                // 引用类型创建新实例
                property.SetValue(null, Activator.CreateInstance(propertyType));
            }
        }
    }




}