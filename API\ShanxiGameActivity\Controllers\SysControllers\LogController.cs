using BLL.SysService;
using BLL.SysService.Exports;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;
using static DAL.Databases.EFHelper;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 系统日志控制器
    /// 提供日志查询、详情和清理等功能
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="logService">日志服务</param>
    [Permission]
    public class LogController(SysLogService logService) : BaseController
    {
        /// <summary>
        /// 日志服务
        /// </summary>
        private readonly SysLogService _logService = logService;


        /// <summary>
        /// 分页查询日志列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>日志列表</returns>
        [HttpGet("query")]
        public async Task<Result<PageEntity<SysLog>>> QueryAsync([FromQuery] SysLogDAL.Queryable queryable)
        => Success(await _logService.GetPageAsync(queryable), "获取日志列表成功");

        /// <summary>
        /// 获取日志详情
        /// </summary>
        /// <param name="id">日志ID</param>
        /// <returns>日志详情</returns>
        [HttpGet("{id}")]
        public async Task<Result<SysLog>> GetAsync(string id)
        {
            // 创建查询条件查找指定ID的日志
            var logs = await _logService.GetPageAsync(new SysLogDAL.Queryable { Id = id });
            var log = logs.List?.FirstOrDefault();
            if (log == null) return Fail<SysLog>("日志不存在");
            return Success(log, "获取日志详情成功");
        }

        /// <summary>
        /// 导出日志列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <param name="exportRequest">导出配置</param>
        /// <returns>CSV文件</returns>
        [HttpPost("export")]
        [FunctionPermission("log:export", "导出日志列表")]
        public async Task<IActionResult> ExportLogsAsync(
            [FromQuery] SysLogDAL.Queryable queryable,
            [FromBody] ExportRequestDto exportRequest)
        {

            // 使用分页查询方法获取日志
            var logList = await _logService.GetListAsync(queryable);

            // 如果未找到符合条件的日志数据，返回错误
            if (logList == null || logList.Count == 0)
                return BadRequest(new Result { Code = 500, Success = false, Msg = "未找到符合条件的日志数据" });

            // 如果没有提供导出列配置，则使用默认配置
            if (exportRequest.Columns == null || exportRequest.Columns.Count == 0)
                exportRequest.Columns =
                [
                    new() { Title = "日志ID", PropertyName = "Id", Order = 1 },
                    new() { Title = "操作用户", PropertyName = "UserName", Order = 2 },
                    new() { Title = "模块", PropertyName = "Module", Order = 3 },
                    new() { Title = "操作描述", PropertyName = "Description", Order = 4 },
                    new() { Title = "IP地址", PropertyName = "IpAddress", Order = 5 },
                    new() { Title = "日志类型", PropertyName = "LogType", Order = 6 },
                    new() { Title = "请求方法", PropertyName = "RequestMethod", Order = 7 },
                    new() { Title = "请求URL", PropertyName = "RequestUrl", Order = 8 },
                    new() { Title = "浏览器", PropertyName = "Browser", Order = 9 },
                    new() { Title = "操作系统", PropertyName = "Os", Order = 10 },
                    new() { Title = "状态", PropertyName = "Status", Order = 11 },
                    new() { Title = "执行时间(ms)", PropertyName = "ExecutionTime", Order = 12 },
                    new() { Title = "创建时间", PropertyName = "CreateTime", Order = 13, Format = "yyyy-MM-dd HH:mm:ss" }
                ];

            // 使用导出服务生成文件
            var exportService = HttpContext.RequestServices.GetRequiredService<ExportService>();
            var fileContentResult = exportService.ExportToFile(logList, exportRequest, "系统日志列表");

            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "日志管理",
                Operation = "导出日志列表",
                BusinessObject = "SysLog",
                ObjectId = "N/A",
                DetailedInfo = $"用户 {currentUser.UserName} 成功导出了日志列表，共 {logList.Count} 条记录",
                BeforeData = queryable,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return fileContentResult;
        }

        /// <summary>
        /// 清理指定日期之前的日志
        /// </summary>
        /// <param name="clearDto">清理日志参数</param>
        /// <returns>操作结果</returns>
        [HttpPost("clear")]
        [FunctionPermission("log:clear", "清理日志")]
        public async Task<Result> ClearLogsAsync([FromBody] LogClearDto clearDto)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行业务操作
            await _logService.ClearLogsAsync(clearDto.BeforeDate, clearDto.LogType);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "日志管理",
                Operation = "清理历史日志",
                BusinessObject = "SysLog",
                ObjectId = "N/A",
                DetailedInfo = $"用户 {currentUser.UserName} 成功清理了 {clearDto.BeforeDate:yyyy-MM-dd} 之前的{(string.IsNullOrEmpty(clearDto.LogType) ? "所有" : clearDto.LogType)}日志",
                BeforeData = clearDto,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("清理日志成功");
        }
    }
}


