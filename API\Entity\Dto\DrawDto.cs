namespace Entity.Dto;

/// <summary>
/// 抽奖次数响应DTO
/// </summary>
public class DrawChancesResponseDto
{
    /// <summary>
    /// 总次数
    /// </summary>
    public int TotalChances { get; set; }

    /// <summary>
    /// 已使用次数
    /// </summary>
    public int UsedChances { get; set; }

    /// <summary>
    /// 剩余次数
    /// </summary>
    public int RemainingChances { get; set; }
}

/// <summary>
/// 抽奖结果响应DTO
/// </summary>
public class DrawResultResponseDto
{
    /// <summary>
    /// 抽奖记录ID
    /// </summary>
    public int DrawId { get; set; }

    /// <summary>
    /// 中奖奖励
    /// </summary>
    public DrawRewardDto Reward { get; set; } = new DrawRewardDto();

    /// <summary>
    /// 剩余次数
    /// </summary>
    public int RemainingChances { get; set; }
}

/// <summary>
/// 抽奖奖励DTO
/// </summary>
public class DrawRewardDto
{
    /// <summary>
    /// 奖励ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 奖励名称
    /// </summary>
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 奖励类型
    /// </summary>
    public RewardType RewardType { get; set; }

    /// <summary>
    /// 通宝数量
    /// </summary>
    public decimal TongbaoAmount { get; set; }

    /// <summary>
    /// 实物描述
    /// </summary>
    public string? PhysicalItem { get; set; }
}

/// <summary>
/// 抽奖记录响应DTO
/// </summary>
public class DrawRecordResponseDto
{
    /// <summary>
    /// 抽奖记录ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 玩家昵称
    /// </summary>
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 奖励名称
    /// </summary>
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 抽奖时间
    /// </summary>
    public DateTime DrawTime { get; set; }
}

/// <summary>
/// 抽奖记录查询DTO
/// </summary>
public class DrawRecordQueryDto
{
    /// <summary>
    /// 记录类型 (all/mine)
    /// </summary>
    public string Type { get; set; } = "all";

    /// <summary>
    /// 数量限制
    /// </summary>
    public int Limit { get; set; } = 50;
}
