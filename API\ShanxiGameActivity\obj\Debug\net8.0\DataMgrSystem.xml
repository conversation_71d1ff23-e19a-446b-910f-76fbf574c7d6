<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DataMgrSystem</name>
    </assembly>
    <members>
        <member name="T:DataMgrSystem.Controllers.ActivityControllers.ActivityController">
            <summary>
            活动管理控制器
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.ActivityController.#ctor(BLL.ActivityService.ActivityService)">
            <summary>
            活动管理控制器
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.ActivityController.CreateAsync(Entity.Dto.CreateActivityDto)">
            <summary>
            创建活动
            </summary>
            <param name="request">创建活动请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.ActivityController.GetActivitiesAsync(System.Nullable{Entity.ActivityStatus},System.String,System.Int32,System.Int32)">
            <summary>
            获取活动列表
            </summary>
            <param name="status">活动状态</param>
            <param name="scope">查询范围</param>
            <param name="page">页码</param>
            <param name="limit">每页数量</param>
            <returns>活动列表</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.ActivityController.GetActivityDetailAsync(System.Int32)">
            <summary>
            获取活动详情
            </summary>
            <param name="activityId">活动ID</param>
            <returns>活动详情</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.ActivityController.CloseActivityAsync(System.Int32)">
            <summary>
            关闭活动
            </summary>
            <param name="activityId">活动ID</param>
            <returns>关闭结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.ActivityController.GetGameUserIdFromHeader">
            <summary>
            从请求头获取游戏用户ID
            </summary>
            <returns>游戏用户ID</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.ActivityControllers.DrawController">
            <summary>
            抽奖系统控制器
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.DrawController.#ctor(BLL.ActivityService.DrawService)">
            <summary>
            抽奖系统控制器
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.DrawController.GetDrawChancesAsync(System.Int32)">
            <summary>
            获取抽奖次数
            </summary>
            <param name="activityId">活动ID</param>
            <returns>抽奖次数信息</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.DrawController.DrawAsync(System.Int32)">
            <summary>
            执行抽奖
            </summary>
            <param name="activityId">活动ID</param>
            <returns>抽奖结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.DrawController.GetDrawRecordsAsync(System.Int32,System.String,System.Int32)">
            <summary>
            获取抽奖记录
            </summary>
            <param name="activityId">活动ID</param>
            <param name="type">记录类型</param>
            <param name="limit">数量限制</param>
            <returns>抽奖记录</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.DrawController.AddDrawChancesAsync(System.Int32,System.String,System.String,System.Int32)">
            <summary>
            增加抽奖次数（内部接口，由任务系统调用）
            </summary>
            <param name="activityId">活动ID</param>
            <param name="gameUserId">游戏用户ID</param>
            <param name="playerNickname">玩家昵称</param>
            <param name="chances">增加的次数</param>
            <returns>更新后的抽奖次数</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.DrawController.GetGameUserIdFromHeader">
            <summary>
            从请求头获取游戏用户ID
            </summary>
            <returns>游戏用户ID</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.ActivityControllers.UserCacheController">
            <summary>
            用户信息缓存控制器
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.UserCacheController.#ctor(BLL.ActivityService.UserCacheService)">
            <summary>
            用户信息缓存控制器
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.UserCacheController.CacheNicknameAsync(Entity.Dto.CacheUserNicknameDto)">
            <summary>
            缓存用户昵称
            </summary>
            <param name="request">缓存请求</param>
            <returns>缓存结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.UserCacheController.GetNicknameAsync(System.String)">
            <summary>
            获取用户昵称
            </summary>
            <param name="gameUserId">游戏用户ID</param>
            <returns>用户昵称</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.ActivityControllers.UserCacheController.GetNicknamesAsync(System.Collections.Generic.List{System.String})">
            <summary>
            批量获取用户昵称
            </summary>
            <param name="gameUserIds">游戏用户ID列表</param>
            <returns>用户昵称字典</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.CacheType">
            <summary>
            缓存类型枚举
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.CacheExpireType">
            <summary>
            缓存过期类型
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.CacheScope">
            <summary>
            缓存范围枚举
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute">
            <summary>
            缓存Action [配置特性]
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.IsToken">
            <summary>
            是否在缓存Key内添加用户信息
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.CacheSeconds">
            <summary>
            缓存时间(秒)
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.CacheType">
            <summary>
            缓存类型
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.RedisInstance">
            <summary>
            Redis实例名
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.CachePrefix">
            <summary>
            缓存前缀
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.CacheScope">
            <summary>
            缓存范围
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.ExpireType">
            <summary>
            过期类型
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.TimeScope">
            <summary>
            时间范围(绝对时间模式下使用)
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.TimeScopeCount">
            <summary>
            时间范围计数(绝对时间模式下使用)
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.UseHashStructure">
            <summary>
            是否使用Hash结构存储
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.#ctor">
            <summary>
            默认构造函数
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.#ctor(System.Int32,DataMgrSystem.Controllers.Attributes.Whether)">
            <summary>
            构造函数 - 基本参数
            </summary>
            <param name="cacheTime">缓存时间[秒]</param>
            <param name="isToken">是否需要将Token加入缓存Key</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.#ctor(System.Int32,DataMgrSystem.Controllers.Attributes.CacheType,DataMgrSystem.Controllers.Attributes.CacheScope,System.String,DataMgrSystem.Controllers.Attributes.Whether,DataMgrSystem.Controllers.Attributes.CacheExpireType,System.Boolean)">
            <summary>
            构造函数 - 完整参数
            </summary>
            <param name="cacheTime">缓存时间[秒]</param>
            <param name="cacheType">缓存类型(内存/Redis)</param>
            <param name="cacheScope">缓存范围(全局/用户/角色/部门)</param>
            <param name="redisInstance">Redis实例名称</param>
            <param name="isToken">是否需要将Token加入缓存Key</param>
            <param name="expireType">过期类型</param>
            <param name="useHashStructure">是否使用Hash结构</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Attributes.ActionCacheAttribute.#ctor(Common.Redis.RedisHelper.TimeScope,System.Int32,DataMgrSystem.Controllers.Attributes.CacheType,DataMgrSystem.Controllers.Attributes.CacheScope,System.String,DataMgrSystem.Controllers.Attributes.Whether,System.Boolean)">
            <summary>
            构造函数 - 绝对时间模式
            </summary>
            <param name="timeScope">时间范围</param>
            <param name="timeScopeCount">时间范围计数</param>
            <param name="cacheType">缓存类型(内存/Redis)</param>
            <param name="cacheScope">缓存范围(全局/用户/角色/部门)</param>
            <param name="redisInstance">Redis实例名称</param>
            <param name="isToken">是否需要将Token加入缓存Key</param>
            <param name="useHashStructure">是否使用Hash结构</param>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.CardOpenValidationAttribute">
            <summary>
            CardOpen验证特性
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.LogAttribute">
            <summary>
            日志记录特性
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.LogAttribute.Module">
            <summary>
            模块名称
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.LogAttribute.Description">
            <summary>
            操作描述
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.LogAttribute.Level">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.LogAttribute.LogParams">
            <summary>
            是否记录参数
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.LogAttribute.LogResult">
            <summary>
            是否记录返回值
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.FunctionPermissionAttribute">
            <summary>
            功能权限特性
            </summary>
            <remarks>
            权限特性
            </remarks>
            <param name="code"> 权限编码 </param>
            <param name="description"> 权限描述 </param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Attributes.FunctionPermissionAttribute.#ctor(System.String,System.String)">
            <summary>
            功能权限特性
            </summary>
            <remarks>
            权限特性
            </remarks>
            <param name="code"> 权限编码 </param>
            <param name="description"> 权限描述 </param>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.FunctionPermissionAttribute.Code">
            <summary>
            权限编码
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Attributes.FunctionPermissionAttribute.Description">
            <summary>
            权限描述
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Attributes.PermissionAttribute">
            <summary>
            权限特性
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.BasisController.BaseController.UserCacheKeyPrefix">
            <summary>
            用户缓存键前缀
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.BasisController.BaseController.UserCacheExpiration">
            <summary>
            用户缓存过期时间
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.BasisController.BaseController.IP">
            <summary>
            当前请求的IP
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.GetHeader(System.String)">
            <summary>
            获取请求头信息
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.TryGetHeader(System.String,System.String)">
            <summary>
            尝试获取请求头信息，如果不存在则返回默认值
            </summary>
            <param name="key">请求头名称</param>
            <param name="defaultValue">默认值</param>
            <returns>请求头值或默认值</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.ValidateToken(System.String)">
            <summary>
            验证并解析JWT Token
            </summary>
            <returns>ClaimsPrincipal对象</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当token验证失败时抛出</exception>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.GetCurrentUserId">
            <summary>
            从JWT中获取当前用户ID
            </summary>
            <returns>用户ID</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取用户ID时抛出</exception>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.GetCurrentUserInfo">
            <summary>
            当前用户ID
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.GetCurrentUserName">
            <summary>
            从JWT中获取当前用户名
            </summary>
            <returns>用户名</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取用户名时抛出</exception>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.IsAdmin">
            <summary>
            检查当前用户是否为管理员
            </summary>
            <returns>是否为管理员</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.GetClaimValue(System.String)">
            <summary>
            获取指定类型的声明值
            </summary>
            <param name="claimType">声明类型</param>
            <returns>声明值</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取指定声明时抛出</exception>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.TryGetClaimValue(System.String,System.String)">
            <summary>
            尝试获取指定类型的声明值，失败时返回默认值而不抛出异常
            </summary>
            <param name="claimType">声明类型</param>
            <param name="defaultValue">获取失败时返回的默认值</param>
            <returns>声明值或默认值</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.GetCurrentUserInfoAsync(BLL.SysService.SysUserService,System.Boolean)">
            <summary>
            从JWT中获取当前用户信息
            </summary>
            <param name="userService">用户服务</param>
            <param name="useCache">是否使用缓存</param>
            <returns>用户信息DTO</returns>
            <exception cref="T:System.ArgumentNullException">当用户服务为空时抛出</exception>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取用户ID时抛出</exception>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.ClearCurrentUserCache">
            <summary>
            清除当前用户的缓存信息
            </summary>
            <returns>是否成功清除缓存</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.GetJwtToken">
            <summary>
            获取JWT令牌
            </summary>
            <returns>JWT令牌字符串</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.Success``1(``0,System.String)">
            <summary>
            创建成功响应
            </summary>
            <typeparam name="T">响应数据类型</typeparam>
            <param name="data">响应数据</param>
            <param name="message">响应消息</param>
            <returns>成功响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.Success(System.String)">
            <summary>
            创建成功响应（无数据）
            </summary>
            <param name="message">响应消息</param>
            <returns>成功响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.Fail``1(System.String,System.Int32)">
            <summary>
            创建失败响应
            </summary>
            <typeparam name="T">响应数据类型</typeparam>
            <param name="message">错误消息</param>
            <param name="code">错误码</param>
            <returns>失败响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.Fail(System.String,System.Int32)">
            <summary>
            创建失败响应（无数据）
            </summary>
            <param name="message">错误消息</param>
            <param name="code">错误码</param>
            <returns>失败响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.BadRequestResult(System.String)">
            <summary>
            创建BadRequest响应
            </summary>
            <param name="message">错误消息</param>
            <returns>BadRequest响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.NotFoundResult(System.String)">
            <summary>
            创建NotFound响应
            </summary>
            <param name="message">错误消息</param>
            <returns>NotFound响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.UnauthorizedResult(System.String)">
            <summary>
            创建Unauthorized响应
            </summary>
            <param name="message">错误消息</param>
            <returns>Unauthorized响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BaseController.ForbiddenResult(System.String)">
            <summary>
            创建Forbidden响应
            </summary>
            <param name="message">错误消息</param>
            <returns>Forbidden响应</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BasisController.UploadFileAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            本地单文件上传
            </summary>
            <param name="file">文件</param>
            <returns></returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.BasisController.BasisController.UploadFilesAsync(System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            本地多文件上传
            </summary>
            <param name="files">文件列表</param>
            <returns></returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.BasisController.ResuItEntity.Result">
            <summary>
            接口返回规范
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.BasisController.ResuItEntity.Result.Code">
            <summary>
            响应码
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.BasisController.ResuItEntity.Result.Msg">
            <summary>
            返回消息
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.BasisController.ResuItEntity.Result.Success">
            <summary>
            返回结果
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.BasisController.ResuItEntity.Result`1.Data">
            <summary>
            接口返回数据
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Filter.ExceptionFilter">
            <summary>
            全局异常过滤器
            用于捕获和处理应用程序中所有未处理的异常
            </summary>
            <remarks>
            构造函数，注入日志服务
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Filter.ExceptionFilter.#ctor(BLL.SysService.SysLogService)">
            <summary>
            全局异常过滤器
            用于捕获和处理应用程序中所有未处理的异常
            </summary>
            <remarks>
            构造函数，注入日志服务
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Filter.ExceptionFilter.OnExceptionAsync(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext)">
            <summary>
            异常发生时的处理方法
            </summary>
            <param name="context">异常上下文,包含异常信息和HTTP上下文</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Filter.ExceptionFilter.GetFullExceptionMessage(System.Exception)">
            <summary>
            获取完整的异常信息,包括所有内部异常
            </summary>
            <param name="ex">异常对象</param>
            <returns>包含所有内部异常信息的字符串,用箭头连接</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.Filter.LogActionFilter">
            <summary>
            日志操作过滤器
            用于处理带有LogAttribute特性的Action
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Filter.LogActionFilter.#ctor(BLL.SysService.SysLogService)">
            <summary>
            日志操作过滤器
            用于处理带有LogAttribute特性的Action
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Filter.LogActionFilter.GetLogAttribute(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            获取Action的Log特性
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Filter.LogActionFilter.LogActionExecution(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,DataMgrSystem.Controllers.Attributes.LogAttribute,System.Exception,System.Int64)">
            <summary>
            记录Action执行日志
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.CachingMiddleware">
            <summary>
            缓存中间件
            </summary>
            <param name="next"></param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            缓存中间件
            </summary>
            <param name="next"></param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            中间件处理方法
            </summary>
            <param name="context">HTTP上下文</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddleware.GenerateCacheKey(Microsoft.AspNetCore.Http.HttpContext,DataMgrSystem.Controllers.Attributes.ActionCacheAttribute)">
            <summary>
            生成缓存键
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="attribute">缓存特性</param>
            <returns>基础键和哈希字段</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddleware.GenerateShortCode(System.String)">
            <summary>
            生成短码
            </summary>
            <param name="input">输入字符串</param>
            <returns>生成的短码</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddleware.TryGetAndReturnCachedResponse(Microsoft.AspNetCore.Http.HttpContext,DataMgrSystem.Controllers.Attributes.ActionCacheAttribute,System.String,System.String)">
            <summary>
            尝试获取缓存并返回响应
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="attribute">缓存特性</param>
            <param name="baseKey">基础键</param>
            <param name="hashField">哈希字段</param>
            <returns>是否成功获取缓存</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddleware.SetCache(System.String,System.String,System.String,DataMgrSystem.Controllers.Attributes.ActionCacheAttribute)">
            <summary>
            设置缓存
            </summary>
            <param name="baseKey">基础键</param>
            <param name="hashField">哈希字段</param>
            <param name="responseContent">响应内容</param>
            <param name="attribute">缓存特性</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddleware.SetRedisExpiry(System.String,DataMgrSystem.Controllers.Attributes.ActionCacheAttribute)">
            <summary>
            设置Redis缓存过期时间
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.CachingMiddlewareExtensions">
            <summary>
            中间件扩展方法
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.CachingMiddlewareExtensions.UseCaching(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加缓存中间件到应用程序管道
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare">
            <summary>
            全局异常处理中间件
            用于捕获和处理管道中未被其他过滤器处理的异常
            </summary>
            <param name="next">请求处理委托</param>
            <param name="logService">日志服务</param>
            <param name="logger">ILogger实例</param>
            <param name="environment">Web主机环境</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,BLL.SysService.SysLogService,Microsoft.Extensions.Logging.ILogger{DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare},Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            全局异常处理中间件
            用于捕获和处理管道中未被其他过滤器处理的异常
            </summary>
            <param name="next">请求处理委托</param>
            <param name="logService">日志服务</param>
            <param name="logger">ILogger实例</param>
            <param name="environment">Web主机环境</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            处理HTTP请求
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare.HandleExceptionAsync(Microsoft.AspNetCore.Http.HttpContext,System.Exception)">
            <summary>
             处理异常
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="ex">异常对象</param>
            <returns></returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare.WriteLogAsync(Entity.Dto.CreateLogDto,System.Exception)">
            <summary>
            写入文本日志和数据库日志
            </summary>
            <param name="logDto">日志信息</param>
            <param name="ex">异常信息</param>
            <returns></returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare.DetermineStatusAndMessage(System.Exception)">
            <summary>
            确定状态码和消息 返回是否需要记录文本和数据库日志
            </summary>
            <param name="ex">异常对象</param>
            <returns>状态码, 消息, 是否需要记录文本和数据库日志</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.ExceptionMiddleWare.GetFullExceptionMessage(System.Exception)">
            <summary>
            递归获取异常及其内部异常的完整消息
            </summary>
            <param name="ex">异常对象</param>
            <returns>完整的异常消息</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.ExceptionMiddlewareExtensions">
            <summary>
            异常中间件扩展方法
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.ExceptionMiddlewareExtensions.UseExceptionMiddleware(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用全局异常处理中间件
            </summary>
            <param name="builder">应用构建器</param>
            <returns>应用构建器</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.IPValidationMiddleware">
            <summary>
            IP验证中间件
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.IPValidationMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            IP验证中间件
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.IPValidationMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            中间件处理方法
            </summary>
            <param name="context">HTTP上下文</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.IPValidationMiddleware.GetClientIpAddress(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            获取客户端IP地址
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.IPValidationMiddlewareExtensions">
            <summary>
            IP验证中间件扩展方法
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.PermissionMiddleware">
            <summary>
            权限中间件
            用于验证用户权限和身份认证，确保只有授权用户能访问受保护的API端点
            </summary>
            <param name="next">请求处理管道中的下一个中间件</param>
            <param name="permissionService">权限服务</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,BLL.SysService.SysPermissionService)">
            <summary>
            权限中间件
            用于验证用户权限和身份认证，确保只有授权用户能访问受保护的API端点
            </summary>
            <param name="next">请求处理管道中的下一个中间件</param>
            <param name="permissionService">权限服务</param>
        </member>
        <member name="F:DataMgrSystem.Controllers.Middleware.PermissionMiddleware._next">
            <summary>
            请求处理管道中的下一个中间件
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.Middleware.PermissionMiddleware._permissionService">
            <summary>
            权限服务，用于获取和验证用户权限
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.USER_LOGIN_STATUS_PREFIX">
            <summary>
            用户登录状态在Redis中的键前缀
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            中间件处理方法，验证用户权限并处理请求
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.ValidateOpenPermissionAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            开放接口验证
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>验证是否成功</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.TryGetPropertyValue``1(System.Object,System.Type,System.String,``0@)">
            <summary>
            尝试获取对象属性值
            </summary>
            <typeparam name="T">返回值类型</typeparam>
            <param name="obj">目标对象</param>
            <param name="type">对象类型</param>
            <param name="propertyName">属性名</param>
            <param name="value">输出属性值</param>
            <returns>是否成功获取</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.ValidatePlayerToken(System.String,System.String)">
            <summary>
            验证玩家Token
            </summary>
            <param name="playerId">玩家ID</param>
            <param name="playerToken">玩家Token</param>
            <returns>验证是否通过</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.ValidatePermissionAsync(Microsoft.AspNetCore.Http.HttpContext,DataMgrSystem.Controllers.Middleware.PermissionMiddleware.EndpointAttributes)">
            <summary>
            身份和权限验证
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="attributes">权限特性</param>
            <returns>验证是否成功</returns>        
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.IsAnonymousEndpoint(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            检查终结点是否允许匿名访问
            </summary>
            <param name="endpoint">HTTP终结点</param>
            <returns>如果允许匿名访问则返回true，否则返回false</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.GetEndpointAttributes(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            获取终结点上的权限特性
            </summary>
            <param name="endpoint">HTTP终结点</param>
            <returns>包含权限特性和功能权限特性的对象</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.ValidateUserAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            验证用户身份并获取用户信息
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>包含用户信息和令牌的元组</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.CreateClaimsPrincipal(Common.JWT.UserInfo)">
            <summary>
            创建用户身份主体
            </summary>
            <param name="userInfo">用户信息</param>
            <returns>用户身份主体</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.ValidateFunctionPermissionAsync(System.String,DataMgrSystem.Controllers.Attributes.FunctionPermissionAttribute)">
            <summary>
            验证用户是否具有特定功能权限
            </summary>
            <param name="userId">用户ID</param>
            <param name="attribute">功能权限特性</param>
            <returns>如果用户具有权限则返回true，否则返回false</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.HandleUnauthorized(Microsoft.AspNetCore.Http.HttpContext,System.String)">
            <summary>
            处理未授权响应（401）
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="message">错误消息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.HandleForbidden(Microsoft.AspNetCore.Http.HttpContext,System.String)">
            <summary>
            处理禁止访问响应（403）
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="message">错误消息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.WriteErrorResponse(Microsoft.AspNetCore.Http.HttpContext,System.Int32,System.String,System.Int32)">
            <summary>
            写入HTTP响应
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="code">业务状态码</param>
            <param name="message">响应消息</param>
            <param name="statusCode">HTTP状态码</param>
            <returns>异步任务</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.EndpointAttributes">
            <summary>
            获取终结点上的权限特性
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.EndpointAttributes.PermissionAttribute">
            <summary>
            权限特性
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.EndpointAttributes.FunctionPermissionAttribute">
            <summary>
            功能权限特性
            </summary>
        </member>
        <member name="P:DataMgrSystem.Controllers.Middleware.PermissionMiddleware.EndpointAttributes.CardOpenValidationAttribute">
            <summary>
            卡片开通验证特性
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.PermissionMiddlewareExtensions">
            <summary>
            权限中间件扩展方法
            提供将权限中间件添加到应用程序请求管道的扩展方法
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.PermissionMiddlewareExtensions.UsePermissionValidation(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            向应用程序请求管道添加权限验证中间件
            </summary>
            <param name="builder">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.Middleware.RequestSizeVerifyMiddleWare">
            <summary>
            验证请求是否超过设置的最大值
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.RequestSizeVerifyMiddleWare.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            验证请求是否超过设置的最大值
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.Middleware.RequestSizeVerifyMiddleWare.Verify(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            验证请求是否超过设置的最大值
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>是否验证通过</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.AuthController">
            <summary>
            认证控制器 - 处理用户认证相关的请求
            </summary>
            <remarks>
            构造函数 - 依赖注入用户服务和短信登录服务
            </remarks>
            <param name="userService">用户服务实例</param>
            <param name="smsLoginService">短信登录服务实例</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.AuthController.#ctor(BLL.SysService.SysUserService,BLL.SysService.SmsLoginService,BLL.SysService.SysLogService)">
            <summary>
            认证控制器 - 处理用户认证相关的请求
            </summary>
            <remarks>
            构造函数 - 依赖注入用户服务和短信登录服务
            </remarks>
            <param name="userService">用户服务实例</param>
            <param name="smsLoginService">短信登录服务实例</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.AuthController._userService">
            <summary>
            用户服务接口
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.AuthController._smsLoginService">
            <summary>
            短信登录服务接口
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.AuthController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.AuthController.Login(Entity.Dto.LoginRequestDto)">
            <summary>
            用户登录接口
            </summary>
            <param name="loginRequest">登录请求DTO，包含用户名和密码</param>
            <returns>
            返回登录响应结果:
            - 成功: 返回200状态码和用户Token信息
            - 失败: 返回500状态码和错误信息
            </returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.AuthController.Logout">
            <summary>
            用户登出接口
            </summary>
            <returns>登出结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.AuthController.SendSmsCode(System.String)">
            <summary>
            发送短信验证码
            </summary>
            <param name="phoneNumber">手机号码</param>
            <returns>发送结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.AuthController.SmsLogin(Entity.Dto.SmsLoginDto)">
            <summary>
            使用短信验证码登录
            </summary>
            <param name="dto">登录信息，包含手机号和验证码</param>
            <returns>登录结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.AuthController.GetUserInfo">
            <summary>
            获取当前登录用户信息接口
            </summary>
            <returns>
            返回用户信息结果:
            - 成功: 返回200状态码和用户详细信息
            - 未授权: 返回401状态码
            - 失败: 返回500状态码和错误信息
            </returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.AuthController.GetModelStateErrors">
            <summary>
            获取ModelState中的错误信息
            </summary>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.ButtonController">
            <summary>
            按钮管理控制器
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.ButtonController.#ctor(BLL.SysService.SysButtonService,BLL.SysService.SysLogService)">
            <summary>
            按钮管理控制器
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.ButtonController._buttonService">
            <summary>
            按钮服务接口
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.ButtonController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.ButtonController.SyncButtons">
            <summary>
            重新获取权限数据
            </summary>
            <returns>操作结果</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.LogController">
            <summary>
            系统日志控制器
            提供日志查询、详情和清理等功能
            </summary>
            <remarks>
            构造函数
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.LogController.#ctor(BLL.SysService.SysLogService)">
            <summary>
            系统日志控制器
            提供日志查询、详情和清理等功能
            </summary>
            <remarks>
            构造函数
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.LogController._logService">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.LogController.QueryAsync(DAL.SysDAL.SysLogDAL.Queryable)">
            <summary>
            分页查询日志列表
            </summary>
            <param name="queryable">查询条件</param>
            <returns>日志列表</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.LogController.GetAsync(System.String)">
            <summary>
            获取日志详情
            </summary>
            <param name="id">日志ID</param>
            <returns>日志详情</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.LogController.ExportLogsAsync(DAL.SysDAL.SysLogDAL.Queryable,BLL.SysService.Exports.ExportRequestDto)">
            <summary>
            导出日志列表
            </summary>
            <param name="queryable">查询条件</param>
            <param name="exportRequest">导出配置</param>
            <returns>CSV文件</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.LogController.ClearLogsAsync(Entity.Dto.LogClearDto)">
            <summary>
            清理指定日期之前的日志
            </summary>
            <param name="clearDto">清理日志参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.MenuController">
            <summary>
            菜单管理控制器
            提供菜单的CRUD操作、菜单树查询、用户菜单权限管理等功能
            </summary>
            <remarks>
            构造函数,通过依赖注入注入菜单服务
            </remarks>
            <param name="menuService">菜单服务实例</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.#ctor(BLL.SysService.SysMenuService,BLL.SysService.SysLogService)">
            <summary>
            菜单管理控制器
            提供菜单的CRUD操作、菜单树查询、用户菜单权限管理等功能
            </summary>
            <remarks>
            构造函数,通过依赖注入注入菜单服务
            </remarks>
            <param name="menuService">菜单服务实例</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.MenuController._menuService">
            <summary>
            菜单服务接口
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.MenuController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.CreateAsync(Entity.Dto.CreateMenuDto)">
            <summary>
            创建新菜单
            </summary>
            <param name="input">创建菜单请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.UpdateAsync(Entity.Dto.UpdateMenuDto)">
            <summary>
            更新现有菜单信息
            </summary>
            <param name="input">更新菜单请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.DeleteAsync(System.String)">
            <summary>
            删除指定菜单
            </summary>
            <param name="id">菜单ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.GetAsync(System.String)">
            <summary>
            获取指定菜单的详细信息
            </summary>
            <param name="id">菜单ID</param>
            <returns>菜单详细信息</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.GetTreeAsync(DAL.SysDAL.SysMenuDAL.Queryable)">
            <summary>
            获取菜单树结构
            </summary>
            <param name="query">菜单查询条件</param>
            <returns>菜单树结构</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.GetSelfUserMenuTreeAsync">
            <summary>
            获取指定用户的菜单树
            </summary>
            <returns>当前用户菜单树</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.MenuController.FlattenMenuTree(System.Collections.Generic.List{Entity.Dto.MenuDto})">
            <summary>
            将菜单树扁平化为列表
            </summary>
            <param name="menuTree">菜单树</param>
            <returns>扁平化的菜单列表</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.PermissionController">
            <summary>
            权限管理控制器
            用于管理系统的权限相关功能,包括菜单权限、角色权限等
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.PermissionController.#ctor(BLL.SysService.SysPermissionService,BLL.SysService.SysLogService)">
            <summary>
            权限管理控制器
            用于管理系统的权限相关功能,包括菜单权限、角色权限等
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.PermissionController._permissionService">
            <summary>
            权限服务接口
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.PermissionController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.PermissionController.GetSelfAllPermissionsAsync">
            <summary>
            获取当前用户所有权限
            </summary>
            <returns></returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.PermissionController.GetUserAllPermissionsAsync(System.String)">
            <summary>
            获取用户所有权限
            </summary>
            <param name="userId">用户ID</param>
            <returns>返回用户所有权限数据</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.PermissionController.GetUserPermissionsAsync(System.String)">
            <summary>
            获取用户权限
            </summary>
            <param name="userId">用户ID</param>
            <returns>返回用户权限数据</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.PermissionController.ClearPermissionCache">
            <summary>
            清空权限缓存
            </summary>
            <returns></returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.PermissionController.GetRolePermissionsAsync(System.String)">
            <summary>
            获取角色权限
            </summary>
            <param name="roleId">角色ID</param>
            <returns>返回角色权限数据</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.PermissionController.AssignPermissionsAsync(Entity.Dto.AssignPermissions_ReqDto)">
            <summary>
            分配权限
            </summary>
            <param name="assignPermissions">权限分配参数</param>
            <returns>返回分配结果</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.RoleController">
            <summary>
            角色管理控制器
            提供角色的增删改查、菜单权限分配等管理功能
            </summary>
            <remarks>
            构造函数,通过依赖注入注入角色服务
            </remarks>
            <param name="roleService">角色服务实例</param>
            <param name="logService"></param>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.RoleController.#ctor(BLL.SysService.SysRoleService,BLL.SysService.SysLogService)">
            <summary>
            角色管理控制器
            提供角色的增删改查、菜单权限分配等管理功能
            </summary>
            <remarks>
            构造函数,通过依赖注入注入角色服务
            </remarks>
            <param name="roleService">角色服务实例</param>
            <param name="logService"></param>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.RoleController._roleService">
            <summary>
            角色服务接口
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.RoleController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.RoleController.CreateAsync(Entity.Dto.CreateRoleDto)">
            <summary>
            创建新角色
            </summary>
            <param name="input">创建角色请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.RoleController.UpdateAsync(Entity.Dto.UpdateRoleDto)">
            <summary>
            更新现有角色信息
            </summary>
            <param name="input">更新角色请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.RoleController.DeleteAsync(System.String)">
            <summary>
            删除指定角色
            </summary>
            <param name="id">角色ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.RoleController.GetAsync(System.String)">
            <summary>
            获取角色详细信息
            </summary>
            <param name="id">角色ID</param>
            <returns>角色详细信息</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.RoleController.GetPageAsync(DAL.SysDAL.SysRoleDAL.Queryable)">
            <summary>
            分页查询角色列表
            </summary>
            <param name="queryable">分页查询请求</param>
            <returns>角色列表</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.SysDictionaryController">
            <summary>
            系统字典表控制器
            </summary>
            <remarks>
            构造函数
            </remarks>
            <param name="sysDictionaryBLL">系统字典表业务逻辑层</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.#ctor(BLL.SysService.SysDictionaryBLL,BLL.SysService.SysLogService)">
            <summary>
            系统字典表控制器
            </summary>
            <remarks>
            构造函数
            </remarks>
            <param name="sysDictionaryBLL">系统字典表业务逻辑层</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.SysDictionaryController._sysDictionaryBLL">
            <summary>
            系统字典表业务逻辑层
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.SysDictionaryController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.GetAllAsync">
            <summary>
            获取所有字典列表
            </summary>
            <returns>字典列表</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.GetByTypeCodeAsync(System.String)">
            <summary>
            根据字典类型码获取字典项
            </summary>
            <param name="dictTypeCode">字典类型码</param>
            <returns>字典项列表</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.GetDropdownAsync(System.String)">
            <summary>
            根据字典类型码获取下拉框数据
            </summary>
            <param name="dictTypeCode">字典类型码</param>
            <returns>下拉框数据</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.GetAllTypesAsync">
            <summary>
            获取所有字典类型（父级字典）
            </summary>
            <returns>父级字典列表结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.QueryAsync(DAL.SysDAL.SysDictionaryDAL.Queryable)">
            <summary>
            分页查询字典列表
            </summary>
            <param name="queryable">分页查询请求</param>
            <returns>字典列表</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.CreateAsync(Entity.Dto.SysDictionaryCreateDto)">
            <summary>
            添加字典项
            </summary>
            <param name="createDto">字典项创建DTO</param>
            <returns>添加结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.BatchCreateAsync(System.Collections.Generic.List{Entity.Dto.SysDictionaryCreateDto})">
            <summary>
            批量添加字典项
            </summary>
            <param name="createDtos">字典项创建DTO列表</param>
            <returns>添加结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.UpdateAsync(Entity.Dto.SysDictionaryUpdateDto)">
            <summary>
            更新字典项
            </summary>
            <param name="updateDto">字典项更新DTO</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.DeleteAsync(System.String)">
            <summary>
            删除字典项
            </summary>
            <param name="id">字典项ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.DeleteByTypeCodeAsync(System.String)">
            <summary>
            根据字典类型码删除字典项
            </summary>
            <param name="dictTypeCode">字典类型码</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.CreateParentDictionaryAsync(Entity.Dto.SysDictionaryCreateDto)">
            <summary>
            添加父级字典（字典类型）
            </summary>
            <param name="createDto">父级字典创建DTO</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.SysDictionaryController.DeleteParentDictionaryAsync(System.String)">
            <summary>
            删除父级字典（字典类型）及其下所有子字典项
            </summary>
            <param name="parentId">父级字典ID</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:DataMgrSystem.Controllers.SysControllers.UserController">
            <summary>
            用户管理控制器
            提供用户的增删改查、角色分配、密码管理等功能
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.#ctor(BLL.SysService.SysUserService,BLL.SysService.SysLogService)">
            <summary>
            用户管理控制器
            提供用户的增删改查、角色分配、密码管理等功能
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.UserController._userService">
            <summary>
            用户服务接口
            </summary>
        </member>
        <member name="F:DataMgrSystem.Controllers.SysControllers.UserController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.CreateAsync(Entity.Dto.CreateUserDto)">
            <summary>
            创建新用户
            </summary>
            <param name="input">创建用户请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.UpdateAsync(Entity.Dto.UpdateUserDto)">
            <summary>
            更新用户信息
            </summary>
            <param name="input">更新用户请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.DeleteAsync(System.String)">
            <summary>
            删除指定用户
            </summary>
            <param name="id">用户ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.GetAsync(System.String)">
            <summary>
            获取用户详细信息
            </summary>
            <param name="id">用户ID</param>
            <returns>用户详细信息</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.QueryAsync(DAL.SysDAL.SysUserDAL.UserDALQuery)">
            <summary>
            分页查询用户列表
            </summary>
            <param name="queryable">分页查询请求</param>
            <returns>用户列表</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.ChangePasswordAsync(Entity.Dto.ChangePasswordDto)">
            <summary>
            修改用户密码
            </summary>
            <param name="input">修改密码请求</param>
            <returns>修改结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.ResetPasswordAsync(Entity.Dto.ResetPasswordDto)">
            <summary>
            重置用户密码
            </summary>
            <param name="input">重置密码请求</param>
            <returns>重置结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.AssignRolesAsync(System.String,System.Collections.Generic.List{System.String})">
            <summary>
            为用户分配角色
            </summary>
            <param name="userId">用户ID</param>
            <param name="roleIds">角色ID列表</param>
            <returns>分配结果</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.GetUserRolesAsync(System.String)">
            <summary>
            获取用户已分配的角色列表
            </summary>
            <param name="userId">用户ID</param>
            <returns>用户角色列表</returns>
        </member>
        <member name="M:DataMgrSystem.Controllers.SysControllers.UserController.ExportUsersAsync(DAL.SysDAL.SysUserDAL.UserDALQuery,BLL.SysService.Exports.ExportRequestDto)">
            <summary>
            导出用户列表
            </summary>
            <param name="query">查询条件</param>
            <param name="exportRequest">导出配置</param>
            <returns>CSV文件</returns>
        </member>
        <member name="T:DataMgrSystem.Infrastructure.AutofacModule">
            <summary>
            Autofac模块
            </summary>
        </member>
        <member name="M:DataMgrSystem.Infrastructure.AutofacModule.Load(Autofac.ContainerBuilder)">
            <summary>
            加载Autofac模块
            </summary>
            <param name="builder">容器构建器</param>
        </member>
    </members>
</doc>
