{"openapi": "3.0.1", "info": {"title": "DataMgrSystem", "version": "1.0"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登录接口", "requestBody": {"description": "登录请求DTO，包含用户名和密码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}}}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登出接口", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Auth/sendSmsCode": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "发送短信验证码", "parameters": [{"name": "phoneNumber", "in": "query", "description": "手机号码", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Auth/smsLogin": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "使用短信验证码登录", "requestBody": {"description": "登录信息，包含手机号和验证码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SmsLoginDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}}}}}}, "/api/Auth/userinfo": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取当前登录用户信息接口", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserInfoDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserInfoDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserInfoDtoResult"}}}}}}}, "/api/Basis/UploadFileAsync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "本地单文件上传", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Basis/UploadFilesAsync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "本地多文件上传", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Button/sync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "重新获取权限数据", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/CardBatch/create": {"post": {"tags": ["CardBatch"], "summary": "创建卡片批次", "requestBody": {"description": "创建卡片批次请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBatchDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBatchDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBatchDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/CardBatch/activate": {"post": {"tags": ["CardBatch"], "summary": "批次激活", "requestBody": {"description": "批次激活请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivateCardBatchDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActivateCardBatchDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ActivateCardBatchDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/CardBatch/ship": {"post": {"tags": ["CardBatch"], "summary": "批次出货", "requestBody": {"description": "批次出货请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShipCardBatchDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ShipCardBatchDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ShipCardBatchDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/CardBatch/query": {"get": {"tags": ["CardBatch"], "summary": "批次查询", "parameters": [{"name": "BatchNo", "in": "query", "schema": {"type": "string"}}, {"name": "Channel", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"type": "string"}}, {"name": "GameCurrency", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ActualValue", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BatchDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatchDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchDtoPageEntityResult"}}}}}}}, "/api/CardBatch/getInfo": {"get": {"tags": ["CardBatch"], "summary": "批次详情", "parameters": [{"name": "BatchNo", "in": "query", "schema": {"type": "string"}}, {"name": "Channel", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"type": "string"}}, {"name": "GameCurrency", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ActualValue", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BatchDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BatchDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BatchDtoResult"}}}}}}}, "/api/CardBatch/delete": {"post": {"tags": ["CardBatch"], "summary": "删除批次", "parameters": [{"name": "batchNo", "in": "query", "description": "批次编号", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/CardHome/statistics": {"get": {"tags": ["CardHome"], "summary": "获取首页统计数据", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/HomeStatistics_ResDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/HomeStatistics_ResDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HomeStatistics_ResDtoResult"}}}}}}}, "/api/CardHome/monthly-statistics": {"get": {"tags": ["CardHome"], "summary": "获取月度统计数据", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MonthlyStatistics_ResDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MonthlyStatistics_ResDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MonthlyStatistics_ResDtoListResult"}}}}}}}, "/api/CardInfo/query": {"get": {"tags": ["CardInfo"], "summary": "卡片查询", "parameters": [{"name": "CardNo", "in": "query", "schema": {"type": "string"}}, {"name": "BatchNo", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Holder<PERSON>d", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "AcquireTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UseTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "GameCurrency", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ActualValue", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Price", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardDtoPageEntityResult"}}}}}}}, "/api/CardInfo/export": {"post": {"tags": ["CardInfo"], "summary": "导出卡信息", "parameters": [{"name": "CardNo", "in": "query", "schema": {"type": "string"}}, {"name": "BatchNo", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Holder<PERSON>d", "in": "query", "schema": {"type": "string"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "AcquireTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UseTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "GameCurrency", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ActualValue", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Price", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CardOpenApi/TransferCard": {"post": {"tags": ["CardOpenApi"], "summary": "转移卡片", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferCard_ReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferCard_ReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TransferCard_ReqDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/CardOpenApi/UseTheCard": {"post": {"tags": ["CardOpenApi"], "summary": "使用卡片", "requestBody": {"description": "使用卡片请求DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UseTheCard_ReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UseTheCard_ReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UseTheCard_ReqDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/CardOpenApi/GetCardInfo": {"post": {"tags": ["CardOpenApi"], "summary": "查询用户拥有的卡信息", "requestBody": {"description": "获取卡片信息请求DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCardInfoAsync_ReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetCardInfoAsync_ReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetCardInfoAsync_ReqDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OpenApiGetCardInfoAsync_ResDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OpenApiGetCardInfoAsync_ResDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OpenApiGetCardInfoAsync_ResDtoListResult"}}}}}}}, "/api/CardOpenApi/GetCardTransferRecord": {"post": {"tags": ["CardOpenApi"], "summary": "查询指定用户的 转出卡和使用卡日志", "requestBody": {"description": "查询指定用户的 转出卡和使用卡日志请求DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCardTransferRecordAsync_ReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetCardTransferRecordAsync_ReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetCardTransferRecordAsync_ReqDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardTransferRecordDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardTransferRecordDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardTransferRecordDtoListResult"}}}}}}}, "/api/CardOperationRecord/transfer/records": {"get": {"tags": ["CardOperationRecord"], "summary": "流转记录查询", "parameters": [{"name": "OrderId", "in": "query", "schema": {"type": "string"}}, {"name": "Operator", "in": "query", "schema": {"type": "string"}}, {"name": "TargetId", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardTransferRecordDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardTransferRecordDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardTransferRecordDtoPageEntityResult"}}}}}}}, "/api/CardOperationRecord/import/template": {"get": {"tags": ["CardOperationRecord"], "summary": "获取卡片流转记录导入模板", "responses": {"200": {"description": "Success"}}}}, "/api/CardOperationRecord/import": {"post": {"tags": ["CardOperationRecord"], "summary": "导入卡片流转记录", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ImportResultDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ImportResultDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ImportResultDtoResult"}}}}}}}, "/api/CardOperationRecord/export": {"post": {"tags": ["CardOperationRecord"], "summary": "导出流转记录", "parameters": [{"name": "OrderId", "in": "query", "schema": {"type": "string"}}, {"name": "Operator", "in": "query", "schema": {"type": "string"}}, {"name": "TargetId", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CardOperationRecord/GetUserCardSummaryAsync": {"post": {"tags": ["CardOperationRecord"], "summary": "获取用户转卡记录汇总", "requestBody": {"description": "用户卡片查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserCardSummaryDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserCardSummaryDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCardSummaryDtoListResult"}}}}}}}, "/api/CardOperationRecord/GetUserSentCardDetailsAsync": {"post": {"tags": ["CardOperationRecord"], "summary": "获取用户赠送卡详细信息", "requestBody": {"description": "用户卡片查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardOperationDetailDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardOperationDetailDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardOperationDetailDtoListResult"}}}}}}}, "/api/CardOperationRecord/GetUserRechargedCardDetailsAsync": {"post": {"tags": ["CardOperationRecord"], "summary": "获取用户充值卡详细信息", "requestBody": {"description": "用户卡片查询条件", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCardQueryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardOperationDetailDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardOperationDetailDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardOperationDetailDtoListResult"}}}}}}}, "/api/CardShippingRecord/Create": {"post": {"tags": ["CardShippingRecord"], "summary": "创建发货记录", "requestBody": {"description": "创建发货记录请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShippingDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateShippingDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateShippingDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/CardShippingRecord/GetAllPage": {"get": {"tags": ["CardShippingRecord"], "summary": "查询发货记录", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "BatchNo", "in": "query", "schema": {"type": "string"}}, {"name": "TargetId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "ShippingTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ShippingTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Operator", "in": "query", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardShippingRecordPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardShippingRecordPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardShippingRecordPageEntityResult"}}}}}}}, "/api/CardShippingTargets/GetAllPage": {"get": {"tags": ["CardShippingTargets"], "summary": "查询发货目标", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "TargetId", "in": "query", "schema": {"type": "string"}}, {"name": "Channel", "in": "query", "schema": {"type": "string"}}, {"name": "TargetName", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatorName", "in": "query", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardShippingTargetPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardShippingTargetPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardShippingTargetPageEntityResult"}}}}}}}, "/api/CardShippingTargets/GetInfo/{targetId}": {"get": {"tags": ["CardShippingTargets"], "summary": "根据ID查询发货目标", "parameters": [{"name": "targetId", "in": "path", "description": "目标ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardShippingTargetResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardShippingTargetResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardShippingTargetResult"}}}}}}}, "/api/CardShippingTargets/Update": {"post": {"tags": ["CardShippingTargets"], "summary": "更新发货目标", "requestBody": {"description": "更新发货目标请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateShippingTargetDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateShippingTargetDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateShippingTargetDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/CardShippingTargets/Delete/{targetId}": {"post": {"tags": ["CardShippingTargets"], "summary": "删除发货目标", "parameters": [{"name": "targetId", "in": "path", "description": "目标ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/CardShippingTargets/Create": {"post": {"tags": ["CardShippingTargets"], "summary": "创建发货目标", "requestBody": {"description": "创建发货目标请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateShippingTargetDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateShippingTargetDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateShippingTargetDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/CardShippingTargets/export": {"post": {"tags": ["CardShippingTargets"], "summary": "导出发货目标列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "TargetId", "in": "query", "schema": {"type": "string"}}, {"name": "Channel", "in": "query", "schema": {"type": "string"}}, {"name": "TargetName", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatorName", "in": "query", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/CardValueConfig/create": {"post": {"tags": ["CardValueConfig"], "summary": "创建渠道卡配置", "requestBody": {"description": "创建渠道卡配置请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChannelCardValueConfigDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateChannelCardValueConfigDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateChannelCardValueConfigDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/CardValueConfig/update": {"post": {"tags": ["CardValueConfig"], "summary": "更新渠道卡配置", "requestBody": {"description": "更新渠道卡配置请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChannelCardValueConfigDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateChannelCardValueConfigDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateChannelCardValueConfigDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/CardValueConfig/query": {"get": {"tags": ["CardValueConfig"], "summary": "分页查询渠道卡配置", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Channel", "in": "query", "schema": {"type": "string"}}, {"name": "GameCurrency", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "ActualValue", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "CardType", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinFaceValue", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxFaceValue", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CreateTimeStart", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreateTimeEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatorName", "in": "query", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CardValueConfigPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CardValueConfigPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CardValueConfigPageEntityResult"}}}}}}}, "/api/CardValueConfig/delete/{id}": {"post": {"tags": ["CardValueConfig"], "summary": "删除渠道卡配置", "parameters": [{"name": "id", "in": "path", "description": "配置ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/ConfigTest/GetRedisConfig": {"get": {"tags": ["ConfigTest"], "summary": "获取Redis配置", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ConfigCompareResultResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ConfigCompareResultResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ConfigCompareResultResult"}}}}}}}, "/api/Log/query": {"get": {"tags": ["Log"], "summary": "分页查询日志列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "schema": {"type": "string"}}, {"name": "Operation", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "LogType", "in": "query", "schema": {"type": "string"}}, {"name": "LogLevel", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysLogPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysLogPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysLogPageEntityResult"}}}}}}}, "/api/Log/{id}": {"get": {"tags": ["Log"], "summary": "获取日志详情", "parameters": [{"name": "id", "in": "path", "description": "日志ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysLogResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysLogResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysLogResult"}}}}}}}, "/api/Log/export": {"post": {"tags": ["Log"], "summary": "导出日志列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "schema": {"type": "string"}}, {"name": "Operation", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "LogType", "in": "query", "schema": {"type": "string"}}, {"name": "LogLevel", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Log/clear": {"post": {"tags": ["Log"], "summary": "清理指定日期之前的日志", "requestBody": {"description": "清理日志参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Menu/create": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "创建新菜单", "requestBody": {"description": "创建菜单请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/Menu/update": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "更新现有菜单信息", "requestBody": {"description": "更新菜单请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Menu/delete/{id}": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "删除指定菜单", "parameters": [{"name": "id", "in": "path", "description": "菜单ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Menu/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取指定菜单的详细信息", "parameters": [{"name": "id", "in": "path", "description": "菜单ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoResult"}}}}}}}, "/api/Menu/tree": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取菜单树结构", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "Ids", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "ParentId", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderByOrderNum", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Menu/GetSelfUserMenuTreeAsync": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取指定用户的菜单树", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/self/all": {"get": {"tags": ["Permission"], "summary": "获取当前用户所有权限", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/user/{userId}/all": {"get": {"tags": ["Permission"], "summary": "获取用户所有权限", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/user/{userId}": {"get": {"tags": ["Permission"], "summary": "获取用户权限", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/clear/cache": {"get": {"tags": ["Permission"], "summary": "清空权限缓存", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Permission/role/{roleId}": {"get": {"tags": ["Permission"], "summary": "获取角色权限", "parameters": [{"name": "roleId", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/assign": {"post": {"tags": ["Permission"], "summary": "分配权限", "requestBody": {"description": "权限分配参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignPermissions_ReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignPermissions_ReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignPermissions_ReqDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Role/create": {"post": {"tags": ["Role"], "summary": "创建新角色", "requestBody": {"description": "创建角色请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/Role/update": {"post": {"tags": ["Role"], "summary": "更新现有角色信息", "requestBody": {"description": "更新角色请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Role/delete/{id}": {"post": {"tags": ["Role"], "summary": "删除指定角色", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Role/{id}": {"get": {"tags": ["Role"], "summary": "获取角色详细信息", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}}}}}}, "/api/Role/query": {"get": {"tags": ["Role"], "summary": "分页查询角色列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderByOrderNum", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoPageEntityResult"}}}}}}}, "/api/SysDictionary/list": {"get": {"tags": ["SysDictionary"], "summary": "获取所有字典列表", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}}}}}}, "/api/SysDictionary/by-type/{dictTypeCode}": {"get": {"tags": ["SysDictionary"], "summary": "根据字典类型码获取字典项", "parameters": [{"name": "dictTypeCode", "in": "path", "description": "字典类型码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}}}}}}, "/api/SysDictionary/dropdown/{dictTypeCode}": {"get": {"tags": ["SysDictionary"], "summary": "根据字典类型码获取下拉框数据", "parameters": [{"name": "dictTypeCode", "in": "path", "description": "字典类型码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DictionaryDropdownDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DictionaryDropdownDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictionaryDropdownDtoListResult"}}}}}}}, "/api/SysDictionary/types": {"get": {"tags": ["SysDictionary"], "summary": "获取所有字典类型（父级字典）", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DictionaryTypeDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DictionaryTypeDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictionaryTypeDtoListResult"}}}}}}}, "/api/SysDictionary/query": {"get": {"tags": ["SysDictionary"], "summary": "分页查询字典列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "ParentId", "in": "query", "schema": {"type": "string"}}, {"name": "DictTypeCode", "in": "query", "schema": {"type": "string"}}, {"name": "DictTypeName", "in": "query", "schema": {"type": "string"}}, {"name": "DictItemCode", "in": "query", "schema": {"type": "string"}}, {"name": "DictItemName", "in": "query", "schema": {"type": "string"}}, {"name": "IsEnabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderBySortOrder", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntityResult"}}}}}}}, "/api/SysDictionary/create": {"post": {"tags": ["SysDictionary"], "summary": "添加字典项", "requestBody": {"description": "字典项创建DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/batch-create": {"post": {"tags": ["SysDictionary"], "summary": "批量添加字典项", "requestBody": {"description": "字典项创建DTO列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/update": {"post": {"tags": ["SysDictionary"], "summary": "更新字典项", "requestBody": {"description": "字典项更新DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictionaryUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/delete/{id}": {"post": {"tags": ["SysDictionary"], "summary": "删除字典项", "parameters": [{"name": "id", "in": "path", "description": "字典项ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/delete-by-type/{dictTypeCode}": {"post": {"tags": ["SysDictionary"], "summary": "根据字典类型码删除字典项", "parameters": [{"name": "dictTypeCode", "in": "path", "description": "字典类型码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/create-parent": {"post": {"tags": ["SysDictionary"], "summary": "添加父级字典（字典类型）", "requestBody": {"description": "父级字典创建DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/delete-parent/{parentId}": {"post": {"tags": ["SysDictionary"], "summary": "删除父级字典（字典类型）及其下所有子字典项", "parameters": [{"name": "parentId", "in": "path", "description": "父级字典ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/User/create": {"post": {"tags": ["User"], "summary": "创建新用户", "requestBody": {"description": "创建用户请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/User/update": {"post": {"tags": ["User"], "summary": "更新用户信息", "requestBody": {"description": "更新用户请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/delete/{id}": {"post": {"tags": ["User"], "summary": "删除指定用户", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "summary": "获取用户详细信息", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoResult"}}}}}}}, "/api/User/query": {"get": {"tags": ["User"], "summary": "分页查询用户列表", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "RealName", "in": "query", "schema": {"type": "string"}}, {"name": "Mobile", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoPageEntityResult"}}}}}}}, "/api/User/change-password": {"post": {"tags": ["User"], "summary": "修改用户密码", "requestBody": {"description": "修改密码请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/reset-password": {"post": {"tags": ["User"], "summary": "重置用户密码", "requestBody": {"description": "重置密码请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/{userId}/assign-roles": {"post": {"tags": ["User"], "summary": "为用户分配角色", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "角色ID列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/{userId}/roles": {"get": {"tags": ["User"], "summary": "获取用户已分配的角色列表", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysRoleListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysRoleListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysRoleListResult"}}}}}}}, "/api/User/export": {"post": {"tags": ["User"], "summary": "导出用户列表", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "RealName", "in": "query", "schema": {"type": "string"}}, {"name": "Mobile", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"ActivateCardBatchDto": {"required": ["batchNo"], "type": "object", "properties": {"batchNo": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "AssignPermissions_ReqDto": {"type": "object", "properties": {"subjectId": {"maxLength": 32, "type": "string", "nullable": true}, "permissionCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BatchDto": {"type": "object", "properties": {"batchNo": {"type": "string", "nullable": true}, "channel": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "gameCurrency": {"type": "integer", "format": "int64"}, "actualValue": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "quantity": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "targetId": {"type": "string", "nullable": true}, "targetName": {"type": "string", "nullable": true}, "creatorName": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "updaterName": {"type": "string", "nullable": true}, "activateTime": {"type": "string", "format": "date-time", "nullable": true}, "activatorName": {"type": "string", "nullable": true}, "shippingTime": {"type": "string", "format": "date-time", "nullable": true}, "shippingOperatorName": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BatchDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/BatchDto"}, "nullable": true}}, "additionalProperties": false}, "BatchDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/BatchDtoPageEntity"}}, "additionalProperties": false}, "BatchDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/BatchDto"}}, "additionalProperties": false}, "BooleanResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "boolean", "description": "接口返回数据"}}, "additionalProperties": false}, "ButtonDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "permissionCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CardDto": {"type": "object", "properties": {"cardNo": {"type": "string", "nullable": true}, "batchNo": {"type": "string", "nullable": true}, "gameCurrency": {"type": "integer", "format": "int64"}, "actualValue": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "status": {"type": "string", "nullable": true}, "useTime": {"type": "string", "format": "date-time", "nullable": true}, "usePlayerId": {"type": "string", "nullable": true}, "holderId": {"type": "string", "nullable": true}, "acquireTime": {"type": "string", "format": "date-time", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CardDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CardDto"}, "nullable": true}}, "additionalProperties": false}, "CardDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/CardDtoPageEntity"}}, "additionalProperties": false}, "CardOperationDetailDto": {"type": "object", "properties": {"operatorId": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "operationDate": {"type": "string", "nullable": true}, "faceValues": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "operationCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CardOperationDetailDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CardOperationDetailDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "CardShippingRecord": {"type": "object", "properties": {"remark": {"type": "string", "nullable": true}, "createdBy": {"maxLength": 32, "type": "string", "nullable": true}, "creatorName": {"maxLength": 50, "type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updatedBy": {"maxLength": 32, "type": "string", "nullable": true}, "updaterName": {"maxLength": 50, "type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "batchNo": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "shippingTime": {"type": "string", "format": "date-time"}, "operator": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CardShippingRecordPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CardShippingRecord"}, "nullable": true}}, "additionalProperties": false}, "CardShippingRecordPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/CardShippingRecordPageEntity"}}, "additionalProperties": false}, "CardShippingTarget": {"type": "object", "properties": {"remark": {"type": "string", "nullable": true}, "createdBy": {"maxLength": 32, "type": "string", "nullable": true}, "creatorName": {"maxLength": 50, "type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updatedBy": {"maxLength": 32, "type": "string", "nullable": true}, "updaterName": {"maxLength": 50, "type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "targetId": {"type": "string", "nullable": true}, "channel": {"type": "string", "nullable": true}, "targetName": {"type": "string", "nullable": true}, "targetDesc": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CardShippingTargetPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CardShippingTarget"}, "nullable": true}}, "additionalProperties": false}, "CardShippingTargetPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/CardShippingTargetPageEntity"}}, "additionalProperties": false}, "CardShippingTargetResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/CardShippingTarget"}}, "additionalProperties": false}, "CardTransferRecordDto": {"type": "object", "properties": {"recordId": {"type": "string", "nullable": true}, "orderId": {"type": "string", "nullable": true}, "operator": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "operationCount": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double", "nullable": true}, "ip": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "CardTransferRecordDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CardTransferRecordDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "CardTransferRecordDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CardTransferRecordDto"}, "nullable": true}}, "additionalProperties": false}, "CardTransferRecordDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/CardTransferRecordDtoPageEntity"}}, "additionalProperties": false}, "CardValueConfig": {"type": "object", "properties": {"remark": {"type": "string", "nullable": true}, "createdBy": {"maxLength": 32, "type": "string", "nullable": true}, "creatorName": {"maxLength": 50, "type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updatedBy": {"maxLength": 32, "type": "string", "nullable": true}, "updaterName": {"maxLength": 50, "type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "channel": {"type": "string", "nullable": true}, "cardType": {"type": "string", "nullable": true}, "gameCurrency": {"type": "integer", "format": "int64"}, "actualValue": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CardValueConfigPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/CardValueConfig"}, "nullable": true}}, "additionalProperties": false}, "CardValueConfigPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/CardValueConfigPageEntity"}}, "additionalProperties": false}, "ChangePasswordDto": {"required": ["newPassword", "oldPassword", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "oldPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "ConfigCompareResult": {"type": "object", "properties": {"fromStatic": {"description": "从静态类读取的配置", "nullable": true}}, "additionalProperties": false, "description": "配置比较结果"}, "ConfigCompareResultResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/ConfigCompareResult"}}, "additionalProperties": false}, "CreateBatchDto": {"type": "object", "properties": {"channel": {"type": "string", "nullable": true}, "channelCardValueConfigID": {"type": "integer", "format": "int32"}, "shippingTargetID": {"type": "integer", "format": "int32"}, "quantity": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateChannelCardValueConfigDto": {"type": "object", "properties": {"channel": {"type": "string", "nullable": true}, "cardType": {"type": "string", "nullable": true}, "gameCurrency": {"type": "integer", "format": "int64"}, "actualValue": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateMenuDto": {"type": "object", "properties": {"parentId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "component": {"type": "string", "nullable": true}, "perms": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}, "orderNum": {"type": "integer", "format": "int32"}, "visible": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateRoleDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateShippingDto": {"type": "object", "properties": {"batchNo": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "operator": {"type": "string", "nullable": true}, "targetId": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateShippingTargetDto": {"type": "object", "properties": {"targetId": {"type": "string", "nullable": true}, "channel": {"type": "string", "nullable": true}, "targetName": {"type": "string", "nullable": true}, "targetDesc": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUserDto": {"required": ["password", "userName"], "type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "userName": {"maxLength": 50, "minLength": 1, "type": "string"}, "password": {"maxLength": 100, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "DictionaryDropdownDto": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "disabled": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DictionaryDropdownDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DictionaryDropdownDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "DictionaryTypeDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "DictionaryTypeDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DictionaryTypeDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ExportColumnDto": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "propertyName": {"type": "string", "nullable": true}, "order": {"type": "integer", "format": "int32"}, "format": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExportRequestDto": {"type": "object", "properties": {"columns": {"type": "array", "items": {"$ref": "#/components/schemas/ExportColumnDto"}, "nullable": true}, "fileName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetCardInfoAsync_ReqDto": {"type": "object", "properties": {"sign": {"type": "string", "nullable": true}, "tick": {"type": "integer", "format": "int64"}, "playerToken": {"type": "string", "nullable": true}, "playerId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetCardTransferRecordAsync_ReqDto": {"type": "object", "properties": {"sign": {"type": "string", "nullable": true}, "tick": {"type": "integer", "format": "int64"}, "playerToken": {"type": "string", "nullable": true}, "playerId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HomeStatistics_ResDto": {"type": "object", "properties": {"batchCount": {"type": "integer", "description": "批次总数", "format": "int32"}, "cardCount": {"type": "integer", "description": "卡片总数", "format": "int32"}}, "additionalProperties": false, "description": "首页统计数据"}, "HomeStatistics_ResDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/HomeStatistics_ResDto"}}, "additionalProperties": false}, "ImportResultDto": {"type": "object", "properties": {"totalCount": {"type": "integer", "description": "总记录数", "format": "int32"}, "successCount": {"type": "integer", "description": "成功导入数量", "format": "int32"}, "failCount": {"type": "integer", "description": "失败数量", "format": "int32"}, "errors": {"type": "array", "items": {"type": "string"}, "description": "错误信息列表", "nullable": true}}, "additionalProperties": false, "description": "导入结果数据传输对象"}, "ImportResultDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/ImportResultDto"}}, "additionalProperties": false}, "LogClearDto": {"required": ["beforeDate"], "type": "object", "properties": {"beforeDate": {"type": "string", "format": "date-time"}, "logType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequestDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LoginResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "userInfo": {"$ref": "#/components/schemas/UserInfoDto"}}, "additionalProperties": false}, "LoginResponseDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/LoginResponseDto"}}, "additionalProperties": false}, "MenuDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "parentId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "component": {"type": "string", "nullable": true}, "perms": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}, "orderNum": {"type": "integer", "format": "int32"}, "visible": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}, "nullable": true}, "buttons": {"type": "array", "items": {"$ref": "#/components/schemas/ButtonDto"}, "nullable": true}}, "additionalProperties": false}, "MenuDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "MenuDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/MenuDto"}}, "additionalProperties": false}, "MonthlyStatistics_ResDto": {"type": "object", "properties": {"month": {"type": "string", "description": "月份", "nullable": true}, "batchCount": {"type": "integer", "description": "批次总数", "format": "int32"}, "cardCount": {"type": "integer", "description": "卡片总数", "format": "int32"}}, "additionalProperties": false}, "MonthlyStatistics_ResDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MonthlyStatistics_ResDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "OpenApiGetCardInfoAsync_ResDto": {"type": "object", "properties": {"gameCurrency": {"type": "string", "nullable": true}, "count": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "OpenApiGetCardInfoAsync_ResDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/OpenApiGetCardInfoAsync_ResDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ResetPasswordDto": {"required": ["newPassword", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "Result": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}}, "additionalProperties": false, "description": "接口返回规范"}, "RoleDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "RoleDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}, "nullable": true}}, "additionalProperties": false}, "RoleDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/RoleDtoPageEntity"}}, "additionalProperties": false}, "RoleDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/RoleDto"}}, "additionalProperties": false}, "ShipCardBatchDto": {"required": ["batchNo"], "type": "object", "properties": {"batchNo": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "SmsLoginDto": {"required": ["phoneNumber", "verificationCode"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "pattern": "^1[3-9]\\d{9}$", "type": "string"}, "verificationCode": {"maxLength": 6, "minLength": 4, "type": "string"}}, "additionalProperties": false}, "StringResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "string", "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "SysDictionaryCreateDto": {"type": "object", "properties": {"dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "dictItemCode": {"type": "string", "nullable": true}, "dictItemName": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "parentId": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysDictionaryDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "dictItemCode": {"type": "string", "nullable": true}, "dictItemName": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "parentId": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysDictionaryDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "SysDictionaryDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryDto"}, "nullable": true}}, "additionalProperties": false}, "SysDictionaryDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntity"}}, "additionalProperties": false}, "SysDictionaryUpdateDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "dictItemCode": {"type": "string", "nullable": true}, "dictItemName": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "isEnabled": {"type": "boolean", "nullable": true}, "remark": {"type": "string", "nullable": true}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysLog": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "operation": {"type": "string", "nullable": true}, "method": {"type": "string", "nullable": true}, "params": {"type": "string", "nullable": true}, "time": {"type": "integer", "format": "int64"}, "ip": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "logType": {"type": "string", "nullable": true}, "logLevel": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "exception": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SysLogPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/SysLog"}, "nullable": true}}, "additionalProperties": false}, "SysLogPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysLogPageEntity"}}, "additionalProperties": false}, "SysLogResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysLog"}}, "additionalProperties": false}, "SysRole": {"required": ["code", "name"], "type": "object", "properties": {"remark": {"type": "string", "nullable": true}, "createdBy": {"maxLength": 32, "type": "string", "nullable": true}, "creatorName": {"maxLength": 50, "type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updatedBy": {"maxLength": 32, "type": "string", "nullable": true}, "updaterName": {"maxLength": 50, "type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"maxLength": 32, "type": "string", "nullable": true}, "name": {"minLength": 1, "type": "string"}, "code": {"minLength": 1, "type": "string"}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SysRoleListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SysRole"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "TransferCard_ReqDto": {"required": ["gameCurrency", "targetId", "transferCount"], "type": "object", "properties": {"sign": {"type": "string", "nullable": true}, "tick": {"type": "integer", "format": "int64"}, "playerToken": {"type": "string", "nullable": true}, "playerId": {"type": "string", "nullable": true}, "targetId": {"minLength": 1, "type": "string"}, "gameCurrency": {"type": "integer", "format": "int64"}, "transferCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateChannelCardValueConfigDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "actualValue": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateMenuDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "parentId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "component": {"type": "string", "nullable": true}, "perms": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}, "orderNum": {"type": "integer", "format": "int32"}, "visible": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateRoleDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "menuIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateShippingTargetDto": {"type": "object", "properties": {"targetId": {"type": "string", "nullable": true}, "targetName": {"type": "string", "nullable": true}, "targetDesc": {"type": "string", "nullable": true}, "channel": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateUserDto": {"required": ["userId"], "type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "userId": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "UseTheCard_ReqDto": {"required": ["gameCurrency", "hallId", "targetId", "useCount"], "type": "object", "properties": {"sign": {"type": "string", "nullable": true}, "tick": {"type": "integer", "format": "int64"}, "playerToken": {"type": "string", "nullable": true}, "playerId": {"type": "string", "nullable": true}, "hallId": {"minLength": 1, "type": "string"}, "gameCurrency": {"type": "integer", "format": "int64"}, "targetId": {"minLength": 1, "type": "string"}, "useCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserCardQueryDto": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "startTime": {"type": "string", "format": "date-time", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserCardSummaryDto": {"type": "object", "properties": {"faceValue": {"type": "string", "nullable": true}, "receivedCount": {"type": "integer", "format": "int32"}, "sentCount": {"type": "integer", "format": "int32"}, "rechargedCount": {"type": "integer", "format": "int32"}, "remainingCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UserCardSummaryDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UserCardSummaryDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "id": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "roleCode": {"type": "string", "nullable": true}, "lastLoginTime": {"type": "string", "format": "date-time", "nullable": true}, "lastLoginIp": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}}, "additionalProperties": false}, "UserDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/UserDtoPageEntity"}}, "additionalProperties": false}, "UserDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "UserInfoDto": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "nickName": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}, "nullable": true}}, "additionalProperties": false}, "UserInfoDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/UserInfoDto"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "在下框中输入请求头中需要添加Jwt授权Token：Bearer Token", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}