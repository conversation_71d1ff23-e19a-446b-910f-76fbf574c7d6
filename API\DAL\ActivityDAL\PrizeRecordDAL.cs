using Common.Autofac;
using DAL.Databases;
using Entity;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 中奖记录数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class PrizeRecordDAL(MyContext context) : BaseQueryDLL<ActivityPrizeRecord, PrizeRecordDAL.PrizeRecordQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 中奖记录查询条件模型类
        /// </summary>
        public class PrizeRecordQuery : PageQueryEntity
        {
            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ActivityId { get; set; }

            /// <summary>
            /// 玩家游戏用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? PlayerGameUserId { get; set; }

            /// <summary>
            /// 奖励类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public RewardType? RewardType { get; set; }

            /// <summary>
            /// 中奖状态
            /// </summary>
            [Query(QueryOperator.等于)]
            public PrizeStatus? Status { get; set; }
        }

        /// <summary>
        /// 获取活动中奖记录
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID（可选）</param>
        /// <param name="limit">记录数限制</param>
        /// <returns>中奖记录列表</returns>
        public async Task<List<ActivityPrizeRecord>> GetActivityPrizeRecordsAsync(int activityId, string? playerGameUserId = null, int limit = 50)
        {
            var query = _context.PrizeRecords
                .Where(p => p.ActivityId == activityId);

            if (!string.IsNullOrEmpty(playerGameUserId))
            {
                query = query.Where(p => p.PlayerGameUserId == playerGameUserId);
            }

            return await query
                .OrderByDescending(p => p.CreateTime)
                .Take(limit)
                .ToListAsync();
        }

        /// <summary>
        /// 获取待发放的中奖记录
        /// </summary>
        /// <param name="activityId">活动ID（可选）</param>
        /// <returns>待发放中奖记录列表</returns>
        public async Task<List<ActivityPrizeRecord>> GetPendingPrizeRecordsAsync(int? activityId = null)
        {
            var query = _context.PrizeRecords
                .Where(p => p.Status == PrizeStatus.pending);

            if (activityId.HasValue)
            {
                query = query.Where(p => p.ActivityId == activityId.Value);
            }

            return await query
                .OrderBy(p => p.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 更新中奖记录状态
        /// </summary>
        /// <param name="prizeId">中奖记录ID</param>
        /// <param name="status">新状态</param>
        /// <param name="processNote">处理备注</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdatePrizeStatusAsync(int prizeId, PrizeStatus status, string? processNote = null)
        {
            var prize = await _context.PrizeRecords.FindAsync(prizeId);
            if (prize == null)
                return false;

            prize.Status = status;
            prize.ProcessNote = processNote;
            prize.ProcessTime = DateTime.Now;
            prize.UpdateTime = DateTime.Now;

            _context.PrizeRecords.Update(prize);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量更新中奖记录状态
        /// </summary>
        /// <param name="prizeIds">中奖记录ID列表</param>
        /// <param name="status">新状态</param>
        /// <param name="processNote">处理备注</param>
        /// <returns>更新的记录数</returns>
        public async Task<int> BatchUpdatePrizeStatusAsync(List<int> prizeIds, PrizeStatus status, string? processNote = null)
        {
            if (prizeIds.Count == 0)
                return 0;

            var prizes = await _context.PrizeRecords
                .Where(p => prizeIds.Contains(p.Id))
                .ToListAsync();

            var now = DateTime.Now;
            foreach (var prize in prizes)
            {
                prize.Status = status;
                prize.ProcessNote = processNote;
                prize.ProcessTime = now;
                prize.UpdateTime = now;
            }

            _context.PrizeRecords.UpdateRange(prizes);
            return await _context.SaveChangesAsync();
        }

        /// <summary>
        /// 获取玩家中奖统计
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>中奖统计信息</returns>
        public async Task<PlayerPrizeStats> GetPlayerPrizeStatsAsync(int activityId, string playerGameUserId)
        {
            var prizes = await _context.PrizeRecords
                .Where(p => p.ActivityId == activityId && p.PlayerGameUserId == playerGameUserId)
                .ToListAsync();

            return new PlayerPrizeStats
            {
                TotalPrizes = prizes.Count,
                TotalTongbaoAmount = prizes.Sum(p => p.TongbaoAmount),
                PhysicalItemCount = prizes.Count(p => !string.IsNullOrEmpty(p.PhysicalItem)),
                PendingCount = prizes.Count(p => p.Status == PrizeStatus.pending),
                CompletedCount = prizes.Count(p => p.Status == PrizeStatus.distributed)
            };
        }

        /// <summary>
        /// 获取活动中奖统计
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>活动中奖统计信息</returns>
        public async Task<ActivityPrizeStats> GetActivityPrizeStatsAsync(int activityId)
        {
            var prizes = await _context.PrizeRecords
                .Where(p => p.ActivityId == activityId)
                .ToListAsync();

            var uniqueWinners = prizes.Select(p => p.PlayerGameUserId).Distinct().Count();

            return new ActivityPrizeStats
            {
                TotalPrizes = prizes.Count,
                UniqueWinners = uniqueWinners,
                TotalTongbaoAmount = prizes.Sum(p => p.TongbaoAmount),
                PhysicalItemCount = prizes.Count(p => !string.IsNullOrEmpty(p.PhysicalItem)),
                PendingCount = prizes.Count(p => p.Status == PrizeStatus.pending),
                CompletedCount = prizes.Count(p => p.Status == PrizeStatus.distributed)
            };
        }

        /// <summary>
        /// 获取实物奖品中奖记录
        /// </summary>
        /// <param name="activityId">活动ID（可选）</param>
        /// <param name="status">状态（可选）</param>
        /// <returns>实物奖品中奖记录列表</returns>
        public async Task<List<ActivityPrizeRecord>> GetPhysicalItemPrizesAsync(int? activityId = null, PrizeStatus? status = null)
        {
            var query = _context.PrizeRecords
                .Where(p => !string.IsNullOrEmpty(p.PhysicalItem));

            if (activityId.HasValue)
            {
                query = query.Where(p => p.ActivityId == activityId.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(p => p.Status == status.Value);
            }

            return await query
                .OrderByDescending(p => p.CreateTime)
                .ToListAsync();
        }
    }

    /// <summary>
    /// 玩家中奖统计信息
    /// </summary>
    public class PlayerPrizeStats
    {
        /// <summary>
        /// 总中奖次数
        /// </summary>
        public int TotalPrizes { get; set; }

        /// <summary>
        /// 总中奖通宝金额
        /// </summary>
        public decimal TotalTongbaoAmount { get; set; }

        /// <summary>
        /// 实物奖品数量
        /// </summary>
        public int PhysicalItemCount { get; set; }

        /// <summary>
        /// 待发放数量
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 已完成数量
        /// </summary>
        public int CompletedCount { get; set; }
    }

    /// <summary>
    /// 活动中奖统计信息
    /// </summary>
    public class ActivityPrizeStats
    {
        /// <summary>
        /// 总中奖次数
        /// </summary>
        public int TotalPrizes { get; set; }

        /// <summary>
        /// 中奖人数
        /// </summary>
        public int UniqueWinners { get; set; }

        /// <summary>
        /// 总中奖通宝金额
        /// </summary>
        public decimal TotalTongbaoAmount { get; set; }

        /// <summary>
        /// 实物奖品数量
        /// </summary>
        public int PhysicalItemCount { get; set; }

        /// <summary>
        /// 待发放数量
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 已完成数量
        /// </summary>
        public int CompletedCount { get; set; }
    }
}
