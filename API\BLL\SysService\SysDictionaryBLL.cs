using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Entity.Extensions;
using static DAL.Databases.EFHelper;

namespace BLL.SysService
{
    /// <summary>
    /// 系统字典表业务逻辑层
    /// 提供字典相关的业务逻辑处理
    /// </summary>
    public class SysDictionaryBLL(SysDictionaryDAL sysDictionaryDAL)
    {
        /// <summary>
        /// 字典数据访问层实例
        /// </summary>
        private readonly SysDictionaryDAL _sysDictionaryDAL = sysDictionaryDAL;

        /// <summary>
        /// 获取字典项信息
        /// </summary>
        /// <param name="id">字典项ID</param>
        /// <returns>字典项信息</returns>
        public async Task<SysDictionaryDto> GetInfoAsync(string id)
        {
            var dictionary = await _sysDictionaryDAL.GetFirstAsync(new SysDictionaryDAL.Queryable { Id = id });
            if (dictionary == null) return new();

            return new SysDictionaryDto
            {
                Id = dictionary.Id,
                DictTypeCode = dictionary.DictTypeCode,
                DictTypeName = dictionary.DictTypeName,
                DictItemCode = dictionary.DictItemCode,
                DictItemName = dictionary.DictItemName,
                SortOrder = dictionary.SortOrder,
                IsEnabled = dictionary.IsEnabled,
            };
        }

        /// <summary>
        /// 获取所有字典列表
        /// </summary>
        /// <returns>字典列表</returns>
        public async Task<List<SysDictionaryDto>> GetAllDictionariesAsync()
        {
            var dictionaries = await _sysDictionaryDAL.GetAllDictionariesAsync();
            return [.. dictionaries.Select(x => new SysDictionaryDto
            {
                Id = x.Id,
                DictTypeCode = x.DictTypeCode,
                DictTypeName = x.DictTypeName,
                DictItemCode = x.DictItemCode,
                DictItemName = x.DictItemName,
                SortOrder = x.SortOrder,
                IsEnabled = x.IsEnabled,
                ParentId = x.ParentId ?? string.Empty,
                Remark = x.Remark ?? string.Empty,
                ExtendField1 = x.ExtendField1 ?? string.Empty,
                ExtendField2 = x.ExtendField2 ?? string.Empty
            })];
        }

        /// <summary>
        /// 根据字典类型码获取字典项
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>字典项列表</returns>
        public async Task<List<SysDictionaryDto>> GetDictionariesByTypeCodeAsync(string dictTypeCode)
        {
            var dictionaries = await _sysDictionaryDAL.GetDictionariesByTypeCodeAsync(dictTypeCode);
            return [.. dictionaries.Where(x => x.ParentId != null && x.ParentId != string.Empty).Select(x => new SysDictionaryDto
            {
                Id = x.Id,
                DictTypeCode = x.DictTypeCode,
                DictTypeName = x.DictTypeName,
                DictItemCode = x.DictItemCode,
                DictItemName = x.DictItemName,
                SortOrder = x.SortOrder,
                IsEnabled = x.IsEnabled,
                ParentId = x.ParentId ?? string.Empty,
                Remark = x.Remark ?? string.Empty,
                ExtendField1 = x.ExtendField1 ?? string.Empty,
                ExtendField2 = x.ExtendField2 ?? string.Empty
            })];
        }

        /// <summary>
        /// 根据字典类型码获取下拉框数据
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>下拉框数据</returns>
        public async Task<List<DictionaryDropdownDto>> GetDropdownListByTypeCodeAsync(string dictTypeCode)
        {
            var dictionaries = await _sysDictionaryDAL.GetDictionariesByTypeCodeAsync(dictTypeCode);
            return [.. dictionaries.Select(x => new DictionaryDropdownDto
            {
                Value = x.DictItemCode,
                Label = x.DictItemName,
                Disabled = !x.IsEnabled,
                SortOrder = x.SortOrder,
                ExtendField1 = x.ExtendField1 ?? string.Empty,
                ExtendField2 = x.ExtendField2 ?? string.Empty
            })];
        }

        /// <summary>
        /// 分页获取字典列表
        /// </summary>
        /// <param name="queryable">分页查询参数</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<SysDictionaryDto>> GetPageAsync(SysDictionaryDAL.Queryable queryable)
        {
            var result = await _sysDictionaryDAL.GetPageAsync(queryable);

            return new PageEntity<SysDictionaryDto>
            {
                List = result.List?.Select(x => new SysDictionaryDto
                {
                    Id = x.Id,
                    DictTypeCode = x.DictTypeCode,
                    DictTypeName = x.DictTypeName,
                    DictItemCode = x.DictItemCode,
                    DictItemName = x.DictItemName,
                    SortOrder = x.SortOrder,
                    IsEnabled = x.IsEnabled,
                    ParentId = x.ParentId ?? string.Empty,
                    Remark = x.Remark ?? string.Empty,
                    ExtendField1 = x.ExtendField1 ?? string.Empty,
                    ExtendField2 = x.ExtendField2 ?? string.Empty
                }).ToList(),
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }

        /// <summary>
        /// 添加字典项
        /// </summary>
        /// <param name="createDto">字典项创建DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>添加结果</returns>
        public async Task<bool> AddDictionaryAsync(SysDictionaryCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            var dictionary = new SysDictionary
            {
                DictTypeCode = createDto.DictTypeCode,
                DictTypeName = createDto.DictTypeName,
                DictItemCode = createDto.DictItemCode,
                DictItemName = createDto.DictItemName,
                SortOrder = createDto.SortOrder,
                IsEnabled = createDto.IsEnabled,
                ParentId = createDto.ParentId,
                Remark = createDto.Remark,
                ExtendField1 = createDto.ExtendField1,
                ExtendField2 = createDto.ExtendField2,
            };
            dictionary.InitializeForAdd(currentUserInfo);

            return await _sysDictionaryDAL.AddDictionaryAsync(dictionary);
        }

        /// <summary>
        /// 批量添加字典项
        /// </summary>
        /// <param name="createDtos">字典项创建DTO列表</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>添加结果</returns>
        public async Task<bool> AddDictionariesAsync(List<SysDictionaryCreateDto> createDtos, CurrentUserInfoDto currentUserInfo)
        {
            var dictionaries = createDtos.Select(x => new SysDictionary
            {
                DictTypeCode = x.DictTypeCode,
                DictTypeName = x.DictTypeName,
                DictItemCode = x.DictItemCode,
                DictItemName = x.DictItemName,
                SortOrder = x.SortOrder,
                IsEnabled = x.IsEnabled,
                ParentId = x.ParentId,
                Remark = x.Remark,
                ExtendField1 = x.ExtendField1,
                ExtendField2 = x.ExtendField2,
                CreateTime = DateTime.Now,
                CreatedBy = currentUserInfo.UserId,
                CreatorName = currentUserInfo.UserName

            }).ToList();

            return await _sysDictionaryDAL.AddDictionariesAsync(dictionaries);
        }

        /// <summary>
        /// 更新字典项（支持级联更新字典类型码）
        /// 1. 当更新父级字典时，自动级联更新所有子项的DictTypeCode
        /// 2. 支持部分字段更新（使用null值表示不更新该字段）
        /// </summary>
        /// <param name="updateDto">字典项更新数据传输对象，包含：
        /// - Id: 必填，要更新的字典项ID
        /// - DictTypeCode: 字典类型码（更新父级时会级联更新子项）
        /// - 其他可更新字段：DictItemCode, DictItemName, SortOrder等
        /// </param>
        /// <returns>更新操作结果，true表示成功</returns>
        /// <exception cref="Exception">当指定ID的字典项不存在时抛出</exception>
        public async Task<bool> UpdateDictionaryAsync(SysDictionaryUpdateDto updateDto, CurrentUserInfoDto currentUserInfo)
        {
            // 1. 根据ID查询目标字典项
            var dictionary = await _sysDictionaryDAL.GetFirstAsync(
                new SysDictionaryDAL.Queryable { Id = updateDto.Id })
                ?? throw new Exception("字典项不存在");

            // 2. 处理父级字典的级联更新
            if (string.IsNullOrEmpty(dictionary.ParentId))
            {
                // 2.1 获取所有子项并更新类型码
                var children = await _sysDictionaryDAL.GetListAsync(
                    new SysDictionaryDAL.Queryable { ParentId = dictionary.Id });

                // 2.2 批量更新子项（使用LINQ优化循环）
                var updates = children.Select(child =>
                {
                    child.DictTypeCode = updateDto.DictTypeCode;
                    child.UpdateTime = DateTime.Now;
                    return child;
                }).ToList();

                // 2.3 添加父项到更新列表
                dictionary.InitializeForUpdate(currentUserInfo);
                dictionary.DictTypeCode = updateDto.DictTypeCode;
                updates.Add(dictionary);

                // 2.4 批量执行更新
                await _sysDictionaryDAL.UpdateRangeAsync(updates);
            }

            // 3. 更新目标字典项字段（空值不更新）
            dictionary.DictTypeCode = updateDto.DictTypeCode ?? dictionary.DictTypeCode;
            dictionary.DictItemCode = updateDto.DictItemCode ?? dictionary.DictItemCode;
            dictionary.DictItemName = updateDto.DictItemName ?? dictionary.DictItemName;
            dictionary.SortOrder = updateDto.SortOrder ?? dictionary.SortOrder;
            dictionary.IsEnabled = updateDto.IsEnabled ?? dictionary.IsEnabled;
            dictionary.Remark = updateDto.Remark ?? dictionary.Remark;
            dictionary.ExtendField1 = updateDto.ExtendField1 ?? dictionary.ExtendField1;
            dictionary.ExtendField2 = updateDto.ExtendField2 ?? dictionary.ExtendField2;
            dictionary.UpdateTime = DateTime.Now;

            // 4. 提交更新到数据访问层
            return await _sysDictionaryDAL.UpdateDictionaryAsync(dictionary);
        }

        /// <summary>
        /// 删除字典项
        /// </summary>
        /// <param name="id">字典项ID</param>
        /// <returns>删除结果</returns>
        public Task<bool> DeleteDictionaryAsync(string id)
        => _sysDictionaryDAL.DeleteDictionaryAsync(id);


        /// <summary>
        /// 根据字典类型码删除字典项
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>删除结果</returns>
        public Task<bool> DeleteDictionariesByTypeCodeAsync(string dictTypeCode)
        => _sysDictionaryDAL.DeleteDictionariesByTypeCodeAsync(dictTypeCode);

        /// <summary>
        /// 获取所有字典类型
        /// </summary>
        /// <returns>字典类型列表</returns>
        public async Task<List<DictionaryTypeDto>> GetAllDictionaryTypesAsync()
        {
            var types = await _sysDictionaryDAL.GetAllDictionaryTypesAsync();
            return [.. types.Select(x => new DictionaryTypeDto
            {
                DictTypeCode = x.DictTypeCode,
                DictTypeName = x.DictTypeName
            })];
        }

        /// <summary>
        /// 获取所有父级字典（字典类型）
        /// </summary>
        /// <returns>父级字典列表</returns>
        public async Task<List<DictionaryTypeDto>> GetAllParentDictionariesAsync()
        {
            var dictionaries = await _sysDictionaryDAL.GetAllParentDictionariesAsync();

            return [.. dictionaries.Select(d => new DictionaryTypeDto
            {
                Id = d.Id,
                DictTypeCode = d.DictTypeCode,
                DictTypeName = d.DictTypeName,
                Remark = d.Remark ?? string.Empty,
                SortOrder = d.SortOrder,
                IsEnabled = d.IsEnabled,
                CreateTime = d.CreateTime,
                UpdateTime = d.UpdateTime
            })];
        }

        /// <summary>
        /// 添加父级字典
        /// </summary>
        /// <param name="createDto">父级字典创建DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否添加成功</returns>
        public async Task<bool> AddParentDictionaryAsync(SysDictionaryCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            var dictionary = new SysDictionary
            {
                Id = Guid.NewGuid().ToString("N"),
                DictTypeCode = createDto.DictTypeCode,
                DictTypeName = createDto.DictTypeName,
                Remark = createDto.Remark,
                SortOrder = createDto.SortOrder,
                IsEnabled = createDto.IsEnabled,
            };
            dictionary.InitializeForAdd(currentUserInfo);

            return await _sysDictionaryDAL.AddParentDictionaryAsync(dictionary);
        }

        /// <summary>
        /// 删除父级字典及其所有子字典
        /// </summary>
        /// <param name="parentId">父级字典ID</param>
        /// <returns>是否删除成功</returns>
        public Task<bool> DeleteParentDictionaryWithChildrenAsync(string parentId)
        => _sysDictionaryDAL.DeleteParentDictionaryWithChildrenAsync(parentId);
    }
}