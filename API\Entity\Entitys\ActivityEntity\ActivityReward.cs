using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 活动奖励配置表
/// </summary>
[Table("activity_rewards")]
public class ActivityReward : BaseEntity_ID
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [Required]
    [Column("activity_id")]
    [Comment("活动ID")]
    public int ActivityId { get; set; }

    /// <summary>
    /// 奖励名称
    /// </summary>
    [Required]
    [MaxLength(200)]
    [Column("reward_name")]
    [Comment("奖励名称")]
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 奖励类型：通宝/实物
    /// </summary>
    [Required]
    [Column("reward_type")]
    [Comment("奖励类型：通宝/实物")]
    public RewardType RewardType { get; set; }

    /// <summary>
    /// 通宝数量
    /// </summary>
    [Column("tongbao_amount", TypeName = "decimal(15,2)")]
    [Comment("通宝数量")]
    public decimal TongbaoAmount { get; set; } = 0;

    /// <summary>
    /// 实物描述
    /// </summary>
    [MaxLength(500)]
    [Column("physical_item")]
    [Comment("实物描述")]
    public string? PhysicalItem { get; set; }

    /// <summary>
    /// 权重
    /// </summary>
    [Required]
    [Column("weight")]
    [Comment("权重")]
    public int Weight { get; set; }

    /// <summary>
    /// 中奖概率
    /// </summary>
    [Required]
    [Column("probability", TypeName = "decimal(8,6)")]
    [Comment("中奖概率")]
    public decimal Probability { get; set; }

    /// <summary>
    /// 每日份数
    /// </summary>
    [Required]
    [Column("daily_quantity")]
    [Comment("每日份数")]
    public int DailyQuantity { get; set; }

    /// <summary>
    /// 剩余份数
    /// </summary>
    [Required]
    [Column("remaining_quantity")]
    [Comment("剩余份数")]
    public int RemainingQuantity { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [Column("sort_order")]
    [Comment("排序")]
    public int SortOrder { get; set; } = 0;

    /// <summary>
    /// 上次重置时间
    /// </summary>
    [Column("last_reset_time")]
    [Comment("上次重置时间")]
    public DateTime? LastResetTime { get; set; }

}
