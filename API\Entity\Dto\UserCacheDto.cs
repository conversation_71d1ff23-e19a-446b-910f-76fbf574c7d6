using System.ComponentModel.DataAnnotations;

namespace Entity.Dto;

/// <summary>
/// 缓存用户昵称请求DTO
/// </summary>
public class CacheUserNicknameDto
{
    /// <summary>
    /// 游戏用户ID
    /// </summary>
    [Required(ErrorMessage = "游戏用户ID不能为空")]
    [MaxLength(50, ErrorMessage = "游戏用户ID长度不能超过50个字符")]
    public string GameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 昵称
    /// </summary>
    [Required(ErrorMessage = "昵称不能为空")]
    [MaxLength(100, ErrorMessage = "昵称长度不能超过100个字符")]
    public string Nickname { get; set; } = string.Empty;
}

/// <summary>
/// 用户缓存响应DTO
/// </summary>
public class UserCacheResponseDto
{
    /// <summary>
    /// 游戏用户ID
    /// </summary>
    public string GameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 昵称
    /// </summary>
    public string Nickname { get; set; } = string.Empty;
}
