using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 游戏数据同步表
/// </summary>
[Table("activity_game_data_sync")]
public class ActivityGameDataSync : BaseEntity_ID
{
    /// <summary>
    /// 同步日期
    /// </summary>
    [Required]
    [Column("sync_date")]
    [Comment("同步日期")]
    public DateTime SyncDate { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [Required]
    [Column("user_id")]
    [Comment("用户ID")]
    public int UserId { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("nickname")]
    [Comment("昵称")]
    public string Nickname { get; set; } = string.Empty;

    /// <summary>
    /// 联盟ID
    /// </summary>
    [Required]
    [Column("club_id")]
    [Comment("联盟ID")]
    public int ClubId { get; set; }

    /// <summary>
    /// 联盟名称
    /// </summary>
    [Required]
    [MaxLength(200)]
    [Column("club_name")]
    [Comment("联盟名称")]
    public string ClubName { get; set; } = string.Empty;

    /// <summary>
    /// 游戏局数
    /// </summary>
    [Column("game_count")]
    [Comment("游戏局数")]
    public int GameCount { get; set; } = 0;

    /// <summary>
    /// 服务费
    /// </summary>
    [Column("service_fee", TypeName = "decimal(15,2)")]
    [Comment("服务费")]
    public decimal ServiceFee { get; set; } = 0;

    /// <summary>
    /// 在线时长（秒）
    /// </summary>
    [Column("online_duration")]
    [Comment("在线时长（秒）")]
    public int OnlineDuration { get; set; } = 0;

    /// <summary>
    /// 游戏时长（秒）
    /// </summary>
    [Column("game_duration")]
    [Comment("游戏时长（秒）")]
    public int GameDuration { get; set; } = 0;
}
