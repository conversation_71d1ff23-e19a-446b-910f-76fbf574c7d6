<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动神秘盒子 API 测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
        }
        .api-title {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="number"], input[type="text"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .response {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .response.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .response.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .description {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>活动神秘盒子 API 测试页面</h1>
        
        <div class="api-section" style="background-color: #e7f3ff; border: 1px solid #b8daff; padding: 15px; margin-bottom: 20px;">
            <div class="api-title" style="color: #004085; margin-bottom: 10px;">🔧 测试说明</div>
            <div class="description">
                <p><strong>服务器地址：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">https://localhost:7048</code></p>
                <p><strong>使用方法：</strong></p>
                <ol>
                    <li>确保后端服务器已启动（已运行在 https://localhost:7048）</li>
                    <li>访问测试页面：<br><code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px;">https://localhost:7048/test-mystery-box.html?userId=test123</code></li>
                    <li>填写下方表单中的必填参数</li>
                    <li>点击对应按钮测试API</li>
                </ol>
                <p><strong>当前用户ID：</strong> <span id="current-user-id" style="color: #007bff; font-weight: bold;"></span></p>
                <p><strong>API端点：</strong></p>
                <ul style="margin: 5px 0; padding-left: 20px;">
                    <li><code>POST /api/activities/validate-down-user</code></li>
                    <li><code>POST /api/activities/get-today-game-data</code></li>
                    <li><code>POST /api/activities/update-user-point</code></li>
                </ul>
            </div>
        </div>
        
        <!-- API 1: 验证下级用户 -->
        <div class="api-section">
            <div class="api-title">1. 验证用户是否为下级用户</div>
            <div class="description">
                调用存储过程 api_isDownUser，验证指定用户是否为活动创建者的下级用户。
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="validate-clubId">俱乐部ID:</label>
                    <input type="number" id="validate-clubId" placeholder="例如: 59628845" required>
                </div>
                <div class="form-group">
                    <label for="validate-creatorId">创建者ID:</label>
                    <input type="number" id="validate-creatorId" placeholder="例如: 287809" required>
                </div>
                <div class="form-group">
                    <label for="validate-targetUserId">目标用户ID:</label>
                    <input type="number" id="validate-targetUserId" placeholder="例如: 459036" required>
                </div>
            </div>
            <button onclick="validateDownUser()">验证下级用户</button>
            <div id="validate-response" class="response" style="display: none;"></div>
        </div>

        <!-- API 2: 获取今日游戏数据 -->
        <div class="api-section">
            <div class="api-title">2. 获取今日用户游戏数据</div>
            <div class="description">
                调用存储过程 api_getTodayUsersGameData，获取指定俱乐部和创建者的今日游戏数据。
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="gamedata-clubId">俱乐部ID:</label>
                    <input type="number" id="gamedata-clubId" placeholder="例如: 59628845" required>
                </div>
                <div class="form-group">
                    <label for="gamedata-creatorId">创建者ID:</label>
                    <input type="number" id="gamedata-creatorId" placeholder="例如: 287809" required>
                </div>
            </div>
            <button onclick="getTodayGameData()">获取游戏数据</button>
            <div id="gamedata-response" class="response" style="display: none;"></div>
        </div>

        <!-- API 3: 更新用户通宝 -->
        <div class="api-section">
            <div class="api-title">3. 更新用户通宝</div>
            <div class="description">
                调用存储过程 api_updateUserPoint，更新用户通宝。操作类型：1=从用户扣除通宝注入奖池，2=从奖池扣除通宝。
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="point-clubId">俱乐部ID:</label>
                    <input type="number" id="point-clubId" placeholder="例如: 59628845" required>
                </div>
                <div class="form-group">
                    <label for="point-creatorId">创建者ID:</label>
                    <input type="number" id="point-creatorId" placeholder="例如: 287809" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="point-operationType">操作类型:</label>
                    <select id="point-operationType" style="width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="1">1 - 从用户扣除通宝注入奖池</option>
                        <option value="2">2 - 从奖池扣除通宝</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="point-targetUserId">目标用户ID:</label>
                    <input type="number" id="point-targetUserId" placeholder="例如: 459036" required>
                </div>
                <div class="form-group">
                    <label for="point-amount">通宝数量:</label>
                    <input type="number" id="point-amount" placeholder="例如: 100" required>
                </div>
            </div>
            <button onclick="updateUserPoint()">更新通宝</button>
            <div id="point-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 使用绝对路径指向正确的服务器
        const API_BASE_URL = 'https://localhost:7048/api/activities';

        // 从URL参数或默认值获取用户ID
        function getGameUserId() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('userId') || 'test-user-123';
        }

        // 页面加载时显示当前用户ID
        document.addEventListener('DOMContentLoaded', function() {
            const userId = getGameUserId();
            document.getElementById('current-user-id').textContent = userId;
        });

        // 通用的API调用函数
        async function callAPI(endpoint, data, responseElementId) {
            const responseElement = document.getElementById(responseElementId);
            responseElement.style.display = 'block';
            responseElement.className = 'response loading';
            responseElement.textContent = '正在调用API...';

            try {
                const gameUserId = getGameUserId();
                const response = await fetch(`${API_BASE_URL}/${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Game-User-Id': gameUserId
                    },
                    body: JSON.stringify(data)
                });

                let result;
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    result = await response.json();
                } else {
                    const text = await response.text();
                    result = { 
                        status: response.status, 
                        statusText: response.statusText,
                        responseText: text,
                        contentType: contentType
                    };
                }
                
                if (response.ok) {
                    responseElement.className = 'response success';
                    responseElement.textContent = `✅ 调用成功 (${response.status})\n\n${JSON.stringify(result, null, 2)}`;
                } else {
                    responseElement.className = 'response error';
                    responseElement.textContent = `❌ 调用失败 (${response.status})\n\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                responseElement.className = 'response error';
                responseElement.textContent = `❌ 网络错误: ${error.message}\n\n请检查:\n1. 服务器是否运行\n2. API路由是否正确\n3. 网络连接是否正常`;
            }
        }

        // 验证下级用户
        function validateDownUser() {
            const clubId = parseInt(document.getElementById('validate-clubId').value);
            const creatorId = parseInt(document.getElementById('validate-creatorId').value);
            const targetUserId = parseInt(document.getElementById('validate-targetUserId').value);

            if (!clubId || !creatorId || !targetUserId) {
                alert('请填写所有必需的参数！');
                return;
            }

            const data = {
                clubId: clubId,
                creatorId: creatorId,
                targetUserId: targetUserId
            };

            callAPI('validate-down-user', data, 'validate-response');
        }

        // 获取今日游戏数据
        function getTodayGameData() {
            const clubId = parseInt(document.getElementById('gamedata-clubId').value);
            const creatorId = parseInt(document.getElementById('gamedata-creatorId').value);

            if (!clubId || !creatorId) {
                alert('请填写所有必需的参数！');
                return;
            }

            const data = {
                clubId: clubId,
                creatorId: creatorId
            };

            callAPI('get-today-game-data', data, 'gamedata-response');
        }

        // 更新用户通宝
        function updateUserPoint() {
            const clubId = parseInt(document.getElementById('point-clubId').value);
            const creatorId = parseInt(document.getElementById('point-creatorId').value);
            const operationType = parseInt(document.getElementById('point-operationType').value);
            const targetUserId = parseInt(document.getElementById('point-targetUserId').value);
            const pointAmount = parseInt(document.getElementById('point-amount').value);

            if (!clubId || !creatorId || !operationType || !targetUserId || !pointAmount) {
                alert('请填写所有必需的参数！');
                return;
            }

            const data = {
                clubId: clubId,
                creatorId: creatorId,
                operationType: operationType,
                targetUserId: targetUserId,
                pointAmount: pointAmount
            };

            callAPI('update-user-point', data, 'point-response');
        }

        // 页面加载时设置一些默认值
        window.onload = function() {
            // 为验证下级用户设置默认值
            document.getElementById('validate-clubId').value = '368447';
            document.getElementById('validate-creatorId').value = '287809';
            document.getElementById('validate-targetUserId').value = '137468';

            // 为获取游戏数据设置默认值
            document.getElementById('gamedata-clubId').value = '368447';
            document.getElementById('gamedata-creatorId').value = '287809';

            // 为更新通宝设置默认值
            document.getElementById('point-clubId').value = '368447';
            document.getElementById('point-creatorId').value = '287809';
            document.getElementById('point-targetUserId').value = '459036';
            document.getElementById('point-amount').value = '100';
        };
    </script>
</body>
</html>
