using System.ComponentModel.DataAnnotations;

namespace Entity.Dto;

/// <summary>
/// 创建活动请求DTO
/// </summary>
public class CreateActivityDto
{
    /// <summary>
    /// 活动名称
    /// </summary>
    [Required(ErrorMessage = "活动名称不能为空")]
    [MaxLength(200, ErrorMessage = "活动名称长度不能超过200个字符")]
    public string ActivityName { get; set; } = string.Empty;

    /// <summary>
    /// 活动类型
    /// </summary>
    [Required(ErrorMessage = "活动类型不能为空")]
    public ActivityType ActivityType { get; set; }

    /// <summary>
    /// 投入的总通宝
    /// </summary>
    [Required(ErrorMessage = "投入通宝不能为空")]
    [Range(0.01, double.MaxValue, ErrorMessage = "投入通宝必须大于0")]
    public decimal TotalTongbao { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [Required(ErrorMessage = "开始时间不能为空")]
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [Required(ErrorMessage = "结束时间不能为空")]
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 奖励配置
    /// </summary>
    [Required(ErrorMessage = "奖励配置不能为空")]
    public List<CreateActivityRewardDto> Rewards { get; set; } = [];

    /// <summary>
    /// 任务配置
    /// </summary>
    [Required(ErrorMessage = "任务配置不能为空")]
    public List<CreateActivityTaskDto> Tasks { get; set; } = [];
}

/// <summary>
/// 创建活动奖励DTO
/// </summary>
public class CreateActivityRewardDto
{
    /// <summary>
    /// 奖励名称
    /// </summary>
    [Required(ErrorMessage = "奖励名称不能为空")]
    [MaxLength(200, ErrorMessage = "奖励名称长度不能超过200个字符")]
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 奖励类型
    /// </summary>
    [Required(ErrorMessage = "奖励类型不能为空")]
    public RewardType RewardType { get; set; }

    /// <summary>
    /// 通宝数量
    /// </summary>
    public decimal TongbaoAmount { get; set; }

    /// <summary>
    /// 实物描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "实物描述长度不能超过500个字符")]
    public string? PhysicalItem { get; set; }

    /// <summary>
    /// 权重
    /// </summary>
    [Required(ErrorMessage = "权重不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "权重必须大于0")]
    public int Weight { get; set; }

    /// <summary>
    /// 每日份数
    /// </summary>
    [Required(ErrorMessage = "每日份数不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "每日份数必须大于0")]
    public int DailyQuantity { get; set; }
}

/// <summary>
/// 创建活动任务DTO
/// </summary>
public class CreateActivityTaskDto
{
    /// <summary>
    /// 任务类型
    /// </summary>
    [Required(ErrorMessage = "任务类型不能为空")]
    public TaskType TaskType { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    [Required(ErrorMessage = "任务名称不能为空")]
    [MaxLength(200, ErrorMessage = "任务名称长度不能超过200个字符")]
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 目标数值
    /// </summary>
    [Required(ErrorMessage = "目标数值不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "目标数值必须大于0")]
    public int TargetValue { get; set; }

    /// <summary>
    /// 奖励次数
    /// </summary>
    [Required(ErrorMessage = "奖励次数不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "奖励次数必须大于0")]
    public int RewardChances { get; set; }

    /// <summary>
    /// 刷新类型
    /// </summary>
    [Required(ErrorMessage = "刷新类型不能为空")]
    public RefreshType RefreshType { get; set; }
}

/// <summary>
/// 活动列表查询DTO
/// </summary>
public class ActivityQueryDto : BaseQueryDto
{
    /// <summary>
    /// 活动状态
    /// </summary>
    public ActivityStatus? Status { get; set; }

    /// <summary>
    /// 查询范围 (my/all)
    /// </summary>
    public string? Scope { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// 每页数量
    /// </summary>
    public int Limit { get; set; } = 10;
}

/// <summary>
/// 活动响应DTO
/// </summary>
public class ActivityResponseDto
{
    /// <summary>
    /// 活动ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 活动名称
    /// </summary>
    public string ActivityName { get; set; } = string.Empty;

    /// <summary>
    /// 活动类型
    /// </summary>
    public ActivityType ActivityType { get; set; }

    /// <summary>
    /// 投入的总通宝
    /// </summary>
    public decimal TotalTongbao { get; set; }

    /// <summary>
    /// 剩余通宝
    /// </summary>
    public decimal RemainingTongbao { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 活动状态
    /// </summary>
    public ActivityStatus Status { get; set; }

    /// <summary>
    /// 参与人数
    /// </summary>
    public int ParticipantsCount { get; set; }

    /// <summary>
    /// 创建者昵称
    /// </summary>
    public string CreatorNickname { get; set; } = string.Empty;
}

/// <summary>
/// 活动详情响应DTO
/// </summary>
public class ActivityDetailResponseDto : ActivityResponseDto
{
    /// <summary>
    /// 奖励配置
    /// </summary>
    public List<ActivityRewardResponseDto> Rewards { get; set; } = [];

    /// <summary>
    /// 任务配置
    /// </summary>
    public List<ActivityTaskResponseDto> Tasks { get; set; } = [];
}

/// <summary>
/// 活动奖励响应DTO
/// </summary>
public class ActivityRewardResponseDto
{
    /// <summary>
    /// 奖励ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 奖励名称
    /// </summary>
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 奖励类型
    /// </summary>
    public RewardType RewardType { get; set; }

    /// <summary>
    /// 通宝数量
    /// </summary>
    public decimal TongbaoAmount { get; set; }

    /// <summary>
    /// 实物描述
    /// </summary>
    public string? PhysicalItem { get; set; }

    /// <summary>
    /// 中奖概率
    /// </summary>
    public decimal Probability { get; set; }

    /// <summary>
    /// 剩余份数
    /// </summary>
    public int RemainingQuantity { get; set; }
}

/// <summary>
/// 活动任务响应DTO
/// </summary>
public class ActivityTaskResponseDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public TaskType TaskType { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 目标数值
    /// </summary>
    public int TargetValue { get; set; }

    /// <summary>
    /// 奖励次数
    /// </summary>
    public int RewardChances { get; set; }

    /// <summary>
    /// 刷新类型
    /// </summary>
    public RefreshType RefreshType { get; set; }
}
