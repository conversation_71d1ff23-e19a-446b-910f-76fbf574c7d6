using BLL.SysService;
using DAL.SysDAL;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;
using static DAL.Databases.EFHelper;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 系统字典表控制器
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="sysDictionaryBLL">系统字典表业务逻辑层</param>
    /// <param name="logService">日志服务实例</param>
    [Permission]
    public class SysDictionaryController(SysDictionaryBLL sysDictionaryBLL, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 系统字典表业务逻辑层
        /// </summary>
        private readonly SysDictionaryBLL _sysDictionaryBLL = sysDictionaryBLL;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        #region 基础查询操作

        /// <summary>
        /// 获取所有字典列表
        /// </summary>
        /// <returns>字典列表</returns>
        [HttpGet("list")]
        public async Task<Result<List<SysDictionaryDto>>> GetAllAsync()
        => Success(await _sysDictionaryBLL.GetAllDictionariesAsync(), "获取成功");

        /// <summary>
        /// 根据字典类型码获取字典项
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>字典项列表</returns>
        [HttpGet("by-type/{dictTypeCode}")]
        public async Task<Result<List<SysDictionaryDto>>> GetByTypeCodeAsync(string dictTypeCode)
        => Success(await _sysDictionaryBLL.GetDictionariesByTypeCodeAsync(dictTypeCode), "获取成功");

        /// <summary>
        /// 根据字典类型码获取下拉框数据
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>下拉框数据</returns>
        [HttpGet("dropdown/{dictTypeCode}")]
        public async Task<Result<List<DictionaryDropdownDto>>> GetDropdownAsync(string dictTypeCode)
        => Success(await _sysDictionaryBLL.GetDropdownListByTypeCodeAsync(dictTypeCode), "获取成功");

        /// <summary>
        /// 获取所有字典类型（父级字典）
        /// </summary>
        /// <returns>父级字典列表结果</returns>
        [HttpGet("types")]
        public async Task<Result<List<DictionaryTypeDto>>> GetAllTypesAsync()
        => Success(await _sysDictionaryBLL.GetAllParentDictionariesAsync(), "获取成功");

        /// <summary>
        /// 分页查询字典列表
        /// </summary>
        /// <param name="queryable">分页查询请求</param>
        /// <returns>字典列表</returns>
        [HttpGet("query")]
        public async Task<Result<PageEntity<SysDictionaryDto>>> QueryAsync([FromQuery] SysDictionaryDAL.Queryable queryable)
        => Success(await _sysDictionaryBLL.GetPageAsync(queryable), "获取成功");

        #endregion

        #region 字典管理操作

        /// <summary>
        /// 添加字典项
        /// </summary>
        /// <param name="createDto">字典项创建DTO</param>
        /// <returns>添加结果</returns>
        [FunctionPermission("dictionary:create", "创建字典")]
        [HttpPost("create")]
        public async Task<Result<bool>> CreateAsync([FromBody] SysDictionaryCreateDto createDto)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行业务操作
            var result = await _sysDictionaryBLL.AddDictionaryAsync(createDto, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "字典管理",
                Operation = "创建字典",
                BusinessObject = "SysDictionary",
                ObjectId = "N/A",
                DetailedInfo = $"用户 {currentUser.UserName} 成功创建了字典 {createDto.DictItemName}，类型: {createDto.DictTypeCode}",
                AfterData = createDto,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(result, "创建成功");
        }

        /// <summary>
        /// 批量添加字典项
        /// </summary>
        /// <param name="createDtos">字典项创建DTO列表</param>
        /// <returns>添加结果</returns>
        [Log(Module = "字典管理", Description = "批量创建字典", LogParams = true)]
        [FunctionPermission("dictionary:batch-create", "批量创建字典")]
        [HttpPost("batch-create")]
        public async Task<Result<bool>> BatchCreateAsync([FromBody] List<SysDictionaryCreateDto> createDtos)
        => Success(await _sysDictionaryBLL.AddDictionariesAsync(createDtos, GetCurrentUserInfo()), "批量创建成功");

        /// <summary>
        /// 更新字典项
        /// </summary>
        /// <param name="updateDto">字典项更新DTO</param>
        /// <returns>更新结果</returns>
        [FunctionPermission("dictionary:update", "更新字典")]
        [HttpPost("update")]
        public async Task<Result<bool>> UpdateAsync([FromBody] SysDictionaryUpdateDto updateDto)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取更新前的字典信息，用于日志记录
            var beforeDict = await _sysDictionaryBLL.GetInfoAsync(updateDto.Id);

            // 执行业务操作
            var result = await _sysDictionaryBLL.UpdateDictionaryAsync(updateDto, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "字典管理",
                Operation = "更新字典",
                BusinessObject = "SysDictionary",
                ObjectId = updateDto.Id,
                DetailedInfo = $"用户 {currentUser.UserName} 成功更新了字典 {updateDto.DictItemName}",
                BeforeData = beforeDict,
                AfterData = updateDto,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(result, "更新成功");
        }

        /// <summary>
        /// 删除字典项
        /// </summary>
        /// <param name="id">字典项ID</param>
        /// <returns>删除结果</returns>
        [Log(Module = "字典管理", Description = "删除字典")]
        [FunctionPermission("dictionary:delete", "删除字典")]
        [HttpPost("delete/{id}")]
        public async Task<Result<bool>> DeleteAsync(string id)
        => Success(await _sysDictionaryBLL.DeleteDictionaryAsync(id), "删除成功");

        /// <summary>
        /// 根据字典类型码删除字典项
        /// </summary>
        /// <param name="dictTypeCode">字典类型码</param>
        /// <returns>删除结果</returns>
        [Log(Module = "字典管理", Description = "根据类型删除字典")]
        [FunctionPermission("dictionary:delete-by-type", "根据类型删除字典")]
        [HttpPost("delete-by-type/{dictTypeCode}")]
        public async Task<Result<bool>> DeleteByTypeCodeAsync(string dictTypeCode)
        => Success(await _sysDictionaryBLL.DeleteDictionariesByTypeCodeAsync(dictTypeCode), "删除成功");

        /// <summary>
        /// 添加父级字典（字典类型）
        /// </summary>
        /// <param name="createDto">父级字典创建DTO</param>
        /// <returns>操作结果</returns>
        [FunctionPermission("dictionary:create-parent", "添加父级字典")]
        [HttpPost("create-parent")]
        public async Task<Result<bool>> CreateParentDictionaryAsync([FromBody] SysDictionaryCreateDto createDto)
        {
            if (string.IsNullOrEmpty(createDto.DictTypeCode))
                return Fail<bool>("字典类型编码不能为空");

            if (string.IsNullOrEmpty(createDto.DictTypeName))
                return Fail<bool>("字典类型名称不能为空");

            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 添加父级字典逻辑
            var result = await _sysDictionaryBLL.AddParentDictionaryAsync(createDto, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "字典管理",
                Operation = "添加父级字典",
                BusinessObject = "SysDictionary",
                ObjectId = "N/A",
                DetailedInfo = $"用户 {currentUser.UserName} 成功添加了父级字典 {createDto.DictTypeName}({createDto.DictTypeCode})",
                AfterData = createDto,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(result, "父级字典添加成功");
        }

        /// <summary>
        /// 删除父级字典（字典类型）及其下所有子字典项
        /// </summary>
        /// <param name="parentId">父级字典ID</param>
        /// <returns>操作结果</returns>
        [Log(Module = "字典管理", Description = "删除父级字典及子字典")]
        [FunctionPermission("dictionary:delete-parent", "删除父级字典")]
        [HttpPost("delete-parent/{parentId}")]
        public async Task<Result<bool>> DeleteParentDictionaryAsync(string parentId)
        {
            if (string.IsNullOrEmpty(parentId))
                return Fail<bool>("父级字典ID不能为空");

            var result = await _sysDictionaryBLL.DeleteParentDictionaryWithChildrenAsync(parentId);
            return Success(result, "删除成功");
        }
        #endregion
    }
}