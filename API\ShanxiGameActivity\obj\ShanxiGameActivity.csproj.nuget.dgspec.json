{"format": 1, "restore": {"D:\\MyWork\\ShanxiGameActivity\\API\\ShanxiGameActivity\\ShanxiGameActivity.csproj": {}}, "projects": {"D:\\MyWork\\ShanxiGameActivity\\API\\BLL\\BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\ShanxiGameActivity\\API\\BLL\\BLL.csproj", "projectName": "BLL", "projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\BLL\\BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\ShanxiGameActivity\\API\\BLL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj"}, "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\DAL.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\DAL.csproj"}, "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\Entity.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj", "projectName": "Common", "projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AlibabaCloud.OpenApiClient": {"target": "Package", "version": "[0.1.13, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "RabbitMQ.Client": {"target": "Package", "version": "[6.8.1, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.7.17, )"}, "log4net": {"target": "Package", "version": "[2.0.15, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\DAL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\DAL.csproj", "projectName": "DAL", "projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\DAL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj"}, "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\Entity.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\Entity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.2.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\Entity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\Entity.csproj", "projectName": "Entity", "projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\Entity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Entity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\ShanxiGameActivity\\API\\ShanxiGameActivity\\ShanxiGameActivity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\ShanxiGameActivity\\API\\ShanxiGameActivity\\ShanxiGameActivity.csproj", "projectName": "ShanxiGameActivity", "projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\ShanxiGameActivity\\ShanxiGameActivity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\ShanxiGameActivity\\API\\ShanxiGameActivity\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\ShanxiGameActivity\\API\\BLL\\BLL.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\BLL\\BLL.csproj"}, "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\Common\\Common.csproj"}, "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\DAL.csproj": {"projectPath": "D:\\MyWork\\ShanxiGameActivity\\API\\DAL\\DAL.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[10.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.2, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}