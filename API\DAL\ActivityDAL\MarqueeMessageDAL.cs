using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 跑马灯消息数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class MarqueeMessageDAL(MyContext context) : BaseQueryDLL<ActivityMarqueeMessage, MarqueeMessageDAL.MarqueeMessageQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 跑马灯消息查询条件模型类
        /// </summary>
        public class MarqueeMessageQuery : PageQueryEntity
        {
            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ActivityId { get; set; }

            /// <summary>
            /// 玩家游戏用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? PlayerGameUserId { get; set; }

            /// <summary>
            /// 是否已显示
            /// </summary>
            [Query(QueryOperator.等于)]
            public bool? IsDisplayed { get; set; }
        }

        /// <summary>
        /// 获取活动的跑马灯消息
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="limit">消息数量限制</param>
        /// <param name="onlyUndisplayed">是否只获取未显示的消息</param>
        /// <returns>跑马灯消息列表</returns>
        public async Task<List<ActivityMarqueeMessage>> GetActivityMarqueeMessagesAsync(int activityId, int limit = 20, bool onlyUndisplayed = false)
        {
            var query = _context.MarqueeMessages
                .Where(m => m.ActivityId == activityId);

            if (onlyUndisplayed)
            {
                query = query.Where(m => !m.IsDisplayed);
            }

            return await query
                .OrderByDescending(m => m.CreateTime)
                .Take(limit)
                .ToListAsync();
        }

        /// <summary>
        /// 获取最新的跑马灯消息
        /// </summary>
        /// <param name="activityId">活动ID（可选，为空则获取所有活动）</param>
        /// <param name="limit">消息数量限制</param>
        /// <returns>最新跑马灯消息列表</returns>
        public async Task<List<ActivityMarqueeMessage>> GetLatestMarqueeMessagesAsync(int? activityId = null, int limit = 10)
        {
            var query = _context.MarqueeMessages
                .Where(m => !m.IsDisplayed);

            if (activityId.HasValue)
            {
                query = query.Where(m => m.ActivityId == activityId.Value);
            }

            return await query
                .OrderByDescending(m => m.CreateTime)
                .Take(limit)
                .ToListAsync();
        }

        /// <summary>
        /// 标记消息为已显示
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> MarkAsDisplayedAsync(int messageId)
        {
            var message = await _context.MarqueeMessages.FindAsync(messageId);
            if (message == null)
                return false;

            message.IsDisplayed = true;
            message.DisplayTime = DateTime.Now;
            message.UpdateTime = DateTime.Now;

            _context.MarqueeMessages.Update(message);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量标记消息为已显示
        /// </summary>
        /// <param name="messageIds">消息ID列表</param>
        /// <returns>更新的记录数</returns>
        public async Task<int> BatchMarkAsDisplayedAsync(List<int> messageIds)
        {
            if (messageIds.Count == 0)
                return 0;

            var messages = await _context.MarqueeMessages
                .Where(m => messageIds.Contains(m.Id))
                .ToListAsync();

            var now = DateTime.Now;
            foreach (var message in messages)
            {
                message.IsDisplayed = true;
                message.DisplayTime = now;
                message.UpdateTime = now;
            }

            _context.MarqueeMessages.UpdateRange(messages);
            return await _context.SaveChangesAsync();
        }

        /// <summary>
        /// 创建中奖跑马灯消息
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="playerNickname">玩家昵称</param>
        /// <param name="rewardName">奖励名称</param>
        /// <returns>创建的跑马灯消息</returns>
        public async Task<ActivityMarqueeMessage> CreateWinMessageAsync(int activityId, string playerGameUserId, string playerNickname, string rewardName)
        {
            var message = new ActivityMarqueeMessage
            {
                ActivityId = activityId,
                PlayerGameUserId = playerGameUserId,
                PlayerNickname = playerNickname,
                RewardName = rewardName,
                IsDisplayed = false,
                CreateTime = DateTime.Now
            };

            _context.MarqueeMessages.Add(message);
            await _context.SaveChangesAsync();

            return message;
        }

        /// <summary>
        /// 清理过期的跑马灯消息
        /// </summary>
        /// <param name="expireDays">过期天数，默认7天</param>
        /// <returns>清理的记录数</returns>
        public async Task<int> CleanExpiredMessagesAsync(int expireDays = 7)
        {
            var expireTime = DateTime.Now.AddDays(-expireDays);

            var expiredMessages = await _context.MarqueeMessages
                .Where(m => m.CreateTime < expireTime)
                .ToListAsync();

            if (expiredMessages.Count == 0)
                return 0;

            _context.MarqueeMessages.RemoveRange(expiredMessages);
            return await _context.SaveChangesAsync();
        }

        /// <summary>
        /// 获取活动跑马灯统计
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>跑马灯统计信息</returns>
        public async Task<MarqueeStats> GetMarqueeStatsAsync(int activityId)
        {
            var messages = await _context.MarqueeMessages
                .Where(m => m.ActivityId == activityId)
                .ToListAsync();

            return new MarqueeStats
            {
                TotalMessages = messages.Count,
                DisplayedMessages = messages.Count(m => m.IsDisplayed),
                PendingMessages = messages.Count(m => !m.IsDisplayed),
                LastMessageTime = messages.Count != 0 ? messages.Max(m => m.CreateTime) : null
            };
        }

        /// <summary>
        /// 格式化跑马灯消息文本
        /// </summary>
        /// <param name="message">跑马灯消息</param>
        /// <returns>格式化后的消息文本</returns>
        public static string FormatMarqueeText(ActivityMarqueeMessage message)
        {
            // 可以根据需要自定义消息格式
            return $"恭喜 {message.PlayerNickname} 在活动中获得 {message.RewardName}！";
        }

        /// <summary>
        /// 获取格式化的跑马灯消息列表
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="limit">消息数量限制</param>
        /// <returns>格式化的消息文本列表</returns>
        public async Task<List<string>> GetFormattedMarqueeMessagesAsync(int activityId, int limit = 10)
        {
            var messages = await GetLatestMarqueeMessagesAsync(activityId, limit);
            return [.. messages.Select(FormatMarqueeText)];
        }
    }

    /// <summary>
    /// 跑马灯统计信息
    /// </summary>
    public class MarqueeStats
    {
        /// <summary>
        /// 总消息数
        /// </summary>
        public int TotalMessages { get; set; }

        /// <summary>
        /// 已显示消息数
        /// </summary>
        public int DisplayedMessages { get; set; }

        /// <summary>
        /// 待显示消息数
        /// </summary>
        public int PendingMessages { get; set; }

        /// <summary>
        /// 最后消息时间
        /// </summary>
        public DateTime? LastMessageTime { get; set; }
    }
}
