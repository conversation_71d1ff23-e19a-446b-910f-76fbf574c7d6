{"GlobalPropertiesHash": "E3GpxouHcQ+jbNzw3P2hCiPbQ3EJSLKg6/PUk8AhoZg=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["5cEgDtBReNmDocD2T9TkcrzRZDW6Gt3V8rAXlF2aEuQ=", "iOFVqkhUnzE49m7bV+JUfS0bbVgGBl9HIfhjpTKEk24=", "4MaJvpnKR+tK2tunD/jFIzVxnfjMGU452/U0paR5rOs=", "vnolUrofLq+ge6+tn6gDQKShgWVu3SJSnj6+IdxSH9Q=", "Y9+0S1FV+pDu+/ygjI4zBPJgireLqWXSK2xiwT/DEF4=", "3hYqz21XmzdBaihLDOcyMPtBnUSOVe2KrCtzjWP2rAo=", "hTMOA096a975teNa+K5sQKU3pvsip4jgfO98t6D6tCo="], "CachedAssets": {"5cEgDtBReNmDocD2T9TkcrzRZDW6Gt3V8rAXlF2aEuQ=": {"Identity": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\build-app.bat", "SourceId": "DataMgrSystem", "SourceType": "Discovered", "ContentRoot": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\", "BasePath": "_content/DataMgrSystem", "RelativePath": "build-app#[.{fingerprint}]?.bat", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "np9u4khkac", "Integrity": "VQ+4rvqmbv+GjN26PiOCMtJaScvA4ithSU07GswUQ4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\build-app.bat", "FileLength": 672, "LastWriteTime": "2025-05-07T02:41:29.596095+00:00"}, "iOFVqkhUnzE49m7bV+JUfS0bbVgGBl9HIfhjpTKEk24=": {"Identity": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-app.bat", "SourceId": "DataMgrSystem", "SourceType": "Discovered", "ContentRoot": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\", "BasePath": "_content/DataMgrSystem", "RelativePath": "start-app#[.{fingerprint}]?.bat", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mqzi4oyyqd", "Integrity": "bgn2FE8iLHkiOsBN2NGhw9Kic4U7+1B/9vlkCx6sGxQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\start-app.bat", "FileLength": 142, "LastWriteTime": "2025-05-07T02:41:29.59796+00:00"}, "4MaJvpnKR+tK2tunD/jFIzVxnfjMGU452/U0paR5rOs=": {"Identity": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\start-without-debug.bat", "SourceId": "DataMgrSystem", "SourceType": "Discovered", "ContentRoot": "D:\\MyWork\\DataMgrSystem\\API\\DataMgrSystem\\wwwroot\\", "BasePath": "_content/DataMgrSystem", "RelativePath": "start-without-debug#[.{fingerprint}]?.bat", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7rvjcizg5g", "Integrity": "fZTG4pweBR/aCuiPspT67qLwbzrWFS4QIOpE2Nv2pN8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\start-without-debug.bat", "FileLength": 953, "LastWriteTime": "2025-05-07T02:41:29.5984843+00:00"}}, "CachedCopyCandidates": {}}