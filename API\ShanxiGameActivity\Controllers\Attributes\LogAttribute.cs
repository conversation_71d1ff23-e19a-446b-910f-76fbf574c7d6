namespace ShanxiGameActivity.Controllers.Attributes
{
    /// <summary>
    /// 日志记录特性
    /// </summary>
    [AttributeUsage(AttributeTargets.Method)]
    public class LogAttribute : Attribute
    {
        /// <summary>
        /// 模块名称
        /// </summary>
        public string Module { get; set; } = string.Empty;

        /// <summary>
        /// 操作描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; } = LogLevel.Information;

        /// <summary>
        /// 是否记录参数
        /// </summary>
        public bool LogParams { get; set; } = true;

        /// <summary>
        /// 是否记录返回值
        /// </summary>
        public bool LogResult { get; set; } = false;
    }
}