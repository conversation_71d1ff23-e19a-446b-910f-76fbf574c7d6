using BLL.SysService;
using DAL.SysDAL;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;
using static DAL.Databases.EFHelper;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 角色管理控制器
    /// 提供角色的增删改查、菜单权限分配等管理功能
    /// </summary>
    /// <remarks>
    /// 构造函数,通过依赖注入注入角色服务
    /// </remarks>
    /// <param name="roleService">角色服务实例</param>
    /// <param name="logService"></param>
    [Permission]
    public class RoleController(SysRoleService roleService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 角色服务接口
        /// </summary>
        private readonly SysRoleService _roleService = roleService;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        #region 基础CRUD操作

        /// <summary>
        /// 创建新角色
        /// </summary>
        /// <param name="input">创建角色请求</param>
        /// <returns>创建结果</returns>
        [FunctionPermission("role:create", "创建角色")]
        [HttpPost("create")]
        public async Task<Result<string>> CreateAsync(CreateRoleDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行业务操作
            var roleId = await _roleService.CreateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "角色管理",
                Operation = "创建角色",
                BusinessObject = "SysRole",
                ObjectId = roleId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功创建了角色 {input.Name}，角色ID: {roleId}",
                AfterData = new { RoleId = roleId, RoleName = input.Name, Description = input.Remark },
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(roleId.ToString(), "创建成功");
        }

        /// <summary>
        /// 更新现有角色信息
        /// </summary>
        /// <param name="input">更新角色请求</param>
        /// <returns>更新结果</returns>
        [FunctionPermission("role:update", "更新角色")]
        [HttpPost("update")]
        public async Task<Result> UpdateAsync(UpdateRoleDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取更新前的角色信息，用于日志记录
            var beforeRole = await _roleService.GetAsync(input.Id);

            // 执行业务操作
            await _roleService.UpdateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "角色管理",
                Operation = "更新角色",
                BusinessObject = "SysRole",
                ObjectId = input.Id,
                DetailedInfo = $"用户 {currentUser.UserName} 成功更新了角色 {input.Name} 的信息",
                BeforeData = beforeRole,
                AfterData = input,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("更新成功");
        }

        /// <summary>
        /// 删除指定角色
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>删除结果</returns>
        [FunctionPermission("role:delete", "删除角色")]
        [HttpPost("delete/{id}")]
        public async Task<Result> DeleteAsync(string id)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取删除前的角色信息，用于日志记录
            var beforeRole = await _roleService.GetAsync(id);

            // 执行业务操作
            await _roleService.DeleteAsync(id);

            // 记录操作成功的业务日志

            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "角色管理",
                Operation = "删除角色",
                BusinessObject = "SysRole",
                ObjectId = id,
                DetailedInfo = $"用户 {currentUser.UserName} 成功删除了角色 {beforeRole.Name}，角色ID: {id}",
                BeforeData = beforeRole,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("删除成功");
        }

        /// <summary>
        /// 获取角色详细信息
        /// </summary>
        /// <param name="id">角色ID</param>
        /// <returns>角色详细信息</returns>
        [HttpGet("{id}")]
        public async Task<Result<RoleDto>> GetAsync(string id)
        => Success(await _roleService.GetAsync(id), "获取成功");

        /// <summary>
        /// 分页查询角色列表
        /// </summary>
        /// <param name="queryable">分页查询请求</param>
        /// <returns>角色列表</returns>
        [HttpGet("query")]
        public async Task<Result<PageEntity<RoleDto>>> GetPageAsync([FromQuery] SysRoleDAL.Queryable queryable)
        => Success(await _roleService.GetPageAsync(queryable), "获取成功");


        #endregion
    }
}