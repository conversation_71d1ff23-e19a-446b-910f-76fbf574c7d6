using System.ComponentModel.DataAnnotations;

namespace Entity.Dto
{


    /// <summary>
    /// 分配权限请求DTO
    /// </summary>
    public class AssignPermissions_ReqDto
    {
        /// <summary>
        /// 主体ID
        /// </summary>
        [MaxLength(32)]
        public string SubjectId { get; set; } = string.Empty;

        /// <summary>
        /// 权限代码列表
        /// </summary>
        public List<string> PermissionCodes { get; set; } = [];
    }














    /// <summary>
    /// 权限DTO
    /// </summary>
    public class PermissionDto
    {
        /// <summary>
        /// 权限编码
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 权限名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 权限类型（1：菜单 2：按钮）
        /// </summary>
        public byte Type { get; set; }

        /// <summary>
        /// 菜单ID
        /// </summary>
        [MaxLength(32)]
        public string? MenuId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 分配角色权限DTO
    /// </summary>
    public class AssignRolePermissionDto
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [MaxLength(32)]
        public string RoleId { get; set; } = string.Empty;

        /// <summary>
        /// 权限编码列表
        /// </summary>
        public List<string> PermissionCodes { get; set; } = [];
    }
}