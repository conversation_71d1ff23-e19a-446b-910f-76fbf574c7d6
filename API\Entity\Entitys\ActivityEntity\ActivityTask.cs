using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 活动任务配置表
/// </summary>
[Table("activity_tasks")]
public class ActivityTask : BaseEntity_ID
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [Required]
    [Column("activity_id")]
    [Comment("活动ID")]
    public int ActivityId { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    [Required]
    [Column("task_type")]
    [Comment("任务类型")]
    public TaskType TaskType { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    [Required]
    [MaxLength(200)]
    [Column("task_name")]
    [Comment("任务名称")]
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 目标数值
    /// </summary>
    [Required]
    [Column("target_value")]
    [Comment("目标数值")]
    public int TargetValue { get; set; }

    /// <summary>
    /// 奖励次数
    /// </summary>
    [Required]
    [Column("reward_chances")]
    [Comment("奖励次数")]
    public int RewardChances { get; set; }

    /// <summary>
    /// 刷新类型
    /// </summary>
    [Required]
    [Column("refresh_type")]
    [Comment("刷新类型")]
    public RefreshType RefreshType { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [Column("sort_order")]
    [Comment("排序")]
    public int SortOrder { get; set; } = 0;

}
