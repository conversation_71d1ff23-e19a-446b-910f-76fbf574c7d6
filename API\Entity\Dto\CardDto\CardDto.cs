namespace Entity.Dto.CardDto
{
    /// <summary>
    /// 卡片信息DTO
    /// </summary>
    public class CardDto
    {
        /// <summary>
        /// 卡片编号
        /// </summary>
        public string CardNo { get; set; } = string.Empty;

        /// <summary>
        /// 关联卡片批次表的批次号
        /// </summary>
        public string BatchNo { get; set; } = string.Empty;

        /// <summary>
        /// 游戏币
        /// </summary>
        public long GameCurrency { get; set; }

        /// <summary>
        /// 实际面值
        /// </summary>
        public decimal ActualValue { get; set; }

        /// <summary>
        /// 卡片售价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 卡片状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 卡片核销时间
        /// </summary>
        public DateTime? UseTime { get; set; }

        /// <summary>
        /// 核销的玩家ID
        /// </summary>
        public string? UsePlayerId { get; set; }

        /// <summary>
        /// 当前持有人ID
        /// </summary>
        public string? HolderId { get; set; }

        /// <summary>
        /// 当前持有人获取卡片的时间
        /// </summary>
        public DateTime? AcquireTime { get; set; }

        /// <summary>
        /// 卡片创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }


    /// <summary>
    /// 查询用户拥有的卡信息DTO
    /// </summary>
    public class OpenApiGetCardInfoAsync_ResDto
    {
        /// <summary>
        /// 卡片面额
        /// </summary>
        public string GameCurrency { get; set; } = string.Empty;

        /// <summary>
        /// 卡片核销时间
        /// </summary>
        public int Count { get; set; }
    }

    /// <summary>
    /// 创建卡片DTO
    /// </summary>
    public class CreateCardDto
    {
        /// <summary>
        /// 关联卡片批次表的批次号
        /// </summary>
        public string BatchNo { get; set; } = string.Empty;

        /// <summary>
        /// 卡片面额
        /// </summary>
        public long GameCurrency { get; set; }

        /// <summary>
        /// 实际面值
        /// </summary>
        public decimal ActualValue { get; set; }

        /// <summary>
        /// 卡片售价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 卡片密码
        /// </summary>
        public string? CardPwd { get; set; }
    }

    /// <summary>
    /// 更新卡片DTO
    /// </summary>
    public class UpdateCardDto
    {
        /// <summary>
        /// 卡片编号
        /// </summary>
        public string CardNo { get; set; } = string.Empty;

        /// <summary>
        /// 卡片状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 卡片密码
        /// </summary>
        public string? CardPwd { get; set; }

        /// <summary>
        /// 当前持有人ID
        /// </summary>
        public string? HolderId { get; set; }
    }
}