using BLL.ActivityService;
using DAL.ActivityDAL;
using Entity;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.ActivityControllers
{
    /// <summary>
    /// 活动管理控制器
    /// </summary>
    [ApiController]
    [Route("api/activities")]
    public class ActivityController(ActivityService activityService) : BaseController
    {
        private readonly ActivityService _activityService = activityService;

        /// <summary>
        /// 创建活动
        /// </summary>
        /// <param name="request">创建活动请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<Result<ActivityResponseDto>> CreateAsync([FromBody] CreateActivityDto request)
        {
            // 从请求头获取用户信息
            var gameUserId = GetGameUserIdFromHeader();
            if (string.IsNullOrEmpty(gameUserId))
            {
                return Fail<ActivityResponseDto>("未授权：缺少用户身份信息", 401);
            }

            // 这里应该从游戏系统获取用户昵称，暂时使用默认值
            var creatorNickname = "用户" + gameUserId;

            var result = await _activityService.CreateActivityAsync(request, gameUserId, creatorNickname);
            return Success(result, "活动创建成功");
        }

        /// <summary>
        /// 获取活动列表
        /// </summary>
        /// <param name="status">活动状态</param>
        /// <param name="scope">查询范围</param>
        /// <param name="page">页码</param>
        /// <param name="limit">每页数量</param>
        /// <returns>活动列表</returns>
        [HttpGet]
        public async Task<object> GetActivitiesAsync(
            [FromQuery] ActivityStatus? status = null,
            [FromQuery] string? scope = "my",
            [FromQuery] int page = 1,
            [FromQuery] int limit = 10)
        {
            var gameUserId = GetGameUserIdFromHeader();
            if (string.IsNullOrEmpty(gameUserId))
            {
                return Fail<object>("未授权：缺少用户身份信息", 401);
            }

            var query = new ActivityQueryDto
            {
                Status = status,
                Scope = scope,
                Page = page,
                Limit = limit
            };

            var (activities, total) = await _activityService.GetActivitiesAsync(query, gameUserId);

            return Success(new
            {
                activities,
                total,
                page,
                limit
            }, "获取活动列表成功");
        }

        /// <summary>
        /// 获取活动详情
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>活动详情</returns>
        [HttpGet("{activityId}")]
        public async Task<Result<ActivityDetailResponseDto>> GetActivityDetailAsync(int activityId)
        {
            var gameUserId = GetGameUserIdFromHeader();
            if (string.IsNullOrEmpty(gameUserId))
            {
                return Fail<ActivityDetailResponseDto>("未授权：缺少用户身份信息", 401);
            }

            var result = await _activityService.GetActivityDetailAsync(activityId);
            if (result == null)
            {
                return Fail<ActivityDetailResponseDto>("活动不存在", 404);
            }
            return Success(result, "获取活动详情成功");
        }

        /// <summary>
        /// 关闭活动
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>关闭结果</returns>
        [HttpPut("{activityId}/close")]
        public async Task<object> CloseActivityAsync(int activityId)
        {
            var gameUserId = GetGameUserIdFromHeader();
            if (string.IsNullOrEmpty(gameUserId))
            {
                return Fail<object>("未授权：缺少用户身份信息", 401);
            }

            var success = await _activityService.CloseActivityAsync(activityId, gameUserId);
            if (!success)
            {
                return Fail<object>("关闭活动失败：活动不存在或无权限", 400);
            }

            return Success(new
            {
                activity_id = activityId,
                status = "closed"
            }, "活动已关闭");
        }

        /// <summary>
        /// 从请求头获取游戏用户ID
        /// </summary>
        /// <returns>游戏用户ID</returns>
        private string? GetGameUserIdFromHeader()
        {
            return Request.Headers["X-Game-User-Id"].FirstOrDefault();
        }

        #region 神秘盒子活动API接口

        /// <summary>
        /// 验证用户是否为指定创建者的下级用户
        /// </summary>
        /// <param name="request">验证请求参数</param>
        /// <returns>验证结果</returns>
        [HttpPost("validate-down-user")]
        public async Task<Result<bool>> ValidateDownUser([FromBody] ValidateDownUserRequest request)
        {
            var result = await _activityService.ValidateDownUserAsync(
                request.ClubId,
                request.CreatorId,
                request.TargetUserId);

            if (result)
            {
                return Success(true, "验证用户成功");
            }
            else
            {
                return Success(false, "验证用户失败：该用户不是下级用户");
            }
        }

        /// <summary>
        /// 获取今日用户游戏数据
        /// </summary>
        /// <param name="request">获取游戏数据请求参数</param>
        /// <returns>今日游戏数据列表</returns>
        [HttpPost("get-today-game-data")]
        public async Task<Result<List<TodayGameDataResult>>> GetTodayGameData([FromBody] GetTodayGameDataRequest request)
        {
            var result = await _activityService.GetTodayGameDataAsync(request.ClubId, request.CreatorId);
            return Success(result, "获取今日游戏数据成功");
        }

        /// <summary>
        /// 更新用户通宝
        /// </summary>
        /// <param name="request">更新通宝请求参数</param>
        /// <returns>更新结果列表</returns>
        [HttpPost("update-user-point")]
        public async Task<Result<List<UpdatePointResult>>> UpdateUserPoint([FromBody] UpdateUserPointRequest request)
        {
            var result = await _activityService.UpdateUserPointAsync(
                request.ClubId,
                request.CreatorId,
                request.OperationType,
                request.TargetUserId,
                request.PointAmount);
            return Success(result, "更新通宝成功");
        }

        #endregion
    }

    #region 神秘盒子请求参数模型

    /// <summary>
    /// 验证下级用户请求参数
    /// </summary>
    public class ValidateDownUserRequest
    {
        /// <summary>
        /// 活动创建者所属俱乐部ID（必需）
        /// </summary>
        public int ClubId { get; set; }

        /// <summary>
        /// 活动创建者ID（必需）
        /// </summary>
        public int CreatorId { get; set; }

        /// <summary>
        /// 要验证的下级ID（必需）
        /// </summary>
        public int TargetUserId { get; set; }
    }

    /// <summary>
    /// 获取今日游戏数据请求参数
    /// </summary>
    public class GetTodayGameDataRequest
    {
        /// <summary>
        /// 活动创建者所属俱乐部ID
        /// </summary>
        public int ClubId { get; set; }

        /// <summary>
        /// 活动创建者ID
        /// </summary>
        public int CreatorId { get; set; }
    }

    /// <summary>
    /// 更新用户通宝请求参数
    /// </summary>
    public class UpdateUserPointRequest
    {
        /// <summary>
        /// 活动创建者所属俱乐部ID
        /// </summary>
        public int ClubId { get; set; }

        /// <summary>
        /// 活动创建者ID
        /// </summary>
        public int CreatorId { get; set; }

        /// <summary>
        /// 操作类型：1-从用户身上扣除通宝注入奖池，2-从奖池中扣除通宝
        /// </summary>
        public int OperationType { get; set; }

        /// <summary>
        /// 被操作者ID
        /// </summary>
        public int TargetUserId { get; set; }

        /// <summary>
        /// 本次操作的通宝数量
        /// </summary>
        public int PointAmount { get; set; }
    }

    #endregion
}
