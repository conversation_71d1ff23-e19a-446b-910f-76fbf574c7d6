using Common.Autofac;
using DAL.ActivityDAL;
using Entity;
using Entity.Entitys.ActivityEntity;
using Microsoft.Extensions.Logging;

namespace BLL.ActivityService
{
    /// <summary>
    /// 任务进度服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class TaskProgressService(
        ActivityDAL activityDAL,
        ActivityTaskDAL activityTaskDAL,
        PlayerTaskProgressDAL playerTaskProgressDAL,
        GameDataSyncDAL gameDataSyncDAL,
        PlayerDrawChanceDAL playerDrawChanceDAL,
        ILogger<TaskProgressService> logger)
    {
        private readonly ActivityDAL _activityDAL = activityDAL;
        private readonly ActivityTaskDAL _activityTaskDAL = activityTaskDAL;
        private readonly PlayerTaskProgressDAL _playerTaskProgressDAL = playerTaskProgressDAL;
        private readonly GameDataSyncDAL _gameDataSyncDAL = gameDataSyncDAL;
        private readonly PlayerDrawChanceDAL _playerDrawChanceDAL = playerDrawChanceDAL;
        private readonly ILogger<TaskProgressService> _logger = logger;

        /// <summary>
        /// 更新所有活动的任务进度
        /// </summary>
        /// <returns>更新结果</returns>
        public async Task<TaskProgressUpdateResult> UpdateAllTaskProgressAsync()
        {
            var result = new TaskProgressUpdateResult();

            try
            {
                _logger.LogInformation("开始更新所有活动的任务进度");

                // 获取所有进行中的活动
                var activities = await _activityDAL.GetListAsync(new ActivityDAL.ActivityQuery
                {
                    Status = ActivityStatus.running
                });

                if (activities.Count == 0)
                {
                    result.Message = "没有找到进行中的活动";
                    return result;
                }

                foreach (var activity in activities)
                {
                    try
                    {
                        var activityResult = await UpdateActivityTaskProgressAsync(activity.Id);
                        result.ProcessedActivities++;
                        result.UpdatedTasks += activityResult.UpdatedTasks;
                        result.UpdatedPlayers += activityResult.UpdatedPlayers;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"更新活动 {activity.Id} 的任务进度失败");
                        result.FailedActivities++;
                    }
                }

                result.Success = true;
                result.Message = $"更新完成，处理了 {result.ProcessedActivities} 个活动";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度时发生异常");
                result.Success = false;
                result.Message = $"更新失败：{ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 更新指定活动的任务进度
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>更新结果</returns>
        public async Task<TaskProgressUpdateResult> UpdateActivityTaskProgressAsync(int activityId)
        {
            var result = new TaskProgressUpdateResult();

            try
            {
                // 获取活动信息
                var activity = await _activityDAL.GetByIdAsync(activityId);
                if (activity == null)
                {
                    result.Message = "活动不存在";
                    return result;
                }

                // 获取活动的所有任务
                var tasks = await _activityTaskDAL.GetListAsync(new ActivityTaskDAL.ActivityTaskQuery
                {
                    ActivityId = activityId
                });

                if (tasks.Count == 0)
                {
                    result.Message = "活动没有配置任务";
                    return result;
                }

                // 获取今日的游戏数据
                var today = DateTime.Today;
                var gameDataList = await _gameDataSyncDAL.GetGameDataBySyncDateAsync(today);

                if (gameDataList.Count == 0)
                {
                    result.Message = "没有找到今日游戏数据";
                    return result;
                }

                // 按用户分组处理
                var userGameData = gameDataList.GroupBy(g => g.UserId).ToList();

                foreach (var userGroup in userGameData)
                {
                    var userId = userGroup.Key;
                    var userNickname = userGroup.First().Nickname;
                    var userTotalData = userGroup.First(); // 假设每个用户每天只有一条记录

                    foreach (var task in tasks)
                    {
                        try
                        {
                            await UpdateUserTaskProgressAsync(
                                activityId,
                                task,
                                userId.ToString(),
                                userNickname,
                                userTotalData,
                                activity.StartTime,
                                activity.EndTime);

                            result.UpdatedTasks++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"更新用户 {userId} 任务 {task.Id} 进度失败");
                        }
                    }

                    result.UpdatedPlayers++;
                }

                result.Success = true;
                result.ProcessedActivities = 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新活动 {activityId} 任务进度失败");
                result.Success = false;
                result.Message = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 更新指定创建者相关的任务进度
        /// </summary>
        /// <param name="creatorId">创建者ID</param>
        /// <returns>更新结果</returns>
        public async Task<TaskProgressUpdateResult> UpdateTaskProgressForCreatorAsync(int creatorId)
        {
            var result = new TaskProgressUpdateResult();

            try
            {
                // 获取该创建者的所有进行中活动
                var activities = await _activityDAL.GetListAsync(new ActivityDAL.ActivityQuery
                {
                    CreatorGameUserId = creatorId.ToString(),
                    Status = ActivityStatus.running
                });

                foreach (var activity in activities)
                {
                    var activityResult = await UpdateActivityTaskProgressAsync(activity.Id);
                    result.ProcessedActivities++;
                    result.UpdatedTasks += activityResult.UpdatedTasks;
                    result.UpdatedPlayers += activityResult.UpdatedPlayers;
                }

                result.Success = true;
                result.Message = $"更新完成，处理了 {result.ProcessedActivities} 个活动";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新创建者 {creatorId} 的任务进度失败");
                result.Success = false;
                result.Message = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 更新单个用户的任务进度
        /// </summary>
        private async Task UpdateUserTaskProgressAsync(
            int activityId,
            ActivityTask task,
            string playerGameUserId,
            string playerNickname,
            ActivityGameDataSync gameData,
            DateTime activityStartTime,
            DateTime activityEndTime)
        {
            // 计算任务有效期
            var (validFrom, validTo) = CalculateTaskValidPeriod(task, activityStartTime, activityEndTime);

            // 获取或创建任务进度记录
            var progress = await _playerTaskProgressDAL.GetOrCreatePlayerTaskProgressAsync(
                activityId,
                task.Id,
                playerGameUserId,
                playerNickname,
                validFrom,
                validTo);

            // 检查是否需要刷新进度（处理跨天任务）
            CheckAndRefreshTaskProgress(progress, task);

            // 根据任务类型计算新的进度值
            var newProgressValue = CalculateTaskProgress(task.TaskType, gameData);

            // 更新进度
            if (newProgressValue > progress.CurrentProgress)
            {
                progress.CurrentProgress = newProgressValue;
                progress.UpdateTime = DateTime.Now;

                // 检查任务是否完成
                if (progress.CurrentProgress >= task.TargetValue && !progress.RewardClaimed)
                {
                    progress.CompletedTimes++;
                    progress.LastCompletedTime = DateTime.Now;
                }

                await _playerTaskProgressDAL.BatchUpdateTaskProgressAsync([progress]);
            }
        }

        /// <summary>
        /// 计算任务有效期
        /// </summary>
        private static (DateTime validFrom, DateTime validTo) CalculateTaskValidPeriod(
            ActivityTask task,
            DateTime activityStartTime,
            DateTime activityEndTime)
        {
            var now = DateTime.Now;

            switch (task.RefreshType)
            {
                case RefreshType.daily:
                    return (now.Date, now.Date.AddDays(1).AddSeconds(-1));
                case RefreshType.weekly:
                    var weekStart = now.Date.AddDays(-(int)now.DayOfWeek);
                    var weekEnd = weekStart.AddDays(7).AddSeconds(-1);
                    return (weekStart, weekEnd);
                case RefreshType.never:
                    return (activityStartTime, activityEndTime);
                default:
                    return (activityStartTime, activityEndTime);
            }
        }

        /// <summary>
        /// 检查并刷新任务进度
        /// </summary>
        private static void CheckAndRefreshTaskProgress(ActivityPlayerTaskProgress progress, ActivityTask task)
        {
            var now = DateTime.Now;
            var shouldRefresh = false;

            switch (task.RefreshType)
            {
                case RefreshType.daily:
                    shouldRefresh = progress.LastRefreshTime == null ||
                                   progress.LastRefreshTime.Value.Date < now.Date;
                    break;
                case RefreshType.weekly:
                    var weekStart = now.Date.AddDays(-(int)now.DayOfWeek);
                    shouldRefresh = progress.LastRefreshTime == null ||
                                   progress.LastRefreshTime.Value < weekStart;
                    break;
                case RefreshType.never:
                    shouldRefresh = false;
                    break;
            }

            if (shouldRefresh)
            {
                progress.CurrentProgress = 0;
                progress.LastRefreshTime = now;
                progress.RewardClaimed = false;
                progress.RewardClaimedTime = null;
                progress.RewardChancesEarned = 0;
            }
        }

        /// <summary>
        /// 根据任务类型计算进度值
        /// </summary>
        private static int CalculateTaskProgress(TaskType taskType, ActivityGameDataSync gameData)
        {
            return taskType switch
            {
                TaskType.login => gameData.OnlineDuration > 0 ? 1 : 0,
                TaskType.game_rounds => gameData.GameCount,
                TaskType.service_fee => (int)gameData.ServiceFee,
                _ => 0
            };
        }

        /// <summary>
        /// 领取任务奖励
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>领取结果</returns>
        public async Task<TaskRewardClaimResult> ClaimTaskRewardAsync(int activityId, int taskId, string playerGameUserId)
        {
            var result = new TaskRewardClaimResult();

            try
            {
                // 获取任务进度
                var progress = await _playerTaskProgressDAL.GetPlayerTaskProgressAsync(activityId, taskId, playerGameUserId);
                if (progress == null)
                {
                    result.Message = "任务进度不存在";
                    return result;
                }

                // 获取关联的任务信息
                var task = await _activityTaskDAL.GetFirstAsync(new ActivityTaskDAL.ActivityTaskQuery { Id = taskId });
                if (task == null)
                {
                    result.Message = "任务不存在";
                    return result;
                }

                // 检查任务是否完成
                if (progress.CurrentProgress < task.TargetValue)
                {
                    result.Message = "任务尚未完成";
                    return result;
                }

                // 检查奖励是否已领取
                if (progress.RewardClaimed)
                {
                    result.Message = "奖励已经领取过了";
                    return result;
                }

                // 检查任务是否在有效期内
                var now = DateTime.Now;
                if (now < progress.ValidFrom || now > progress.ValidTo)
                {
                    result.Message = "任务已过期";
                    return result;
                }

                // 领取奖励
                var rewardChances = task.RewardChances;
                var claimSuccess = await _playerTaskProgressDAL.ClaimTaskRewardAsync(progress.Id, rewardChances);

                if (claimSuccess)
                {
                    // 更新玩家抽奖次数
                    await UpdatePlayerDrawChancesAsync(activityId, playerGameUserId, rewardChances);

                    result.Success = true;
                    result.RewardChances = rewardChances;
                    result.Message = $"成功领取奖励，获得 {rewardChances} 次抽奖机会";
                }
                else
                {
                    result.Message = "领取奖励失败";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"领取任务奖励失败：活动{activityId}，任务{taskId}，用户{playerGameUserId}");
                result.Message = $"领取失败：{ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 更新玩家抽奖次数
        /// </summary>
        private async Task UpdatePlayerDrawChancesAsync(int activityId, string playerGameUserId, int additionalChances)
        {
            try
            {
                // 获取或创建玩家抽奖次数记录
                var drawChance = await _playerDrawChanceDAL.GetPlayerDrawChanceAsync(activityId, playerGameUserId);

                if (drawChance == null)
                {
                    // 创建新的抽奖次数记录
                    drawChance = new ActivityPlayerDrawChance
                    {
                        ActivityId = activityId,
                        PlayerGameUserId = playerGameUserId,
                        TotalChances = additionalChances,
                        UsedChances = 0,
                        RemainingChances = additionalChances,
                        CreateTime = DateTime.Now
                    };
                    await _playerDrawChanceDAL.AddAsync(drawChance);
                }
                else
                {
                    // 更新现有记录
                    drawChance.TotalChances += additionalChances;
                    drawChance.RemainingChances += additionalChances;
                    drawChance.UpdateTime = DateTime.Now;
                    await _playerDrawChanceDAL.UpdateAsync(drawChance);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新玩家抽奖次数失败：活动{ActivityId}，用户{PlayerGameUserId}，次数{AdditionalChances}", activityId, playerGameUserId, additionalChances);
            }
        }
    }

    /// <summary>
    /// 任务进度更新结果
    /// </summary>
    public class TaskProgressUpdateResult
    {
        public bool Success { get; set; } = false;
        public string Message { get; set; } = string.Empty;
        public int ProcessedActivities { get; set; } = 0;
        public int FailedActivities { get; set; } = 0;
        public int UpdatedTasks { get; set; } = 0;
        public int UpdatedPlayers { get; set; } = 0;
    }

    /// <summary>
    /// 任务奖励领取结果
    /// </summary>
    public class TaskRewardClaimResult
    {
        public bool Success { get; set; } = false;
        public string Message { get; set; } = string.Empty;
        public int RewardChances { get; set; } = 0;
    }
}
