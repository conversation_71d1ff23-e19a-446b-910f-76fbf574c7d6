using Common.Autofac;
using DAL.Databases;
using Entity;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 玩家任务进度数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class PlayerTaskProgressDAL(MyContext context) : BaseQueryDLL<ActivityPlayerTaskProgress, PlayerTaskProgressDAL.PlayerTaskProgressQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 玩家任务进度查询条件模型类
        /// </summary>
        public class PlayerTaskProgressQuery : PageQueryEntity
        {
            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ActivityId { get; set; }

            /// <summary>
            /// 任务ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? TaskId { get; set; }

            /// <summary>
            /// 玩家游戏用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? PlayerGameUserId { get; set; }
        }

        /// <summary>
        /// 获取玩家在活动中的任务进度
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>任务进度列表</returns>
        public async Task<List<ActivityPlayerTaskProgress>> GetPlayerTaskProgressAsync(int activityId, string playerGameUserId)
        {
            return await (from progress in _context.PlayerTaskProgresses
                          join task in _context.ActivityTasks on progress.TaskId equals task.Id
                          where progress.ActivityId == activityId && progress.PlayerGameUserId == playerGameUserId
                          orderby task.SortOrder
                          select progress)
                .ToListAsync();
        }

        /// <summary>
        /// 获取玩家特定任务的进度
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>任务进度</returns>
        public async Task<ActivityPlayerTaskProgress?> GetPlayerTaskProgressAsync(int activityId, int taskId, string playerGameUserId)
        {
            return await _context.PlayerTaskProgresses
                .FirstOrDefaultAsync(x => x.ActivityId == activityId &&
                                        x.TaskId == taskId &&
                                        x.PlayerGameUserId == playerGameUserId);
        }

        /// <summary>
        /// 创建或获取玩家任务进度
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="playerNickname">玩家昵称</param>
        /// <param name="validFrom">任务有效期开始时间</param>
        /// <param name="validTo">任务有效期结束时间</param>
        /// <returns>任务进度</returns>
        public async Task<ActivityPlayerTaskProgress> GetOrCreatePlayerTaskProgressAsync(
            int activityId,
            int taskId,
            string playerGameUserId,
            string playerNickname,
            DateTime validFrom,
            DateTime validTo)
        {
            var progress = await GetPlayerTaskProgressAsync(activityId, taskId, playerGameUserId);

            if (progress == null)
            {
                progress = new ActivityPlayerTaskProgress
                {
                    ActivityId = activityId,
                    TaskId = taskId,
                    PlayerGameUserId = playerGameUserId,
                    PlayerNickname = playerNickname,
                    CurrentProgress = 0,
                    CompletedTimes = 0,
                    RewardClaimed = false,
                    RewardChancesEarned = 0,
                    ValidFrom = validFrom,
                    ValidTo = validTo,
                    CreateTime = DateTime.Now
                };

                _context.PlayerTaskProgresses.Add(progress);
                await _context.SaveChangesAsync();
            }

            return progress;
        }

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="progressId">进度ID</param>
        /// <param name="increment">进度增量</param>
        /// <returns>更新后的进度和是否完成</returns>
        public async Task<(ActivityPlayerTaskProgress progress, bool isCompleted, int rewardChances)> UpdateProgressAsync(
            int progressId,
            int increment)
        {
            var progress = await _context.PlayerTaskProgresses
                .FirstOrDefaultAsync(x => x.Id == progressId);

            if (progress == null)
                throw new ArgumentException("任务进度不存在");

            // 获取关联的任务信息
            var task = await _context.ActivityTasks.FindAsync(progress.TaskId);
            if (task == null)
                throw new ArgumentException("关联任务不存在");

            progress.CurrentProgress += increment;
            progress.UpdateTime = DateTime.Now;

            var rewardChances = 0;
            var isCompleted = false;

            // 检查是否完成任务
            if (progress.CurrentProgress >= task.TargetValue)
            {
                isCompleted = true;
                rewardChances = task.RewardChances;
                progress.CompletedTimes++;
                progress.LastCompletedTime = DateTime.Now;

                // 根据刷新类型重置进度
                if (task.RefreshType != RefreshType.never)
                {
                    progress.CurrentProgress = 0;
                }
            }

            _context.PlayerTaskProgresses.Update(progress);
            await _context.SaveChangesAsync();

            return (progress, isCompleted, rewardChances);
        }

        /// <summary>
        /// 刷新玩家任务进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="refreshType">刷新类型</param>
        /// <returns>刷新的进度数量</returns>
        public async Task<int> RefreshTaskProgressAsync(int taskId, RefreshType refreshType)
        {
            var now = DateTime.Now;
            var progresses = await (from progress in _context.PlayerTaskProgresses
                                    join task in _context.ActivityTasks on progress.TaskId equals task.Id
                                    where progress.TaskId == taskId && task.RefreshType == refreshType
                                    select progress)
                .ToListAsync();

            var refreshCount = 0;

            foreach (var progress in progresses)
            {
                var shouldRefresh = false;

                switch (refreshType)
                {
                    case RefreshType.daily:
                        shouldRefresh = progress.LastRefreshTime == null ||
                                      progress.LastRefreshTime.Value.Date < now.Date;
                        break;
                    case RefreshType.weekly:
                        var weekStart = now.AddDays(-(int)now.DayOfWeek);
                        shouldRefresh = progress.LastRefreshTime == null ||
                                      progress.LastRefreshTime.Value < weekStart;
                        break;
                }

                if (shouldRefresh)
                {
                    progress.CurrentProgress = 0;
                    progress.LastRefreshTime = now;
                    progress.UpdateTime = now;
                    refreshCount++;
                }
            }

            if (refreshCount > 0)
            {
                _context.PlayerTaskProgresses.UpdateRange(progresses.Where(x =>
                    x.UpdateTime.HasValue && x.UpdateTime.Value.Date == now.Date));
                await _context.SaveChangesAsync();
            }

            return refreshCount;
        }

        /// <summary>
        /// 获取活动参与人数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>参与人数</returns>
        public async Task<int> GetActivityParticipantsCountAsync(int activityId)
        {
            return await _context.PlayerTaskProgresses
                .Where(x => x.ActivityId == activityId)
                .Select(x => x.PlayerGameUserId)
                .Distinct()
                .CountAsync();
        }

        /// <summary>
        /// 领取任务奖励
        /// </summary>
        /// <param name="progressId">任务进度ID</param>
        /// <param name="rewardChances">奖励次数</param>
        /// <returns>是否成功领取</returns>
        public async Task<bool> ClaimTaskRewardAsync(int progressId, int rewardChances)
        {
            var progress = await _context.PlayerTaskProgresses
                .FirstOrDefaultAsync(x => x.Id == progressId);

            if (progress == null)
                return false;

            // 获取关联的任务信息
            var task = await _context.ActivityTasks.FindAsync(progress.TaskId);
            if (task == null)
                return false;

            // 检查是否已经领取过奖励
            if (progress.RewardClaimed)
                return false;

            // 检查任务是否完成
            if (progress.CurrentProgress < task.TargetValue)
                return false;

            // 检查任务是否在有效期内
            var now = DateTime.Now;
            if (now < progress.ValidFrom || now > progress.ValidTo)
                return false;

            // 更新奖励领取状态
            progress.RewardClaimed = true;
            progress.RewardClaimedTime = now;
            progress.RewardChancesEarned = rewardChances;
            progress.UpdateTime = now;

            _context.PlayerTaskProgresses.Update(progress);
            await _context.SaveChangesAsync();

            return true;
        }

        /// <summary>
        /// 批量更新任务进度
        /// </summary>
        /// <param name="progressList">任务进度列表</param>
        /// <returns>更新的记录数</returns>
        public async Task<int> BatchUpdateTaskProgressAsync(List<ActivityPlayerTaskProgress> progressList)
        {
            if (progressList.Count == 0)
                return 0;

            foreach (var progress in progressList)
            {
                progress.UpdateTime = DateTime.Now;
                _context.PlayerTaskProgresses.Update(progress);
            }

            return await _context.SaveChangesAsync();
        }
    }
}
