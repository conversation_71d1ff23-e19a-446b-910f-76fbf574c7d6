namespace Entity;

/// <summary>
/// 批次状态
/// </summary>
public enum BatchStatusEnum
{
    /// <summary>
    /// 未激活
    /// </summary>
    未激活 = 0,
    /// <summary>
    /// 已激活
    /// </summary>
    已激活 = 1,
    /// <summary>
    /// 已出货
    /// </summary>
    已出货 = 2
}

/// <summary>
/// 卡片状态
/// </summary>
public enum CardStatusEnum
{
    /// <summary>
    /// 未核销
    /// </summary>
    未核销 = 0,
    /// <summary>
    /// 已核销
    /// </summary>
    已核销 = 1,
    /// <summary>
    /// 锁定中
    /// </summary>
    锁定中 = 2
}

/// <summary>
/// 核销状态
/// </summary>
public enum CardUseRecordStatusEnum
{
    /// <summary>
    /// 使用卡片
    /// </summary>
    使用卡片 = 1,
    /// <summary>
    /// 转移卡片
    /// </summary>
    转移卡片 = 0
}

/// <summary>
/// 卡片流转状态
/// </summary>
public enum CardTransferRecordStatusEnum
{
    /// <summary>
    /// 转卡
    /// </summary>
    转卡 = 0,
    /// <summary>
    /// 核销
    /// </summary>
    核销 = 1
}

/// <summary>
/// 活动类型
/// </summary>
public enum ActivityType
{
    /// <summary>
    /// 盲盒活动
    /// </summary>
    blind_box
}

/// <summary>
/// 活动状态
/// </summary>
public enum ActivityStatus
{
    /// <summary>
    /// 待开始
    /// </summary>
    pending,
    /// <summary>
    /// 进行中
    /// </summary>
    running,
    /// <summary>
    /// 已结束
    /// </summary>
    ended,
    /// <summary>
    /// 已停止
    /// </summary>
    stopped
}

/// <summary>
/// 奖励类型
/// </summary>
public enum RewardType
{
    /// <summary>
    /// 通宝
    /// </summary>
    tongbao,
    /// <summary>
    /// 实物
    /// </summary>
    physical
}

/// <summary>
/// 任务类型
/// </summary>
public enum TaskType
{
    /// <summary>
    /// 登录
    /// </summary>
    login,
    /// <summary>
    /// 游戏局数
    /// </summary>
    game_rounds,
    /// <summary>
    /// 服务费
    /// </summary>
    service_fee
}

/// <summary>
/// 刷新类型
/// </summary>
public enum RefreshType
{
    /// <summary>
    /// 每日刷新
    /// </summary>
    daily,
    /// <summary>
    /// 每周刷新
    /// </summary>
    weekly,
    /// <summary>
    /// 不刷新
    /// </summary>
    never
}

/// <summary>
/// 交易类型
/// </summary>
public enum TransactionType
{
    /// <summary>
    /// 活动投入
    /// </summary>
    activity_invest,
    /// <summary>
    /// 奖励发放
    /// </summary>
    prize_reward,
    /// <summary>
    /// 系统调整
    /// </summary>
    system_adjust
}

/// <summary>
/// 奖品发放状态
/// </summary>
public enum PrizeStatus
{
    /// <summary>
    /// 待发放
    /// </summary>
    pending,
    /// <summary>
    /// 已发放
    /// </summary>
    distributed,
    /// <summary>
    /// 发放失败
    /// </summary>
    failed
}


