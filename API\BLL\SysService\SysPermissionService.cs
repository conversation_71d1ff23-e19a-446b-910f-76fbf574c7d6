using Common.Autofac;
using Common.Redis;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Newtonsoft.Json;

namespace BLL.SysService
{
    /// <summary>
    /// 权限服务实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SysPermissionService(SysPermissionDAL permissionDLL, SysRoleDAL roleDLL, SysMenuService menuService, SysButtonService buttonService)
    {
        private readonly SysPermissionDAL _permissionDLL = permissionDLL;
        private readonly SysRoleDAL _roleDLL = roleDLL;
        private readonly SysMenuService _menuService = menuService;
        private readonly SysButtonService _buttonService = buttonService;

        /// <summary>
        /// 权限基础键
        /// </summary>
        private const string PermissionBaseKey = "SysPermission:";


        /// <summary>
        /// 清空权限缓存
        /// </summary>
        public static void ClearPermissionCache()
        => RedisHelper.Remove([.. RedisHelper.GetKeys(0, $"{PermissionBaseKey}*")]);

        /// <summary>
        /// 获取所有菜单和按钮权限
        /// </summary>
        /// <returns>菜单和按钮权限</returns>
        public async Task<List<MenuDto>> GetALLMenuAndButtonPermissionsAsync()
        => await RedisHelper.GetOrSetAsync($"{PermissionBaseKey}GetALLMenuAndButtonPermissionsAsync", async () =>
            {
                // 获取所有菜单
                var menus = await _menuService.GetTreeAsync(new());
                // 获取所有按钮
                var buttons = await _buttonService.GetListAsync(new());

                return FormatMenuPermissionsResponse(menus, buttons);
            });


        /// <summary>
        /// 获取按钮信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException">参数为空时抛出</exception>
        public async Task<List<ButtonDto>> GetButtonPermissionsAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentNullException(nameof(userId), "用户ID不能为空");

            return await RedisHelper.GetOrSetAsync($"{PermissionBaseKey}GetButtonPermissionsAsync_{userId}", async () =>
               {
                   // 获取用户角色
                   var userRoles = await _roleDLL.GetUserRolesAsync(userId);
                   userRoles.Add(userId);

                   // 获取用户权限
                   var permissions = await _permissionDLL.GetPermissionsAsync(userRoles);
                   if (permissions.Count == 0) return [];
                   // 获取按钮权限
                   var buttons = await _permissionDLL.GetButtonsByPermissionIdAsync([.. permissions.Select(m => m.ObjectId).Distinct()]);

                   List<ButtonDto> buttonDtos = [.. buttons.Select(m => new ButtonDto
                {
                    Id = m.Id,
                    Name = m.Name,
                    PermissionCode = m.PermissionCode
                })];

                   return buttonDtos;
               });
        }

        /// <summary>
        /// 获取用户所有权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户所有权限</returns>
        public async Task<List<MenuDto>> GetUserAllPermissionsAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentNullException(nameof(userId), "用户ID不能为空");

            return await RedisHelper.GetOrSetAsync($"{PermissionBaseKey}GetUserAllPermissionsAsync_{userId}", async () =>
            {
                // 获取用户角色
                var userRoles = await _roleDLL.GetUserRolesAsync(userId);
                userRoles.Add(userId);
                // 获取用户权限
                return await GetMenuAndButtonPermissionsAsync(userRoles);
            });
        }

        /// <summary>
        /// 获取菜单和按钮权限
        /// </summary>
        /// <param name="subjectIds">主体ID列表</param>
        /// <returns>菜单和按钮权限</returns>
        private async Task<List<MenuDto>> GetMenuAndButtonPermissionsAsync(List<string> subjectIds)
        {
            if (subjectIds == null || subjectIds.Count == 0)
                return [];

            // 获取权限列表
            var permissions = await _permissionDLL.GetPermissionsAsync(subjectIds);
            if (permissions.Count == 0)
                return [];

            // 获取权限对象ID列表
            var objectIds = permissions.Select(m => m.ObjectId).Distinct().ToList();

            // 获取角色菜单权限
            var menus = await _permissionDLL.GetMenusByPermissionIdAsync(objectIds);

            // 获取按钮权限
            var buttons = await _permissionDLL.GetButtonsByPermissionIdAsync(objectIds);

            return BuildPermissionsTree(menus, buttons);
        }

        /// <summary>
        /// 构建权限树
        /// </summary>
        /// <param name="menus">菜单列表</param>
        /// <param name="buttons">按钮列表</param>
        /// <returns>格式化后的菜单树</returns>
        private static List<MenuDto> BuildPermissionsTree(List<SysMenu> menus, List<SysButton> buttons)
        => FormatMenuPermissionsResponse(SysMenuService.BuildTree(menus), buttons);

        /// <summary>
        /// 格式化菜单权限响应
        /// </summary>
        /// <param name="menus">菜单列表</param>
        /// <param name="buttons">按钮列表</param>
        /// <returns>格式化后的菜单列表</returns>
        private static List<MenuDto> FormatMenuPermissionsResponse(List<MenuDto> menus, List<SysButton> buttons)
        {
            foreach (var item in menus)
            {
                if (!string.IsNullOrWhiteSpace(item.Perms))
                {
                    item.Buttons = [.. buttons
                        .Where(t => t.PermissionCode.Contains($"{item.Perms}:"))
                        .Select(t => new ButtonDto
                        {
                            Id = t.Id,
                            Name = t.Name,
                            PermissionCode = t.PermissionCode
                        })];
                }

                if (item.Children.Count > 0)
                    FormatMenuPermissionsResponse(item.Children, buttons);

            }

            return menus;
        }

        /// <summary>
        /// 获取用户权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户权限</returns>
        public async Task<List<MenuDto>> GetUserPermissionsAsync(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentNullException(nameof(userId), "用户ID不能为空");

            return await RedisHelper.GetOrSetAsync($"{PermissionBaseKey}GetUserPermissionsAsync_{userId}", async () =>
            {
                // 获取用户角色
                return await GetMenuAndButtonPermissionsAsync([userId]);
            }

            );
        }

        /// <summary>
        /// 获取角色权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>角色权限</returns>
        public async Task<List<MenuDto>> GetRolePermissionsAsync(string roleId)
        {
            if (string.IsNullOrEmpty(roleId))
                throw new ArgumentNullException(nameof(roleId), "角色ID不能为空");

            return await RedisHelper.GetOrSetAsync($"{PermissionBaseKey}GetRolePermissionsAsync_{roleId}", async () =>
            await GetMenuAndButtonPermissionsAsync([roleId])
            );
        }


        /// <summary>
        /// 获取权限列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>权限列表</returns>
        public async Task<List<SysPermission>> GetListAsync(SysPermissionDAL.Queryable queryable)
        => await RedisHelper.GetOrSetAsync($"{PermissionBaseKey}GetListAsync_{JsonConvert.SerializeObject(queryable)}", async () =>
        await _permissionDLL.GetListAsync(queryable));


        /// <summary>
        /// 分配权限
        /// </summary>
        /// <param name="assignPermissions_ReqDto">权限分配请求列表</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>分配结果</returns>
        /// <exception cref="ArgumentNullException">参数为空时抛出</exception>
        /// <exception cref="InvalidOperationException">分配权限失败时抛出</exception>
        public async Task<bool> AssignPermissionsAsync(List<AssignPermissions_ReqDto> assignPermissions_ReqDto, CurrentUserInfoDto currentUserInfo)
        {
            if (assignPermissions_ReqDto == null || assignPermissions_ReqDto.Count == 0)
                throw new ArgumentNullException(nameof(assignPermissions_ReqDto), "未传入权限分配信息");

            if (currentUserInfo == null)
                throw new ArgumentNullException(nameof(currentUserInfo), "当前用户信息不能为空");

            try
            {
                foreach (var item in assignPermissions_ReqDto)
                {
                    await _permissionDLL.AssignPermissionsAsync(item, currentUserInfo);
                }

                // 清空权限缓存
                ClearPermissionCache();

                return true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("分配权限失败", ex);
            }
        }
    }
}