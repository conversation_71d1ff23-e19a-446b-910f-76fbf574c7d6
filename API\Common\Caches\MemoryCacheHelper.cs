﻿using Microsoft.Extensions.Caching.Memory;

namespace Common.Caches
{
    /// <summary>
    /// 内存缓存帮助类
    /// </summary>
    public static class MemoryCacheHelper
    {
        /// <summary>
        /// 内存缓存实例
        /// </summary>
        private static readonly MemoryCache _cache = new(new MemoryCacheOptions());

        /// <summary>
        /// 获取缓存值
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <returns>缓存值,不存在返回默认值</returns>
        public static T? Get<T>(string key)
        {
            ArgumentNullException.ThrowIfNull(key);
            return _cache.Get<T>(key);
        }

        /// <summary>
        /// 设置缓存值
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="time">过期时间</param>
        /// <returns>缓存的值</returns>
        public static T Set<T>(string key, T value, TimeSpan time)
        {
            ArgumentNullException.ThrowIfNull(key);
            ArgumentNullException.ThrowIfNull(value);
            return _cache.Set(key, value, time);
        }

        /// <summary>
        /// 获取或添加缓存
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="func">获取值的函数</param>
        /// <param name="expiry">过期时间</param>
        /// <returns>缓存值</returns>
        public static T GetOrSet<T>(string key, Func<T> func, TimeSpan expiry)
        {
            ArgumentNullException.ThrowIfNull(key);
            ArgumentNullException.ThrowIfNull(func);

            return _cache.GetOrCreate(key, entry =>
            {
                entry.SlidingExpiration = expiry;
                return func();
            }) ?? throw new InvalidOperationException($"无法获取或设置键的缓存值,键: {key}");
        }

        /// <summary>
        /// 异步获取或添加缓存
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="func">异步获取值的函数</param>
        /// <param name="expiry">过期时间</param>
        /// <returns>缓存值</returns>
        public static async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> func, TimeSpan expiry)
        {
            ArgumentNullException.ThrowIfNull(key);
            ArgumentNullException.ThrowIfNull(func);

            var result = await _cache.GetOrCreateAsync(key, async entry =>
            {
                entry.SlidingExpiration = expiry;
                return await func();
            });

            return result ?? throw new InvalidOperationException($"无法获取或设置键的缓存值,键: {key}");
        }

        /// <summary>
        /// 移除缓存
        /// </summary>
        /// <param name="key">缓存键</param>
        public static void Remove(string key)
        {
            ArgumentNullException.ThrowIfNull(key);
            _cache.Remove(key);
        }

        /// <summary>
        /// 尝试获取缓存值
        /// </summary>
        /// <typeparam name="T">缓存值类型</typeparam>
        /// <param name="key">缓存键</param>
        /// <param name="value">输出的缓存值</param>
        /// <returns>是否成功获取</returns>
        public static bool TryGetValue<T>(string key, out T? value)
        {
            ArgumentNullException.ThrowIfNull(key);
            return _cache.TryGetValue(key, out value);
        }
    }
}
