using BLL.ActivityService;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.ActivityControllers
{
    /// <summary>
    /// 游戏数据同步控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class GameDataSyncController(GameDataSyncService gameDataSyncService) : BaseController
    {
        private readonly GameDataSyncService _gameDataSyncService = gameDataSyncService;

        /// <summary>
        /// 手动同步所有活动的游戏数据
        /// </summary>
        /// <returns>同步结果</returns>
        [HttpPost("sync-all")]
        public async Task<Result<GameDataSyncResponseDto>> SyncAllGameDataAsync()
        {
            try
            {
                var result = await _gameDataSyncService.SyncAllActivityGameDataAsync();

                var response = new GameDataSyncResponseDto
                {
                    Success = result.Success,
                    Message = result.Message,
                    ProcessedCreators = result.ProcessedCreators,
                    FailedCreators = result.FailedCreators,
                    SyncedRecords = result.SyncedRecords,
                    SyncTime = DateTime.Now
                };

                if (result.Success)
                {
                    return Success(response, "游戏数据同步成功");
                }
                else
                {
                    return Success(response, result.Message);
                }
            }
            catch (Exception ex)
            {
                return Fail<GameDataSyncResponseDto>($"同步游戏数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 手动同步指定创建者的游戏数据
        /// </summary>
        /// <param name="request">同步请求</param>
        /// <returns>同步结果</returns>
        [HttpPost("sync-creator")]
        public async Task<Result<GameDataSyncResponseDto>> SyncCreatorGameDataAsync([FromBody] GameDataSyncRequestDto request)
        {
            try
            {
                if (!request.ClubId.HasValue || !request.CreatorId.HasValue)
                {
                    return Fail<GameDataSyncResponseDto>("俱乐部ID和创建者ID不能为空");
                }

                var result = await _gameDataSyncService.SyncCreatorGameDataAsync(
                    request.ClubId.Value,
                    request.CreatorId.Value);

                var response = new GameDataSyncResponseDto
                {
                    Success = result.Success,
                    Message = result.Message,
                    ProcessedCreators = result.ProcessedCreators,
                    FailedCreators = result.FailedCreators,
                    SyncedRecords = result.SyncedRecords,
                    SyncTime = DateTime.Now
                };

                if (result.Success)
                {
                    return Success(response, "创建者游戏数据同步成功");
                }
                else
                {
                    return Success(response, result.Message);
                }
            }
            catch (Exception ex)
            {
                return Fail<GameDataSyncResponseDto>($"同步创建者游戏数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取同步状态和统计信息
        /// </summary>
        /// <returns>同步状态信息</returns>
        [HttpGet("sync-status")]
        public Result<object> GetSyncStatusAsync()
        {
            try
            {
                // 这里可以添加获取同步状态的逻辑
                // 比如最后同步时间、同步记录数等
                var status = new
                {
                    LastSyncTime = DateTime.Now, // 实际应该从数据库或缓存中获取
                    IsRunning = false, // 实际应该检查是否有同步任务在运行
                    Message = "同步服务正常运行"
                };

                return Success((object)status, "获取同步状态成功");
            }
            catch (Exception ex)
            {
                return Fail<object>($"获取同步状态失败：{ex.Message}");
            }
        }
    }
}
