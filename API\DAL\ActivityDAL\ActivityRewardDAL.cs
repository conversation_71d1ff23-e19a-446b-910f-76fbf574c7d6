using Common.Autofac;
using DAL.Databases;
using Entity;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 活动奖励数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class ActivityRewardDAL(MyContext context) : BaseQueryDLL<ActivityReward, ActivityRewardDAL.ActivityRewardQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 活动奖励查询条件模型类
        /// </summary>
        public class ActivityRewardQuery : PageQueryEntity
        {
            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ActivityId { get; set; }

            /// <summary>
            /// 奖励类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public RewardType? RewardType { get; set; }
        }

        /// <summary>
        /// 获取活动的所有奖励配置
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>奖励配置列表</returns>
        public async Task<List<ActivityReward>> GetActivityRewardsAsync(int activityId)
        {
            return await _context.ActivityRewards
                .Where(x => x.ActivityId == activityId)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 获取可抽奖的奖励列表（剩余份数大于0）
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>可抽奖的奖励列表</returns>
        public async Task<List<ActivityReward>> GetAvailableRewardsAsync(int activityId)
        {
            return await _context.ActivityRewards
                .Where(x => x.ActivityId == activityId && x.RemainingQuantity > 0)
                .OrderBy(x => x.SortOrder)
                .ToListAsync();
        }

        /// <summary>
        /// 更新奖励剩余份数
        /// </summary>
        /// <param name="rewardId">奖励ID</param>
        /// <param name="quantity">扣减数量</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateRemainingQuantityAsync(int rewardId, int quantity = 1)
        {
            var reward = await _context.ActivityRewards.FindAsync(rewardId);
            if (reward == null || reward.RemainingQuantity < quantity)
                return false;

            reward.RemainingQuantity -= quantity;
            reward.UpdateTime = DateTime.Now;

            _context.ActivityRewards.Update(reward);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量创建活动奖励
        /// </summary>
        /// <param name="rewards">奖励列表</param>
        /// <returns>创建的奖励列表</returns>
        public async Task<List<ActivityReward>> CreateBatchAsync(List<ActivityReward> rewards)
        {
            _context.ActivityRewards.AddRange(rewards);
            await _context.SaveChangesAsync();
            return rewards;
        }

        /// <summary>
        /// 计算奖励概率
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>是否计算成功</returns>
        public async Task<bool> CalculateProbabilitiesAsync(int activityId)
        {
            var rewards = await GetActivityRewardsAsync(activityId);
            if (rewards.Count == 0)
                return false;

            var totalWeight = rewards.Sum(x => x.Weight);

            foreach (var reward in rewards)
            {
                reward.Probability = (decimal)reward.Weight / totalWeight;
                reward.UpdateTime = DateTime.Now;
            }

            _context.ActivityRewards.UpdateRange(rewards);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 重置每日奖励份数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>是否重置成功</returns>
        public async Task<bool> ResetDailyQuantityAsync(int activityId)
        {
            var rewards = await GetActivityRewardsAsync(activityId);

            foreach (var reward in rewards)
            {
                reward.RemainingQuantity = reward.DailyQuantity;
                reward.UpdateTime = DateTime.Now;
            }

            _context.ActivityRewards.UpdateRange(rewards);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取奖励统计信息
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>奖励统计信息</returns>
        public async Task<Dictionary<int, int>> GetRewardStatisticsAsync(int activityId)
        {
            return await _context.DrawRecords
                .Where(x => x.ActivityId == activityId)
                .GroupBy(x => x.RewardId)
                .Select(g => new { RewardId = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.RewardId, x => x.Count);
        }
    }
}
