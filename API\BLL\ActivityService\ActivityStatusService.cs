using Common.Autofac;
using DAL.ActivityDAL;
using Entity;
using Entity.Entitys.ActivityEntity;
using Microsoft.Extensions.Logging;

namespace BLL.ActivityService
{
    /// <summary>
    /// 活动状态管理服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class ActivityStatusService(
        ActivityDAL activityDAL,
        ActivityRewardDAL activityRewardDAL,
        ILogger<ActivityStatusService> logger)
    {
        private readonly ActivityDAL _activityDAL = activityDAL;
        private readonly ActivityRewardDAL _activityRewardDAL = activityRewardDAL;
        private readonly ILogger<ActivityStatusService> _logger = logger;

        /// <summary>
        /// 更新所有活动状态
        /// </summary>
        /// <returns>更新结果</returns>
        public async Task<ActivityStatusUpdateResult> UpdateAllActivityStatusAsync()
        {
            var result = new ActivityStatusUpdateResult();

            try
            {
                _logger.LogInformation("开始更新所有活动状态");

                // 获取所有活动
                var activities = await _activityDAL.GetListAsync(new ActivityDAL.ActivityQuery());
                var now = DateTime.Now;

                foreach (var activity in activities)
                {
                    try
                    {
                        var oldStatus = activity.Status;
                        var newStatus = CalculateActivityStatus(activity, now);

                        if (oldStatus != newStatus)
                        {
                            activity.Status = newStatus;
                            activity.UpdateTime = now;
                            await _activityDAL.UpdateAsync(activity);

                            result.UpdatedActivities++;
                            _logger.LogInformation($"活动 {activity.Id} 状态从 {oldStatus} 更新为 {newStatus}");

                            // 处理状态变更的后续逻辑
                            await HandleStatusChangeAsync(activity, oldStatus, newStatus);
                        }

                        result.ProcessedActivities++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"更新活动 {activity.Id} 状态失败");
                        result.FailedActivities++;
                    }
                }

                result.Success = true;
                result.Message = $"状态更新完成，处理了 {result.ProcessedActivities} 个活动，更新了 {result.UpdatedActivities} 个活动状态";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新活动状态时发生异常");
                result.Success = false;
                result.Message = $"更新失败：{ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 更新指定活动状态
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>更新结果</returns>
        public async Task<ActivityStatusUpdateResult> UpdateActivityStatusAsync(int activityId)
        {
            var result = new ActivityStatusUpdateResult();

            try
            {
                var activity = await _activityDAL.GetByIdAsync(activityId);
                if (activity == null)
                {
                    result.Message = "活动不存在";
                    return result;
                }

                var oldStatus = activity.Status;
                var newStatus = CalculateActivityStatus(activity, DateTime.Now);

                if (oldStatus != newStatus)
                {
                    activity.Status = newStatus;
                    activity.UpdateTime = DateTime.Now;
                    await _activityDAL.UpdateAsync(activity);

                    result.UpdatedActivities = 1;
                    _logger.LogInformation($"活动 {activityId} 状态从 {oldStatus} 更新为 {newStatus}");

                    // 处理状态变更的后续逻辑
                    await HandleStatusChangeAsync(activity, oldStatus, newStatus);
                }

                result.Success = true;
                result.ProcessedActivities = 1;
                result.Message = oldStatus != newStatus ? $"活动状态已更新为 {newStatus}" : "活动状态无需更新";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新活动 {activityId} 状态失败");
                result.Success = false;
                result.Message = $"更新失败：{ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 计算活动状态
        /// </summary>
        /// <param name="activity">活动信息</param>
        /// <param name="currentTime">当前时间</param>
        /// <returns>计算出的活动状态</returns>
        private static ActivityStatus CalculateActivityStatus(ActivityInfo activity, DateTime currentTime)
        {
            // 如果活动被手动停止，保持停止状态
            if (activity.Status == ActivityStatus.stopped)
                return ActivityStatus.stopped;

            // 根据时间判断状态
            if (currentTime < activity.StartTime)
                return ActivityStatus.pending;

            if (currentTime > activity.EndTime)
                return ActivityStatus.ended;

            // 检查活动是否还有剩余通宝
            if (activity.RemainingTongbao <= 0)
                return ActivityStatus.ended;

            return ActivityStatus.running;
        }

        /// <summary>
        /// 处理状态变更的后续逻辑
        /// </summary>
        /// <param name="activity">活动信息</param>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        private async Task HandleStatusChangeAsync(ActivityInfo activity, ActivityStatus oldStatus, ActivityStatus newStatus)
        {
            try
            {
                switch (newStatus)
                {
                    case ActivityStatus.running:
                        await HandleActivityStartAsync(activity);
                        break;
                    case ActivityStatus.ended:
                        await HandleActivityEndAsync(activity);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理活动 {activity.Id} 状态变更后续逻辑失败");
            }
        }

        /// <summary>
        /// 处理活动开始逻辑
        /// </summary>
        /// <param name="activity">活动信息</param>
        private async Task HandleActivityStartAsync(ActivityInfo activity)
        {
            _logger.LogInformation($"活动 {activity.Id} 开始运行");

            // 重置每日奖励份数（如果需要）
            await ResetDailyRewardQuantityAsync(activity.Id);
        }

        /// <summary>
        /// 处理活动结束逻辑
        /// </summary>
        /// <param name="activity">活动信息</param>
        private async Task HandleActivityEndAsync(ActivityInfo activity)
        {
            _logger.LogInformation($"活动 {activity.Id} 已结束");

            // 这里可以添加活动结束后的清理逻辑
            // 比如：生成活动报告、清理临时数据等
        }

        /// <summary>
        /// 重置每日奖励份数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        public async Task<int> ResetDailyRewardQuantityAsync(int activityId)
        {
            try
            {
                var rewards = await _activityRewardDAL.GetListAsync(new ActivityRewardDAL.ActivityRewardQuery
                {
                    ActivityId = activityId
                });

                var updatedCount = 0;
                var today = DateTime.Today;

                foreach (var reward in rewards)
                {
                    // 检查是否需要重置（比如每日重置的奖励）
                    if (reward.DailyQuantity > 0 &&
                        (reward.LastResetTime == null || reward.LastResetTime.Value.Date < today))
                    {
                        reward.RemainingQuantity = reward.DailyQuantity;
                        reward.LastResetTime = today;
                        reward.UpdateTime = DateTime.Now;

                        await _activityRewardDAL.UpdateAsync(reward);
                        updatedCount++;
                    }
                }

                if (updatedCount > 0)
                {
                    _logger.LogInformation($"活动 {activityId} 重置了 {updatedCount} 个奖励的每日份数");
                }

                return updatedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"重置活动 {activityId} 每日奖励份数失败");
                return 0;
            }
        }

        /// <summary>
        /// 手动停止活动
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="reason">停止原因</param>
        /// <returns>是否成功停止</returns>
        public async Task<bool> StopActivityAsync(int activityId, string reason = "")
        {
            try
            {
                var activity = await _activityDAL.GetByIdAsync(activityId);
                if (activity == null)
                    return false;

                if (activity.Status == ActivityStatus.stopped)
                    return true; // 已经是停止状态

                activity.Status = ActivityStatus.stopped;
                activity.UpdateTime = DateTime.Now;
                await _activityDAL.UpdateAsync(activity);

                _logger.LogInformation($"活动 {activityId} 已手动停止，原因：{reason}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止活动 {activityId} 失败");
                return false;
            }
        }

        /// <summary>
        /// 手动启动活动
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>是否成功启动</returns>
        public async Task<bool> StartActivityAsync(int activityId)
        {
            try
            {
                var activity = await _activityDAL.GetByIdAsync(activityId);
                if (activity == null)
                    return false;

                var now = DateTime.Now;
                var newStatus = CalculateActivityStatus(activity, now);

                // 只有在计算状态为运行中时才能启动
                if (newStatus != ActivityStatus.running)
                    return false;

                activity.Status = ActivityStatus.running;
                activity.UpdateTime = now;
                await _activityDAL.UpdateAsync(activity);

                await HandleActivityStartAsync(activity);

                _logger.LogInformation($"活动 {activityId} 已手动启动");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"启动活动 {activityId} 失败");
                return false;
            }
        }
    }

    /// <summary>
    /// 活动状态更新结果
    /// </summary>
    public class ActivityStatusUpdateResult
    {
        public bool Success { get; set; } = false;
        public string Message { get; set; } = string.Empty;
        public int ProcessedActivities { get; set; } = 0;
        public int UpdatedActivities { get; set; } = 0;
        public int FailedActivities { get; set; } = 0;
    }
}
