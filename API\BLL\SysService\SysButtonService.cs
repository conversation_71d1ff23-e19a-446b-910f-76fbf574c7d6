using Common.Autofac;
using DAL.SysDAL;
using Entity.Dto;
using Entity.Entitys.SysEntity;

namespace BLL.SysService
{
    /// <summary>
    /// 按钮服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SysButtonService(SysButtonDAL buttonDAL)
    {
        private readonly SysButtonDAL _buttonDAL = buttonDAL;

        /// <summary>
        /// 同步按钮数据
        /// </summary>
        /// <param name="permissionMethods">权限方法列表</param>
        /// <returns>操作结果</returns>
        public async Task<bool> SyncButtonsAsync(List<PermissionMetadataDto> permissionMethods)
        {
            var result = await _buttonDAL.SyncButtonsAsync(permissionMethods);
            // 清空权限缓存
            SysPermissionService.ClearPermissionCache();
            return result;
        }


        /// <summary>
        /// 获取按钮列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>按钮列表</returns>
        public async Task<List<SysButton>> GetListAsync(SysButtonDAL.Queryable queryable)
        => await _buttonDAL.GetListAsync(queryable);

    }
}