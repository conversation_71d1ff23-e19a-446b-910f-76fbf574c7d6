using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 玩家抽奖次数数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class PlayerDrawChanceDAL(MyContext context) : BaseQueryDLL<ActivityPlayerDrawChance, PlayerDrawChanceDAL.PlayerDrawChanceQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 玩家抽奖次数查询条件模型类
        /// </summary>
        public class PlayerDrawChanceQuery : PageQueryEntity
        {
            /// <summary>
            /// 活动ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ActivityId { get; set; }

            /// <summary>
            /// 玩家游戏用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? PlayerGameUserId { get; set; }
        }

        /// <summary>
        /// 获取玩家抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>抽奖次数信息</returns>
        public async Task<ActivityPlayerDrawChance?> GetPlayerDrawChanceAsync(int activityId, string playerGameUserId)
        {
            return await _context.PlayerDrawChances
                .FirstOrDefaultAsync(x => x.ActivityId == activityId && x.PlayerGameUserId == playerGameUserId);
        }

        /// <summary>
        /// 创建或获取玩家抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="playerNickname">玩家昵称</param>
        /// <returns>抽奖次数信息</returns>
        public async Task<ActivityPlayerDrawChance> GetOrCreatePlayerDrawChanceAsync(
            int activityId,
            string playerGameUserId,
            string playerNickname)
        {
            var drawChance = await GetPlayerDrawChanceAsync(activityId, playerGameUserId);

            if (drawChance == null)
            {
                drawChance = new ActivityPlayerDrawChance
                {
                    ActivityId = activityId,
                    PlayerGameUserId = playerGameUserId,
                    PlayerNickname = playerNickname,
                    TotalChances = 0,
                    UsedChances = 0,
                    RemainingChances = 0,
                    CreateTime = DateTime.Now
                };

                _context.PlayerDrawChances.Add(drawChance);
                await _context.SaveChangesAsync();
            }

            return drawChance;
        }

        /// <summary>
        /// 增加抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="playerNickname">玩家昵称</param>
        /// <param name="chances">增加的次数</param>
        /// <returns>更新后的抽奖次数信息</returns>
        public async Task<ActivityPlayerDrawChance> AddDrawChancesAsync(
            int activityId,
            string playerGameUserId,
            string playerNickname,
            int chances)
        {
            var drawChance = await GetOrCreatePlayerDrawChanceAsync(activityId, playerGameUserId, playerNickname);

            drawChance.TotalChances += chances;
            drawChance.RemainingChances += chances;
            drawChance.UpdateTime = DateTime.Now;

            _context.PlayerDrawChances.Update(drawChance);
            await _context.SaveChangesAsync();

            return drawChance;
        }

        /// <summary>
        /// 使用抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="chances">使用的次数</param>
        /// <returns>是否使用成功</returns>
        public async Task<bool> UseDrawChanceAsync(int activityId, string playerGameUserId, int chances = 1)
        {
            var drawChance = await GetPlayerDrawChanceAsync(activityId, playerGameUserId);

            if (drawChance == null || drawChance.RemainingChances < chances)
                return false;

            drawChance.UsedChances += chances;
            drawChance.RemainingChances -= chances;
            drawChance.UpdateTime = DateTime.Now;

            _context.PlayerDrawChances.Update(drawChance);
            return await _context.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 检查玩家是否有足够的抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="requiredChances">需要的次数</param>
        /// <returns>是否有足够次数</returns>
        public async Task<bool> HasEnoughChancesAsync(int activityId, string playerGameUserId, int requiredChances = 1)
        {
            var drawChance = await GetPlayerDrawChanceAsync(activityId, playerGameUserId);
            return drawChance != null && drawChance.RemainingChances >= requiredChances;
        }

        /// <summary>
        /// 获取活动的抽奖统计
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>抽奖统计信息</returns>
        public async Task<(int totalPlayers, int totalChances, int usedChances)> GetDrawStatisticsAsync(int activityId)
        {
            var statistics = await _context.PlayerDrawChances
                .Where(x => x.ActivityId == activityId)
                .GroupBy(x => x.ActivityId)
                .Select(g => new
                {
                    TotalPlayers = g.Count(),
                    TotalChances = g.Sum(x => x.TotalChances),
                    UsedChances = g.Sum(x => x.UsedChances)
                })
                .FirstOrDefaultAsync();

            return statistics != null
                ? (statistics.TotalPlayers, statistics.TotalChances, statistics.UsedChances)
                : (0, 0, 0);
        }
    }
}
