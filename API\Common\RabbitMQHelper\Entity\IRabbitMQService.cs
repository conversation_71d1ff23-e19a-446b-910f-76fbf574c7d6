namespace Common.RabbitMQHelper.Entity;

/// <summary>
/// RabbitMQ 服务接口
/// </summary>
public interface IRabbitMQService
{
    /// <summary>
    /// 发布消息
    /// </summary>
    Task PublishAsync<T>(T message, string queue, string exchange = "", string routingKey = "", IDictionary<string, object>? headers = null, int expiration = 0);

    /// <summary>
    /// 消费消息
    /// </summary>
    Task ConsumeAsync(string queue, Func<RabbitMessageEntity, Task<bool>> callback, int maxRetry = 0);

    /// <summary>
    /// 关闭连接
    /// </summary>
    Task CloseAsync();
}