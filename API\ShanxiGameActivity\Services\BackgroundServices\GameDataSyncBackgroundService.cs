using BLL.ActivityService;

namespace ShanxiGameActivity.Services.BackgroundServices
{
    /// <summary>
    /// 游戏数据同步后台服务
    /// </summary>
    public class GameDataSyncBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<GameDataSyncBackgroundService> logger,
        IConfiguration configuration) : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider = serviceProvider;
        private readonly ILogger<GameDataSyncBackgroundService> _logger = logger;
        private readonly IConfiguration _configuration = configuration;

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("游戏数据同步后台服务已启动");

            // 从配置中读取同步间隔，默认5分钟
            var syncIntervalMinutes = _configuration.GetValue<int>("GameDataSync:IntervalMinutes", 5);
            var syncInterval = TimeSpan.FromMinutes(syncIntervalMinutes);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformSyncAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "执行游戏数据同步时发生异常");
                }

                // 等待下次同步
                try
                {
                    await Task.Delay(syncInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // 服务正在停止
                    break;
                }
            }

            _logger.LogInformation("游戏数据同步后台服务已停止");
        }

        /// <summary>
        /// 执行同步操作
        /// </summary>
        private async Task PerformSyncAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var gameDataSyncService = scope.ServiceProvider.GetRequiredService<GameDataSyncService>();

            try
            {
                _logger.LogInformation("开始执行定时游戏数据同步");

                var result = await gameDataSyncService.SyncAllActivityGameDataAsync();

                if (result.Success)
                {
                    _logger.LogInformation($"定时同步完成：{result.Message}");
                }
                else
                {
                    _logger.LogWarning($"定时同步失败：{result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "定时同步过程中发生异常");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("正在停止游戏数据同步后台服务...");
            await base.StopAsync(stoppingToken);
        }
    }
}
