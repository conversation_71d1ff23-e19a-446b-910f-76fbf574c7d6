{"openapi": "3.0.1", "info": {"title": "ShanxiGameActivity", "version": "1.0"}, "paths": {"/api/activities": {"post": {"tags": ["Activity"], "summary": "创建活动", "requestBody": {"description": "创建活动请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateActivityDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateActivityDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateActivityDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ActivityResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActivityResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActivityResponseDtoResult"}}}}}}, "get": {"tags": ["Activity"], "summary": "获取活动列表", "parameters": [{"name": "status", "in": "query", "description": "活动状态", "schema": {"$ref": "#/components/schemas/ActivityStatus"}}, {"name": "scope", "in": "query", "description": "查询范围", "schema": {"type": "string", "default": "my"}}, {"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success"}}}}, "/api/activities/{activityId}": {"get": {"tags": ["Activity"], "summary": "获取活动详情", "parameters": [{"name": "activityId", "in": "path", "description": "活动ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ActivityDetailResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActivityDetailResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActivityDetailResponseDtoResult"}}}}}}}, "/api/activities/{activityId}/close": {"put": {"tags": ["Activity"], "summary": "关闭活动", "parameters": [{"name": "activityId", "in": "path", "description": "活动ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/activities/validate-down-user": {"post": {"tags": ["Activity"], "summary": "验证用户是否为指定创建者的下级用户", "requestBody": {"description": "验证请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateDownUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidateDownUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidateDownUserRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/activities/get-today-game-data": {"post": {"tags": ["Activity"], "summary": "获取今日用户游戏数据", "requestBody": {"description": "获取游戏数据请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetTodayGameDataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GetTodayGameDataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GetTodayGameDataRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TodayGameDataResultListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TodayGameDataResultListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TodayGameDataResultListResult"}}}}}}}, "/api/activities/update-user-point": {"post": {"tags": ["Activity"], "summary": "更新用户通宝", "requestBody": {"description": "更新通宝请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserPointRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserPointRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserPointRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UpdatePointResultListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdatePointResultListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePointResultListResult"}}}}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登录接口", "requestBody": {"description": "登录请求DTO，包含用户名和密码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}}}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "用户登出接口", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Auth/sendSmsCode": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "发送短信验证码", "parameters": [{"name": "phoneNumber", "in": "query", "description": "手机号码", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Auth/smsLogin": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "使用短信验证码登录", "requestBody": {"description": "登录信息，包含手机号和验证码", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SmsLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SmsLoginDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoResult"}}}}}}}, "/api/Auth/userinfo": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取当前登录用户信息接口", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserInfoDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserInfoDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserInfoDtoResult"}}}}}}}, "/api/Basis/UploadFileAsync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "本地单文件上传", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Basis/UploadFilesAsync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "本地多文件上传", "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"files": {"style": "form"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Button/sync": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "重新获取权限数据", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/debug/test-procedure": {"post": {"tags": ["Debug"], "summary": "测试存储过程调用", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcedureTestRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcedureTestRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProcedureTestRequest"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectResult"}}}}}}}, "/api/activities/{activityId}/draw-chances": {"get": {"tags": ["Draw"], "summary": "获取抽奖次数", "parameters": [{"name": "activityId", "in": "path", "description": "活动ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DrawChancesResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DrawChancesResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DrawChancesResponseDtoResult"}}}}}}}, "/api/activities/{activityId}/draw": {"post": {"tags": ["Draw"], "summary": "执行抽奖", "parameters": [{"name": "activityId", "in": "path", "description": "活动ID", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DrawResultResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DrawResultResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DrawResultResponseDtoResult"}}}}}}}, "/api/activities/{activityId}/draw-records": {"get": {"tags": ["Draw"], "summary": "获取抽奖记录", "parameters": [{"name": "activityId", "in": "path", "description": "活动ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "type", "in": "query", "description": "记录类型", "schema": {"type": "string", "default": "all"}}, {"name": "limit", "in": "query", "description": "数量限制", "schema": {"type": "integer", "format": "int32", "default": 50}}], "responses": {"200": {"description": "Success"}}}}, "/api/activities/{activityId}/add-chances": {"post": {"tags": ["Draw"], "summary": "增加抽奖次数（内部接口，由任务系统调用）", "parameters": [{"name": "activityId", "in": "path", "description": "活动ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "gameUserId", "in": "query", "description": "游戏用户ID", "schema": {"type": "string"}}, {"name": "playerNickname", "in": "query", "description": "玩家昵称", "schema": {"type": "string"}}, {"name": "chances", "in": "query", "description": "增加的次数", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DrawChancesResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DrawChancesResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DrawChancesResponseDtoResult"}}}}}}}, "/api/Log/query": {"get": {"tags": ["Log"], "summary": "分页查询日志列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "schema": {"type": "string"}}, {"name": "Operation", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "LogType", "in": "query", "schema": {"type": "string"}}, {"name": "LogLevel", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysLogPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysLogPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysLogPageEntityResult"}}}}}}}, "/api/Log/{id}": {"get": {"tags": ["Log"], "summary": "获取日志详情", "parameters": [{"name": "id", "in": "path", "description": "日志ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysLogResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysLogResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysLogResult"}}}}}}}, "/api/Log/export": {"post": {"tags": ["Log"], "summary": "导出日志列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Username", "in": "query", "schema": {"type": "string"}}, {"name": "Operation", "in": "query", "schema": {"type": "string"}}, {"name": "Method", "in": "query", "schema": {"type": "string"}}, {"name": "LogType", "in": "query", "schema": {"type": "string"}}, {"name": "LogLevel", "in": "query", "schema": {"type": "string"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Log/clear": {"post": {"tags": ["Log"], "summary": "清理指定日期之前的日志", "requestBody": {"description": "清理日志参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LogClearDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Menu/create": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "创建新菜单", "requestBody": {"description": "创建菜单请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMenuDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/Menu/update": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "更新现有菜单信息", "requestBody": {"description": "更新菜单请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateMenuDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Menu/delete/{id}": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "删除指定菜单", "parameters": [{"name": "id", "in": "path", "description": "菜单ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Menu/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取指定菜单的详细信息", "parameters": [{"name": "id", "in": "path", "description": "菜单ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoResult"}}}}}}}, "/api/Menu/tree": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取菜单树结构", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "Ids", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "ParentId", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderByOrderNum", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Menu/GetSelfUserMenuTreeAsync": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "获取指定用户的菜单树", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/self/all": {"get": {"tags": ["Permission"], "summary": "获取当前用户所有权限", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/user/{userId}/all": {"get": {"tags": ["Permission"], "summary": "获取用户所有权限", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/user/{userId}": {"get": {"tags": ["Permission"], "summary": "获取用户权限", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/clear/cache": {"get": {"tags": ["Permission"], "summary": "清空权限缓存", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Permission/role/{roleId}": {"get": {"tags": ["Permission"], "summary": "获取角色权限", "parameters": [{"name": "roleId", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MenuDtoListResult"}}}}}}}, "/api/Permission/assign": {"post": {"tags": ["Permission"], "summary": "分配权限", "requestBody": {"description": "权限分配参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignPermissions_ReqDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignPermissions_ReqDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignPermissions_ReqDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Role/create": {"post": {"tags": ["Role"], "summary": "创建新角色", "requestBody": {"description": "创建角色请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRoleDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/Role/update": {"post": {"tags": ["Role"], "summary": "更新现有角色信息", "requestBody": {"description": "更新角色请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Role/delete/{id}": {"post": {"tags": ["Role"], "summary": "删除指定角色", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/Role/{id}": {"get": {"tags": ["Role"], "summary": "获取角色详细信息", "parameters": [{"name": "id", "in": "path", "description": "角色ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoResult"}}}}}}}, "/api/Role/query": {"get": {"tags": ["Role"], "summary": "分页查询角色列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "OrderByOrderNum", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoPageEntityResult"}}}}}}}, "/api/SysDictionary/list": {"get": {"tags": ["SysDictionary"], "summary": "获取所有字典列表", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}}}}}}, "/api/SysDictionary/by-type/{dictTypeCode}": {"get": {"tags": ["SysDictionary"], "summary": "根据字典类型码获取字典项", "parameters": [{"name": "dictTypeCode", "in": "path", "description": "字典类型码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoListResult"}}}}}}}, "/api/SysDictionary/dropdown/{dictTypeCode}": {"get": {"tags": ["SysDictionary"], "summary": "根据字典类型码获取下拉框数据", "parameters": [{"name": "dictTypeCode", "in": "path", "description": "字典类型码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DictionaryDropdownDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DictionaryDropdownDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictionaryDropdownDtoListResult"}}}}}}}, "/api/SysDictionary/types": {"get": {"tags": ["SysDictionary"], "summary": "获取所有字典类型（父级字典）", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DictionaryTypeDtoListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DictionaryTypeDtoListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DictionaryTypeDtoListResult"}}}}}}}, "/api/SysDictionary/query": {"get": {"tags": ["SysDictionary"], "summary": "分页查询字典列表", "parameters": [{"name": "Id", "in": "query", "schema": {"type": "string"}}, {"name": "ParentId", "in": "query", "schema": {"type": "string"}}, {"name": "DictTypeCode", "in": "query", "schema": {"type": "string"}}, {"name": "DictTypeName", "in": "query", "schema": {"type": "string"}}, {"name": "DictItemCode", "in": "query", "schema": {"type": "string"}}, {"name": "DictItemName", "in": "query", "schema": {"type": "string"}}, {"name": "IsEnabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderBySortOrder", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntityResult"}}}}}}}, "/api/SysDictionary/create": {"post": {"tags": ["SysDictionary"], "summary": "添加字典项", "requestBody": {"description": "字典项创建DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/batch-create": {"post": {"tags": ["SysDictionary"], "summary": "批量添加字典项", "requestBody": {"description": "字典项创建DTO列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/update": {"post": {"tags": ["SysDictionary"], "summary": "更新字典项", "requestBody": {"description": "字典项更新DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictionaryUpdateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/delete/{id}": {"post": {"tags": ["SysDictionary"], "summary": "删除字典项", "parameters": [{"name": "id", "in": "path", "description": "字典项ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/delete-by-type/{dictTypeCode}": {"post": {"tags": ["SysDictionary"], "summary": "根据字典类型码删除字典项", "parameters": [{"name": "dictTypeCode", "in": "path", "description": "字典类型码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/create-parent": {"post": {"tags": ["SysDictionary"], "summary": "添加父级字典（字典类型）", "requestBody": {"description": "父级字典创建DTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SysDictionaryCreateDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/SysDictionary/delete-parent/{parentId}": {"post": {"tags": ["SysDictionary"], "summary": "删除父级字典（字典类型）及其下所有子字典项", "parameters": [{"name": "parentId", "in": "path", "description": "父级字典ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanResult"}}}}}}}, "/api/User/create": {"post": {"tags": ["User"], "summary": "创建新用户", "requestBody": {"description": "创建用户请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/User/update": {"post": {"tags": ["User"], "summary": "更新用户信息", "requestBody": {"description": "更新用户请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/delete/{id}": {"post": {"tags": ["User"], "summary": "删除指定用户", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "summary": "获取用户详细信息", "parameters": [{"name": "id", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoResult"}}}}}}}, "/api/User/query": {"get": {"tags": ["User"], "summary": "分页查询用户列表", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "RealName", "in": "query", "schema": {"type": "string"}}, {"name": "Mobile", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoPageEntityResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoPageEntityResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoPageEntityResult"}}}}}}}, "/api/User/change-password": {"post": {"tags": ["User"], "summary": "修改用户密码", "requestBody": {"description": "修改密码请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/reset-password": {"post": {"tags": ["User"], "summary": "重置用户密码", "requestBody": {"description": "重置密码请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/{userId}/assign-roles": {"post": {"tags": ["User"], "summary": "为用户分配角色", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "角色ID列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Result"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Result"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Result"}}}}}}}, "/api/User/{userId}/roles": {"get": {"tags": ["User"], "summary": "获取用户已分配的角色列表", "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SysRoleListResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SysRoleListResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SysRoleListResult"}}}}}}}, "/api/User/export": {"post": {"tags": ["User"], "summary": "导出用户列表", "parameters": [{"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "RealName", "in": "query", "schema": {"type": "string"}}, {"name": "Mobile", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "StartTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderByCreateTime", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageIndex", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"description": "导出配置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportRequestDto"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/users/cache-nickname": {"post": {"tags": ["UserCache"], "summary": "缓存用户昵称", "requestBody": {"description": "缓存请求", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CacheUserNicknameDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CacheUserNicknameDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CacheUserNicknameDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserCacheResponseDtoResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserCacheResponseDtoResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCacheResponseDtoResult"}}}}}}}, "/api/users/{gameUserId}/nickname": {"get": {"tags": ["UserCache"], "summary": "获取用户昵称", "parameters": [{"name": "gameUserId", "in": "path", "description": "游戏用户ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringResult"}}}}}}}, "/api/users/nicknames": {"post": {"tags": ["UserCache"], "summary": "批量获取用户昵称", "requestBody": {"description": "游戏用户ID列表", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringStringDictionaryResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringStringDictionaryResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringStringDictionaryResult"}}}}}}}}, "components": {"schemas": {"ActivityDetailResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "activityName": {"type": "string", "nullable": true}, "activityType": {"$ref": "#/components/schemas/ActivityType"}, "totalTongbao": {"type": "number", "format": "double"}, "remainingTongbao": {"type": "number", "format": "double"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/components/schemas/ActivityStatus"}, "participantsCount": {"type": "integer", "format": "int32"}, "creatorNickname": {"type": "string", "nullable": true}, "rewards": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityRewardResponseDto"}, "nullable": true}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/ActivityTaskResponseDto"}, "nullable": true}}, "additionalProperties": false}, "ActivityDetailResponseDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/ActivityDetailResponseDto"}}, "additionalProperties": false}, "ActivityResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "activityName": {"type": "string", "nullable": true}, "activityType": {"$ref": "#/components/schemas/ActivityType"}, "totalTongbao": {"type": "number", "format": "double"}, "remainingTongbao": {"type": "number", "format": "double"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/components/schemas/ActivityStatus"}, "participantsCount": {"type": "integer", "format": "int32"}, "creatorNickname": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ActivityResponseDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/ActivityResponseDto"}}, "additionalProperties": false}, "ActivityRewardResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "rewardName": {"type": "string", "nullable": true}, "rewardType": {"$ref": "#/components/schemas/RewardType"}, "tongbaoAmount": {"type": "number", "format": "double"}, "physicalItem": {"type": "string", "nullable": true}, "probability": {"type": "number", "format": "double"}, "remainingQuantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ActivityStatus": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "ActivityTaskResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "taskType": {"$ref": "#/components/schemas/TaskType"}, "taskName": {"type": "string", "nullable": true}, "targetValue": {"type": "integer", "format": "int32"}, "rewardChances": {"type": "integer", "format": "int32"}, "refreshType": {"$ref": "#/components/schemas/RefreshType"}}, "additionalProperties": false}, "ActivityType": {"enum": [0], "type": "integer", "format": "int32"}, "AssignPermissions_ReqDto": {"type": "object", "properties": {"subjectId": {"maxLength": 32, "type": "string", "nullable": true}, "permissionCodes": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BooleanResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "boolean", "description": "接口返回数据"}}, "additionalProperties": false}, "ButtonDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "permissionCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CacheUserNicknameDto": {"required": ["gameUserId", "nickname"], "type": "object", "properties": {"gameUserId": {"maxLength": 50, "minLength": 1, "type": "string"}, "nickname": {"maxLength": 100, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "ChangePasswordDto": {"required": ["newPassword", "oldPassword", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "oldPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "CreateActivityDto": {"required": ["activityName", "activityType", "endTime", "rewards", "startTime", "tasks", "totalTongbao"], "type": "object", "properties": {"activityName": {"maxLength": 200, "minLength": 1, "type": "string"}, "activityType": {"$ref": "#/components/schemas/ActivityType"}, "totalTongbao": {"minimum": 0.01, "type": "number", "format": "double"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "rewards": {"type": "array", "items": {"$ref": "#/components/schemas/CreateActivityRewardDto"}}, "tasks": {"type": "array", "items": {"$ref": "#/components/schemas/CreateActivityTaskDto"}}}, "additionalProperties": false}, "CreateActivityRewardDto": {"required": ["dailyQuantity", "reward<PERSON><PERSON>", "rewardType", "weight"], "type": "object", "properties": {"rewardName": {"maxLength": 200, "minLength": 1, "type": "string"}, "rewardType": {"$ref": "#/components/schemas/RewardType"}, "tongbaoAmount": {"type": "number", "format": "double"}, "physicalItem": {"maxLength": 500, "type": "string", "nullable": true}, "weight": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "dailyQuantity": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateActivityTaskDto": {"required": ["refreshType", "rewardChances", "targetValue", "taskName", "taskType"], "type": "object", "properties": {"taskType": {"$ref": "#/components/schemas/TaskType"}, "taskName": {"maxLength": 200, "minLength": 1, "type": "string"}, "targetValue": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "rewardChances": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}, "refreshType": {"$ref": "#/components/schemas/RefreshType"}}, "additionalProperties": false}, "CreateMenuDto": {"type": "object", "properties": {"parentId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "component": {"type": "string", "nullable": true}, "perms": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}, "orderNum": {"type": "integer", "format": "int32"}, "visible": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateRoleDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUserDto": {"required": ["password", "userName"], "type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "userName": {"maxLength": 50, "minLength": 1, "type": "string"}, "password": {"maxLength": 100, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "DictionaryDropdownDto": {"type": "object", "properties": {"value": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "disabled": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DictionaryDropdownDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DictionaryDropdownDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "DictionaryTypeDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "DictionaryTypeDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/DictionaryTypeDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "DrawChancesResponseDto": {"type": "object", "properties": {"totalChances": {"type": "integer", "format": "int32"}, "usedChances": {"type": "integer", "format": "int32"}, "remainingChances": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DrawChancesResponseDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/DrawChancesResponseDto"}}, "additionalProperties": false}, "DrawResultResponseDto": {"type": "object", "properties": {"drawId": {"type": "integer", "format": "int32"}, "reward": {"$ref": "#/components/schemas/DrawRewardDto"}, "remainingChances": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "DrawResultResponseDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/DrawResultResponseDto"}}, "additionalProperties": false}, "DrawRewardDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "rewardName": {"type": "string", "nullable": true}, "rewardType": {"$ref": "#/components/schemas/RewardType"}, "tongbaoAmount": {"type": "number", "format": "double"}, "physicalItem": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExportColumnDto": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "propertyName": {"type": "string", "nullable": true}, "order": {"type": "integer", "format": "int32"}, "format": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExportRequestDto": {"type": "object", "properties": {"columns": {"type": "array", "items": {"$ref": "#/components/schemas/ExportColumnDto"}, "nullable": true}, "fileName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GetTodayGameDataRequest": {"type": "object", "properties": {"clubId": {"type": "integer", "description": "活动创建者所属俱乐部ID", "format": "int32"}, "creatorId": {"type": "integer", "description": "活动创建者ID", "format": "int32"}}, "additionalProperties": false, "description": "获取今日游戏数据请求参数"}, "LogClearDto": {"required": ["beforeDate"], "type": "object", "properties": {"beforeDate": {"type": "string", "format": "date-time"}, "logType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequestDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LoginResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "userInfo": {"$ref": "#/components/schemas/UserInfoDto"}}, "additionalProperties": false}, "LoginResponseDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/LoginResponseDto"}}, "additionalProperties": false}, "MenuDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "parentId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "component": {"type": "string", "nullable": true}, "perms": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}, "orderNum": {"type": "integer", "format": "int32"}, "visible": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}, "nullable": true}, "buttons": {"type": "array", "items": {"$ref": "#/components/schemas/ButtonDto"}, "nullable": true}}, "additionalProperties": false}, "MenuDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "MenuDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/MenuDto"}}, "additionalProperties": false}, "ObjectResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "ProcedureTestRequest": {"type": "object", "properties": {"procedureName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RefreshType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "ResetPasswordDto": {"required": ["newPassword", "userId"], "type": "object", "properties": {"userId": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "Result": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}}, "additionalProperties": false, "description": "接口返回规范"}, "RewardType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "RoleDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "RoleDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}, "nullable": true}}, "additionalProperties": false}, "RoleDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/RoleDtoPageEntity"}}, "additionalProperties": false}, "RoleDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/RoleDto"}}, "additionalProperties": false}, "SmsLoginDto": {"required": ["phoneNumber", "verificationCode"], "type": "object", "properties": {"phoneNumber": {"minLength": 1, "pattern": "^1[3-9]\\d{9}$", "type": "string"}, "verificationCode": {"maxLength": 6, "minLength": 4, "type": "string"}}, "additionalProperties": false}, "StringResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "string", "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "StringStringDictionaryResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "SysDictionaryCreateDto": {"type": "object", "properties": {"dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "dictItemCode": {"type": "string", "nullable": true}, "dictItemName": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "parentId": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysDictionaryDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "dictItemCode": {"type": "string", "nullable": true}, "dictItemName": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isEnabled": {"type": "boolean"}, "parentId": {"type": "string", "nullable": true}, "remark": {"type": "string", "nullable": true}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysDictionaryDtoListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryDto"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "SysDictionaryDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/SysDictionaryDto"}, "nullable": true}}, "additionalProperties": false}, "SysDictionaryDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysDictionaryDtoPageEntity"}}, "additionalProperties": false}, "SysDictionaryUpdateDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "dictTypeCode": {"type": "string", "nullable": true}, "dictTypeName": {"type": "string", "nullable": true}, "dictItemCode": {"type": "string", "nullable": true}, "dictItemName": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "isEnabled": {"type": "boolean", "nullable": true}, "remark": {"type": "string", "nullable": true}, "extendField1": {"type": "string", "nullable": true}, "extendField2": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SysLog": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "operation": {"type": "string", "nullable": true}, "method": {"type": "string", "nullable": true}, "params": {"type": "string", "nullable": true}, "time": {"type": "integer", "format": "int64"}, "ip": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "logType": {"type": "string", "nullable": true}, "logLevel": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "exception": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "SysLogPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/SysLog"}, "nullable": true}}, "additionalProperties": false}, "SysLogPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysLogPageEntity"}}, "additionalProperties": false}, "SysLogResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/SysLog"}}, "additionalProperties": false}, "SysRole": {"required": ["code", "name"], "type": "object", "properties": {"remark": {"type": "string", "nullable": true}, "createdBy": {"maxLength": 32, "type": "string", "nullable": true}, "creatorName": {"maxLength": 50, "type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updatedBy": {"maxLength": 32, "type": "string", "nullable": true}, "updaterName": {"maxLength": 50, "type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}, "id": {"maxLength": 32, "type": "string", "nullable": true}, "name": {"minLength": 1, "type": "string"}, "code": {"minLength": 1, "type": "string"}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SysRoleListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SysRole"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "TaskType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "TimeSpan": {"type": "object", "properties": {"ticks": {"type": "integer", "format": "int64"}, "days": {"type": "integer", "format": "int32", "readOnly": true}, "hours": {"type": "integer", "format": "int32", "readOnly": true}, "milliseconds": {"type": "integer", "format": "int32", "readOnly": true}, "microseconds": {"type": "integer", "format": "int32", "readOnly": true}, "nanoseconds": {"type": "integer", "format": "int32", "readOnly": true}, "minutes": {"type": "integer", "format": "int32", "readOnly": true}, "seconds": {"type": "integer", "format": "int32", "readOnly": true}, "totalDays": {"type": "number", "format": "double", "readOnly": true}, "totalHours": {"type": "number", "format": "double", "readOnly": true}, "totalMilliseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMicroseconds": {"type": "number", "format": "double", "readOnly": true}, "totalNanoseconds": {"type": "number", "format": "double", "readOnly": true}, "totalMinutes": {"type": "number", "format": "double", "readOnly": true}, "totalSeconds": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "TodayGameDataResult": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int32"}, "nickname": {"type": "string", "nullable": true}, "clubId": {"type": "integer", "format": "int32"}, "clubName": {"type": "string", "nullable": true}, "gameCount": {"type": "integer", "format": "int32"}, "serviceFee": {"type": "number", "format": "double"}, "onlineDuration": {"type": "integer", "format": "int32"}, "gameDuration": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TodayGameDataResultListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TodayGameDataResult"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "UpdateMenuDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "parentId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "component": {"type": "string", "nullable": true}, "perms": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "type": {"type": "integer", "format": "int32"}, "orderNum": {"type": "integer", "format": "int32"}, "visible": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePointResult": {"type": "object", "properties": {"date": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int32"}, "nickname": {"type": "string", "nullable": true}, "clubId": {"type": "integer", "format": "int32"}, "clubName": {"type": "string", "nullable": true}, "gameCount": {"type": "integer", "format": "int32"}, "serviceFee": {"type": "number", "format": "double"}, "onlineDuration": {"$ref": "#/components/schemas/TimeSpan"}, "gameDuration": {"$ref": "#/components/schemas/TimeSpan"}}, "additionalProperties": false}, "UpdatePointResultListResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/UpdatePointResult"}, "description": "接口返回数据", "nullable": true}}, "additionalProperties": false}, "UpdateRoleDto": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "orderNum": {"type": "integer", "format": "int32"}, "dataScope": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "remark": {"type": "string", "nullable": true}, "menuIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateUserDto": {"required": ["userId"], "type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "userId": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "UpdateUserPointRequest": {"type": "object", "properties": {"clubId": {"type": "integer", "description": "活动创建者所属俱乐部ID", "format": "int32"}, "creatorId": {"type": "integer", "description": "活动创建者ID", "format": "int32"}, "operationType": {"type": "integer", "description": "操作类型：1-从用户身上扣除通宝注入奖池，2-从奖池中扣除通宝", "format": "int32"}, "targetUserId": {"type": "integer", "description": "被操作者ID", "format": "int32"}, "pointAmount": {"type": "integer", "description": "本次操作的通宝数量", "format": "int32"}}, "additionalProperties": false, "description": "更新用户通宝请求参数"}, "UserCacheResponseDto": {"type": "object", "properties": {"gameUserId": {"type": "string", "nullable": true}, "nickname": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserCacheResponseDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/UserCacheResponseDto"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"realName": {"maxLength": 50, "type": "string", "nullable": true}, "avatar": {"maxLength": 200, "type": "string", "nullable": true}, "email": {"maxLength": 100, "type": "string", "nullable": true}, "mobile": {"maxLength": 20, "type": "string", "nullable": true}, "remark": {"maxLength": 500, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "id": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "roleName": {"type": "string", "nullable": true}, "roleCode": {"type": "string", "nullable": true}, "lastLoginTime": {"type": "string", "format": "date-time", "nullable": true}, "lastLoginIp": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserDtoPageEntity": {"type": "object", "properties": {"pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalCount": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasPrevPages": {"type": "boolean", "readOnly": true}, "hasNextPages": {"type": "boolean", "readOnly": true}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}}, "additionalProperties": false}, "UserDtoPageEntityResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/UserDtoPageEntity"}}, "additionalProperties": false}, "UserDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/UserDto"}}, "additionalProperties": false}, "UserInfoDto": {"type": "object", "properties": {"userId": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "nickName": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "permissions": {"type": "array", "items": {"$ref": "#/components/schemas/MenuDto"}, "nullable": true}}, "additionalProperties": false}, "UserInfoDtoResult": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码", "format": "int32"}, "msg": {"type": "string", "description": "返回消息", "nullable": true}, "success": {"type": "boolean", "description": "返回结果"}, "data": {"$ref": "#/components/schemas/UserInfoDto"}}, "additionalProperties": false}, "ValidateDownUserRequest": {"type": "object", "properties": {"clubId": {"type": "integer", "description": "活动创建者所属俱乐部ID（必需）", "format": "int32"}, "creatorId": {"type": "integer", "description": "活动创建者ID（必需）", "format": "int32"}, "targetUserId": {"type": "integer", "description": "要验证的下级ID（必需）", "format": "int32"}}, "additionalProperties": false, "description": "验证下级用户请求参数"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "在下框中输入请求头中需要添加Jwt授权Token：Bearer Token", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}