using Common;
using Microsoft.AspNetCore.Mvc.Controllers;
using ShanxiGameActivity.Controllers.Attributes;

namespace ShanxiGameActivity.Controllers.Middleware
{
    /// <summary>
    /// IP验证中间件
    /// </summary>
    public class IPValidationMiddleware(RequestDelegate next)
    {
        private readonly RequestDelegate _next = next;

        /// <summary>
        /// 中间件处理方法
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        public async Task InvokeAsync(HttpContext context)
        {
            var endpoint = context.GetEndpoint();
            if (endpoint == null)
            {
                await _next(context);
                return;
            }

            var actionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
            if (actionDescriptor == null)
            {
                await _next(context);
                return;
            }

            // 检查是否有CardOpenValidation特性
            var hasCardOpenValidation = actionDescriptor.MethodInfo.GetCustomAttributes(typeof(CardOpenValidationAttribute), true).Length != 0 ||
                                      actionDescriptor.ControllerTypeInfo.GetCustomAttributes(typeof(CardOpenValidationAttribute), true).Length != 0;

            // 如果有CardOpenValidation特性，不进行IP验证
            if (hasCardOpenValidation)
            {
                await _next(context);
                return;
            }

            // 获取客户端IP
            string clientIp = GetClientIpAddress(context);

            // 验证IP是否在白名单中
            var allowedIPs = IPWhiteListSetting.AllowedIPs;
            if (!allowedIPs.Contains(clientIp))
            {
                context.Response.StatusCode = 200;
                await context.Response.WriteAsJsonAsync(new { code = 403, success = false, msg = "IP地址未授权访问" });
                return;
            }

            await _next(context);
        }



        /// <summary>
        /// 获取客户端IP地址
        /// </summary>
        private static string GetClientIpAddress(HttpContext context)
        {
            string ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault() ??
                       context.Request.Headers["X-Real-IP"].FirstOrDefault() ??
                       context.Connection.RemoteIpAddress?.ToString() ?? "";

            // 如果是X-Forwarded-For，可能包含多个IP，取第一个
            return ip.Split(',').FirstOrDefault()?.Trim() ?? "";
        }
    }

    /// <summary>
    /// IP验证中间件扩展方法
    /// </summary>
    public static class IPValidationMiddlewareExtensions
    {
        public static IApplicationBuilder UseIPValidation(this IApplicationBuilder builder) =>
            builder.UseMiddleware<IPValidationMiddleware>();
    }
}