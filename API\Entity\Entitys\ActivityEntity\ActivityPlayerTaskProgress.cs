using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 玩家任务进度表
/// </summary>
[Table("activity_player_task_progress")]
public class ActivityPlayerTaskProgress : BaseEntity_ID
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [Required]
    [Column("activity_id")]
    [Comment("活动ID")]
    public int ActivityId { get; set; }

    /// <summary>
    /// 任务ID
    /// </summary>
    [Required]
    [Column("task_id")]
    [Comment("任务ID")]
    public int TaskId { get; set; }

    /// <summary>
    /// 玩家游戏用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("player_game_user_id")]
    [Comment("玩家游戏用户ID")]
    public string PlayerGameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 玩家昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("player_nickname")]
    [Comment("玩家昵称")]
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 当前进度
    /// </summary>
    [Column("current_progress")]
    [Comment("当前进度")]
    public int CurrentProgress { get; set; } = 0;

    /// <summary>
    /// 完成次数
    /// </summary>
    [Column("completed_times")]
    [Comment("完成次数")]
    public int CompletedTimes { get; set; } = 0;

    /// <summary>
    /// 上次刷新时间
    /// </summary>
    [Column("last_refresh_time")]
    [Comment("上次刷新时间")]
    public DateTime? LastRefreshTime { get; set; }

    /// <summary>
    /// 上次完成时间
    /// </summary>
    [Column("last_completed_time")]
    [Comment("上次完成时间")]
    public DateTime? LastCompletedTime { get; set; }

    /// <summary>
    /// 奖励是否已领取
    /// </summary>
    [Column("reward_claimed")]
    [Comment("奖励是否已领取")]
    public bool RewardClaimed { get; set; } = false;

    /// <summary>
    /// 奖励领取时间
    /// </summary>
    [Column("reward_claimed_time")]
    [Comment("奖励领取时间")]
    public DateTime? RewardClaimedTime { get; set; }

    /// <summary>
    /// 获得的抽奖次数
    /// </summary>
    [Column("reward_chances_earned")]
    [Comment("获得的抽奖次数")]
    public int RewardChancesEarned { get; set; } = 0;

    /// <summary>
    /// 任务有效期开始时间
    /// </summary>
    [Column("valid_from")]
    [Comment("任务有效期开始时间")]
    public DateTime ValidFrom { get; set; }

    /// <summary>
    /// 任务有效期结束时间
    /// </summary>
    [Column("valid_to")]
    [Comment("任务有效期结束时间")]
    public DateTime ValidTo { get; set; }

}
