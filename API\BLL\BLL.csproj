﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="BasisBLL\**" />
    <EmbeddedResource Remove="BasisBLL\**" />
    <None Remove="BasisBLL\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Common.csproj" />
    <ProjectReference Include="..\DAL\DAL.csproj" />
    <ProjectReference Include="..\Entity\Entity.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.2" />
  </ItemGroup>

</Project>
