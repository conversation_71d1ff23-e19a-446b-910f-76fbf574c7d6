using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.ActivityEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.ActivityDAL
{
    /// <summary>
    /// 游戏数据同步数据访问层
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class GameDataSyncDAL(MyContext context) : BaseQueryDLL<ActivityGameDataSync, GameDataSyncDAL.GameDataSyncQuery>(context)
    {
        private readonly MyContext _context = context;

        /// <summary>
        /// 游戏数据同步查询条件模型类
        /// </summary>
        public class GameDataSyncQuery : PageQueryEntity
        {
            /// <summary>
            /// 同步日期
            /// </summary>
            [Query(QueryOperator.等于)]
            public DateTime? SyncDate { get; set; }

            /// <summary>
            /// 用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? UserId { get; set; }

            /// <summary>
            /// 联盟ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? ClubId { get; set; }
        }

        // 注意：原来的UpsertGameDataAsync方法已移除
        // 现在使用EF Core标准方法在服务层处理数据同步
        // 参见 GameDataSyncService.SyncGameDataWithEFCoreAsync() 方法

        /// <summary>
        /// 获取指定日期的游戏数据
        /// </summary>
        /// <param name="syncDate">同步日期</param>
        /// <returns>游戏数据列表</returns>
        public async Task<List<ActivityGameDataSync>> GetGameDataBySyncDateAsync(DateTime syncDate)
        {
            return await _context.GameDataSyncs
                .Where(x => x.SyncDate.Date == syncDate.Date)
                .ToListAsync();
        }

        /// <summary>
        /// 获取指定用户和日期范围的游戏数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>游戏数据列表</returns>
        public async Task<List<ActivityGameDataSync>> GetUserGameDataAsync(int userId, DateTime startDate, DateTime endDate)
        {
            return await _context.GameDataSyncs
                .Where(x => x.UserId == userId &&
                           x.SyncDate.Date >= startDate.Date &&
                           x.SyncDate.Date <= endDate.Date)
                .OrderBy(x => x.SyncDate)
                .ToListAsync();
        }
    }
}
