using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.CardDto
{
    /// <summary>
    /// 创建卡片批次DTO
    /// </summary>
    public class CreateCardBatchDto
    {
        /// <summary>
        /// 卡片所属渠道
        /// </summary>
        [Required(ErrorMessage = "渠道不能为空")]
        public string Channel { get; set; } = string.Empty;

        /// <summary>
        /// 卡片类型
        /// </summary>
        [Required(ErrorMessage = "卡片类型不能为空")]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 卡片面额
        /// </summary>
        [Required(ErrorMessage = "面额不能为空")]
        [Range(1, long.MaxValue, ErrorMessage = "面额必须大于0")]
        public long GameCurrency { get; set; }

        /// <summary>
        /// 卡片售价
        /// </summary>
        [Required(ErrorMessage = "售价不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "售价必须大于0")]
        public decimal Price { get; set; }

        /// <summary>
        /// 卡片数量
        /// </summary>
        [Required(ErrorMessage = "数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "数量必须大于0")]
        public int Quantity { get; set; }

        /// <summary>
        /// 批次备注信息
        /// </summary>
        public string? Remark { get; set; }
    }
}