using Common.MD5s;
using Common.Time;
using Microsoft.AspNetCore.Http;
using System.Text.Json;

namespace Common.Helper
{
    /// <summary>
    /// 签名验证工具类
    /// </summary>
    public class SignatureValidationHelper
    {


        /// <summary>
        /// 从HttpContext中获取并验证签名（用于中间件）
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="apiKey">API密钥</param>
        /// <param name="validTimeSpanMinutes">有效时间（分钟），默认5分钟</param>
        /// <returns>(bool isValid, string errorMessage, object? validatedObject)</returns>
        public static async Task<(bool isValid, string errorMessage, Dictionary<string, object>? validatedObject)> ValidateFromHttpContext(
            HttpContext context,
            string apiKey,
            int validTimeSpanMinutes = 5)
        {
            try
            {
                context.Request.EnableBuffering();

                using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
                var body = await reader.ReadToEndAsync();
                context.Request.Body.Position = 0;

                if (string.IsNullOrEmpty(body))
                    return (false, "请求体不能为空", null);

                var obj = JsonSerializer.Deserialize<Dictionary<string, object>>(body);
                if (obj == null)
                    return (false, "无法解析请求体", null);



                var (isValid, errorMessage) = ValidateSignature(obj, apiKey, validTimeSpanMinutes);
                return (isValid, errorMessage, obj);
            }
            catch (Exception ex)
            {
                return (false, $"验证过程发生异常: {ex.Message}", null);
            }
        }

        /// <summary>
        /// 验证签名（直接对象验证）
        /// </summary>
        /// <param name="obj">要验证的对象</param>
        /// <param name="apiKey">API密钥</param>
        /// <param name="validTimeSpanMinutes">有效时间（分钟），默认5分钟</param>
        /// <returns>(bool isValid, string errorMessage)</returns>
        private static (bool isValid, string errorMessage) ValidateSignature(Dictionary<string, object> orderedDict, string apiKey, int validTimeSpanMinutes = 5)
        {
            try
            {
                if (orderedDict == null) return (false, "参数不能为空");


                // 获取需要验证的属性并按ASCII排序
                var dicts = orderedDict
                    .OrderBy(q => q.Key, StringComparer.Ordinal);
                var strList = new List<string>();
                string sign = string.Empty;
                string timestamp = string.Empty;

                foreach (var (key, value) in dicts)
                {
                    if (key.Equals("sign", StringComparison.OrdinalIgnoreCase))
                    {
                        sign = value?.ToString() ?? string.Empty;
                    }
                    else if (key.Equals("tick", StringComparison.OrdinalIgnoreCase))
                    {
                        timestamp = value?.ToString() ?? string.Empty;

                        if (!string.IsNullOrEmpty(timestamp))
                        {
                            var ts = long.Parse(timestamp);
                            var nowUtc = TimeHelper.GetNowTimeStampMS();
                            var validTime = ts + 1000 * 60 * validTimeSpanMinutes;

#if !DEBUG
                            if (validTime < nowUtc) return (false, "请求已失效");
#endif

                        }
                        strList.Add($"{key}={value}");
                    }
                    else
                    {
                        var val = value is bool boolVal ? boolVal.ToString().ToLower() : value;
                        strList.Add($"{key.ToLower()}={val}");
                    }
                }

                var builder = string.Join("&", strList);
                builder += apiKey;
                var md5value = MD5Helper.Md5Upper(builder);



#if !DEBUG

                if (string.IsNullOrEmpty(sign) || !md5value.Equals(sign, StringComparison.OrdinalIgnoreCase))
                    return (false, "签名校验失败");
#endif

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                return (false, $"验证过程发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成签名
        /// </summary>
        /// <param name="obj">要签名的对象</param>
        /// <param name="apiKey">API密钥</param>
        /// <returns>签名字符串</returns>
        public static string GenerateSignature(object obj, string apiKey)
        {
            ArgumentNullException.ThrowIfNull(obj);

            try
            {
                var properties = obj.GetType().GetProperties()
                    .Where(p => !p.Name.Equals("Sign", StringComparison.OrdinalIgnoreCase));

                var propertyDict = properties.ToDictionary(
                    p => p.Name.ToLower(),
                    p => p.GetValue(obj, null) ?? string.Empty
                );

                var strList = propertyDict
                    .OrderBy(q => q.Key, StringComparer.Ordinal)
                    .Select(item =>
                    {
                        var val = item.Value is bool boolVal ? boolVal.ToString().ToLower() : item.Value;
                        return $"{item.Key}={val}";
                    })
                    .ToList();

                var builder = string.Join("&", strList) + apiKey;
                return MD5Helper.Md5Upper(builder);
            }
            catch (Exception ex)
            {
                throw new Exception($"生成签名时发生错误: {ex.Message}", ex);
            }
        }
    }
}