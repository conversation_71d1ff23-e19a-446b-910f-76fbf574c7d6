using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using StackExchange.Redis;
using System.Collections.Concurrent;

namespace Common.Redis;

/// <summary>
/// Redis 服务实现
/// </summary>
public class RedisService : IRedisService, IDisposable
{
    private readonly ILogger<RedisService> _logger;
    private readonly RedisOptions _options;
    private readonly ConnectionMultiplexer _connection;
    private readonly IDatabase _database;
    private bool _disposed;
    private readonly string _machineName = Environment.MachineName;

    private static readonly ConcurrentDictionary<string, ConnectionMultiplexer> _connections = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="options">Redis配置选项</param>
    /// <param name="logger">日志记录器</param>
    public RedisService(IOptions<RedisOptions> options, ILogger<RedisService> logger)
    {
        _logger = logger;
        _options = options.Value;

        var instance = string.IsNullOrEmpty(_options.DefaultInstanceName)
            ? RedisSetting.DefaultInstance
            : RedisSetting.GetInstance(_options.DefaultInstanceName);

        _connection = _connections.GetOrAdd(instance.InstanceName,
            name => ConnectionMultiplexer.Connect(instance.Connection));
        _database = _connection.GetDatabase(instance.DefaultDb);
    }

    /// <summary>
    /// 获取Redis数据库
    /// </summary>
    /// <param name="db">数据库索引</param>
    /// <returns>Redis数据库</returns>
    public IDatabase GetDatabase(int db = 0)
    {
        return db == _options.DefaultDb
            ? _database
            : _connection.GetDatabase(db);
    }

    /// <summary>
    /// 获取Redis服务器
    /// </summary>
    /// <param name="endPointsIndex">端点索引</param>
    /// <returns>Redis服务器</returns>
    public IServer GetServer(int endPointsIndex = 0)
    {
        try
        {
            return _connection.GetServer(_connection.GetEndPoints()[endPointsIndex]);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Redis服务器失败: {EndPointsIndex}", endPointsIndex);
            throw new Common.Exceptions.BusinessException("获取Redis服务器失败", ex);
        }
    }

    /// <summary>
    /// 获取Redis发布订阅对象
    /// </summary>
    /// <returns>Redis发布订阅对象</returns>
    public ISubscriber GetSubscriber()
    {
        return _connection.GetSubscriber();
    }

    /// <summary>
    /// 获取指定类型的缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <returns>缓存值，未找到返回默认值</returns>
    public T? Get<T>(string key)
    {
        try
        {
            TryGetValue(key, out T? value);
            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Redis缓存值失败: {Key}", key);
            return default;
        }
    }

    /// <summary>
    /// 尝试获取指定类型的缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">输出的缓存值</param>
    /// <returns>是否获取成功</returns>
    public bool TryGetValue<T>(string key, out T? value)
    {
        value = default;
        try
        {
            var redisValue = _database.StringGet(key);
            if (!redisValue.HasValue)
                return false;

            var stringValue = redisValue.ToString();
            if (string.IsNullOrEmpty(stringValue))
                return false;

            value = JsonConvert.DeserializeObject<T>(stringValue);
            return value != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "尝试获取Redis缓存值失败: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <param name="expiry">过期时间</param>
    /// <returns>是否设置成功</returns>
    public bool Set<T>(string key, T value, TimeSpan? expiry = null)
    {
        try
        {
            string json = JsonConvert.SerializeObject(value);
            return _database.StringSet(key, json, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置Redis缓存值失败: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 通过委托设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="valueFactory">生成缓存值的委托</param>
    /// <param name="expiry">过期时间</param>
    /// <returns>是否设置成功</returns>
    public bool Set<T>(string key, Func<T> valueFactory, TimeSpan? expiry = null)
    {
        try
        {
            T value = valueFactory();
            return Set(key, value, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "通过委托设置Redis缓存值失败: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 获取或设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="valueFactory">生成缓存值的委托</param>
    /// <param name="expiry">过期时间</param>
    /// <param name="cache">是否缓存结果</param>
    /// <returns>缓存值</returns>
    public T GetOrSet<T>(string key, Func<T> valueFactory, TimeSpan? expiry = null, bool cache = true)
    {
        if (TryGetValue(key, out T? value) && value != null)
            return value;

        value = valueFactory();

        if (cache && value != null)
            Set(key, value, expiry);

        return value;
    }

    /// <summary>
    /// 删除缓存键
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否删除成功</returns>
    public bool Remove(string key)
    {
        try
        {
            return _database.KeyDelete(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除Redis缓存键失败: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 批量删除缓存键
    /// </summary>
    /// <param name="keys">缓存键数组</param>
    /// <param name="flags">命令标志</param>
    /// <returns>成功删除的键数量</returns>
    public long Remove(string[] keys, CommandFlags flags = CommandFlags.None)
    {
        try
        {
            var redisKeys = Array.ConvertAll(keys, key => (RedisKey)key);
            return _database.KeyDelete(redisKeys, flags);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量删除Redis缓存键失败");
            return 0;
        }
    }

    /// <summary>
    /// 检查键是否存在
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否存在</returns>
    public bool Exists(string key)
    {
        try
        {
            return _database.KeyExists(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查Redis缓存键是否存在失败: {Key}", key);
            return false;
        }
    }

    /// <summary>
    /// 获取所有匹配的键
    /// </summary>
    /// <param name="database">数据库索引</param>
    /// <param name="pattern">匹配模式</param>
    /// <returns>匹配的键列表</returns>
    public List<string> GetKeys(int database = -1, string pattern = "*")
    {
        try
        {
            var server = GetServer();
            var db = database == -1 ? _options.DefaultDb : database;
            return [.. server.Keys(db, pattern).Select(key => key.ToString()).Where(key => key != null)];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Redis键列表失败: {Database}, {Pattern}", database, pattern);
            return [];
        }
    }

    /// <summary>
    /// Redis分布式锁
    /// </summary>
    /// <param name="key">锁键</param>
    /// <param name="action">锁内执行的操作</param>
    /// <param name="expiry">锁过期时间</param>
    public void Lock(string key, Action action, TimeSpan? expiry = null)
    {
        var lockKey = $"lock:{key}";
        var lockValue = $"{_machineName}:{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}";
        expiry ??= TimeSpan.FromSeconds(5);

        try
        {
            var acquired = false;
            var retryCount = 0;
            var maxRetries = 3;

            while (!acquired && retryCount < maxRetries)
            {
                acquired = _database.StringSet(lockKey, lockValue, expiry, When.NotExists);
                if (!acquired)
                {
                    retryCount++;
                    if (retryCount < maxRetries)
                    {
                        Thread.Sleep(200 * retryCount);
                    }
                }
            }

            if (!acquired)
            {
                throw new Common.Exceptions.BusinessException($"无法获取锁: {key}");
            }

            try
            {
                action();
            }
            finally
            {
                var currentValue = _database.StringGet(lockKey);
                if (currentValue == lockValue)
                {
                    _database.KeyDelete(lockKey);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis锁操作失败: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// 发布消息到指定频道
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="channel">频道名称</param>
    /// <param name="message">消息内容</param>
    /// <returns>接收到消息的客户端数量</returns>
    public long Publish<T>(string channel, T message)
    {
        try
        {
            var redisChannel = RedisChannel.Literal(channel);
            var value = JsonConvert.SerializeObject(message);
            return GetSubscriber().Publish(redisChannel, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布Redis消息失败: {Channel}", channel);
            return 0;
        }
    }

    /// <summary>
    /// 订阅指定频道
    /// </summary>
    /// <param name="channel">频道名称</param>
    /// <param name="handler">消息处理器</param>
    public void Subscribe(string channel, Action<RedisChannel, RedisValue> handler)
    {
        try
        {
            var redisChannel = RedisChannel.Literal(channel);
            GetSubscriber().Subscribe(redisChannel, handler);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅Redis频道失败: {Channel}", channel);
            throw new Common.Exceptions.BusinessException($"订阅Redis频道失败: {channel}", ex);
        }
    }

    /// <summary>
    /// 取消订阅指定频道
    /// </summary>
    /// <param name="channel">频道名称</param>
    public void Unsubscribe(string channel)
    {
        try
        {
            var redisChannel = RedisChannel.Literal(channel);
            GetSubscriber().Unsubscribe(redisChannel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅Redis频道失败: {Channel}", channel);
        }
    }

    /// <summary>
    /// 取消所有订阅
    /// </summary>
    public void UnsubscribeAll()
    {
        try
        {
            GetSubscriber().UnsubscribeAll();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消所有Redis订阅失败");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 这里不需要释放连接，因为连接是共享的
                // 只有在应用程序退出时才需要关闭连接
            }
            _disposed = true;
        }
    }

    /// <summary>
    /// 析构函数
    /// </summary>
    ~RedisService()
    {
        Dispose(false);
    }
}