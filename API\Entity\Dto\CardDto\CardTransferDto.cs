namespace Entity.Dto.CardDto
{






    /// <summary>
    /// 卡片流转记录DTO
    /// </summary>
    public class CardTransferRecordDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string RecordId { get; set; } = string.Empty;

        /// <summary>
        /// 卡号
        /// </summary>
        public string OrderId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人
        /// </summary>
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// 目标人
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 操作数量
        /// </summary>
        public int OperationCount { get; set; }

        /// <summary>
        /// 卡片流转状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 交易金额
        /// </summary>
        public decimal? Amount { get; set; }

        /// <summary>
        /// 操作IP
        /// </summary>
        public string? Ip { get; set; }

        /// <summary>
        /// 流转时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 创建卡片流转记录DTO
    /// </summary>
    public class CreateCardTransferDto
    {
        /// <summary>
        /// 卡号
        /// </summary>
        public string OrderId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人
        /// </summary>
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// 目标人
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 操作IP
        /// </summary>
        public string? Ip { get; set; }
    }

    /// <summary>
    /// 用户卡片汇总DTO
    /// </summary>
    public class UserCardSummaryDto
    {
        /// <summary>
        /// 面值
        /// </summary>
        public string FaceValue { get; set; } = string.Empty;

        /// <summary>
        /// 收到的卡数量
        /// </summary>
        public int ReceivedCount { get; set; }

        /// <summary>
        /// 赠送的卡数量
        /// </summary>
        public int SentCount { get; set; }

        /// <summary>
        /// 充值的卡数量
        /// </summary>
        public int RechargedCount { get; set; }

        /// <summary>
        /// 剩余的卡数量
        /// </summary>
        public int RemainingCount { get; set; }
    }

    /// <summary>
    /// 卡片操作详细信息DTO
    /// </summary>
    public class CardOperationDetailDto
    {
        /// <summary>
        /// 操作人ID
        /// </summary>
        public string OperatorId { get; set; } = string.Empty;

        /// <summary>
        /// 目标人ID
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 操作日期
        /// </summary>
        public string OperationDate { get; set; } = string.Empty;

        /// <summary>
        /// 各面值的卡数量
        /// </summary>
        public Dictionary<string, int> FaceValues { get; set; } = [];

        /// <summary>
        /// 操作总数量
        /// </summary>
        public int OperationCount { get; set; }
    }

    /// <summary>
    /// 用户卡片查询DTO
    /// </summary>
    public class UserCardQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }
}