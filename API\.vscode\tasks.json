{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/AdminProjectTemplate/AdminProjectTemplate.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/AdminProjectTemplate/AdminProjectTemplate.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/AdminProjectTemplate/AdminProjectTemplate.csproj"], "problemMatcher": "$msCompile"}, {"label": "run", "command": "dotnet", "type": "process", "args": ["run", "--project", "DataMgrSystem", "--urls=https://localhost:7048"], "problemMatcher": "$msCompile"}]}