using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 玩家抽奖次数表
/// </summary>
[Table("activity_player_draw_chances")]
public class ActivityPlayerDrawChance : BaseEntity_ID
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [Required]
    [Column("activity_id")]
    [Comment("活动ID")]
    public int ActivityId { get; set; }

    /// <summary>
    /// 玩家游戏用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("player_game_user_id")]
    [Comment("玩家游戏用户ID")]
    public string PlayerGameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 玩家昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("player_nickname")]
    [Comment("玩家昵称")]
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 总次数
    /// </summary>
    [Column("total_chances")]
    [Comment("总次数")]
    public int TotalChances { get; set; } = 0;

    /// <summary>
    /// 已使用次数
    /// </summary>
    [Column("used_chances")]
    [Comment("已使用次数")]
    public int UsedChances { get; set; } = 0;

    /// <summary>
    /// 剩余次数
    /// </summary>
    [Column("remaining_chances")]
    [Comment("剩余次数")]
    public int RemainingChances { get; set; } = 0;

}
