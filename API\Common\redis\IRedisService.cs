using StackExchange.Redis;

namespace Common.Redis;

/// <summary>
/// Redis服务接口，用于定义Redis操作方法
/// </summary>
public interface IRedisService
{
    /// <summary>
    /// 获取指定类型的缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <returns>缓存的值，如未找到则返回默认值</returns>
    T? Get<T>(string key);

    /// <summary>
    /// 尝试获取指定类型的缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">输出的缓存值</param>
    /// <returns>是否获取成功</returns>
    bool TryGetValue<T>(string key, out T? value);

    /// <summary>
    /// 设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">要缓存的值</param>
    /// <param name="expiry">过期时间，为null则永不过期</param>
    /// <returns>是否设置成功</returns>
    bool Set<T>(string key, T value, TimeSpan? expiry = null);

    /// <summary>
    /// 通过委托方法设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="valueFactory">生成缓存值的委托</param>
    /// <param name="expiry">过期时间，为null则永不过期</param>
    /// <returns>是否设置成功</returns>
    bool Set<T>(string key, Func<T> valueFactory, TimeSpan? expiry = null);

    /// <summary>
    /// 删除指定的缓存键
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否删除成功</returns>
    bool Remove(string key);

    /// <summary>
    /// 检查键是否存在
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否存在</returns>
    bool Exists(string key);

    /// <summary>
    /// 获取Redis数据库
    /// </summary>
    /// <param name="db">数据库索引，默认为0</param>
    /// <returns>Redis数据库对象</returns>
    IDatabase GetDatabase(int db = 0);

    /// <summary>
    /// 获取 Redis 服务器
    /// </summary>
    IServer GetServer(int endPointsIndex = 0);

    /// <summary>
    /// 获取订阅器
    /// </summary>
    ISubscriber GetSubscriber();

    /// <summary>
    /// 获取或设置缓存
    /// </summary>
    T GetOrSet<T>(string key, Func<T> func, TimeSpan? expiry = null, bool cache = true);

    /// <summary>
    /// 批量移除缓存
    /// </summary>
    long Remove(string[] keys, CommandFlags flags = CommandFlags.None);

    /// <summary>
    /// 获取所有键
    /// </summary>
    List<string> GetKeys(int database = -1, string pattern = "*");

    /// <summary>
    /// Redis 分布式锁
    /// </summary>
    void Lock(string key, Action action, TimeSpan? expiry = null);

    /// <summary>
    /// 发布消息
    /// </summary>
    long Publish<T>(string channel, T message);

    /// <summary>
    /// 订阅消息
    /// </summary>
    void Subscribe(string channel, Action<RedisChannel, RedisValue> handler);

    /// <summary>
    /// 取消订阅
    /// </summary>
    void Unsubscribe(string channel);

    /// <summary>
    /// 取消所有订阅
    /// </summary>
    void UnsubscribeAll();
}