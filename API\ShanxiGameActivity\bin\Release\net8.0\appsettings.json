{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "BasisConfig": {
    "DataBaseConnectionStrings": {
      "MysqlConnectionString": "Server=************;Port=33066;User=datamgrsys;Password=******;Database=datamgrsystem",
      //"MysqlConnectionString": "Server=127.0.0.1;User=root;Password=my@root;Database=datamgrsystem"

    },
    "Redis": {
      "Instances": [
        {
          "InstanceName": "main",
          "Connection": "127.0.0.1:6380,password=******",
          "DefaultDb": 0
        },
        {
          "InstanceName": "game",
          "Connection": "************:63790,password=******",
          "DefaultDb": 0
        }
      ]
    },
    "JWT": {
      "sign": "aVeryLongRandomStringThatIsAtLeast32Characters",
      "exp": 60000,
      "Issuer": "AdminProjectTemplate",
      "Audience": "AdminProjectTemplate"
    },
    "RabbitMQ": {
      "HostName": "127.0.0.1",
      "Port": 5672,
      "UserName": "guest",
      "Password": "guest",
      "VirtualHost": "my_vhost",
      "MaxRetryCount": 3,
      "ConnectionPoolSize": 5
    },
    "LogService": {
      "Path": "https://localhost:5001/CreateLogInfo"
    },
    "ExternalGameServer": {
      "Base64Dict": "FG567HU4VabcWXdYZeAfghB89CijDklmnIJTopEqKLO23RSrwxMNyzstPQuv01+/=",
      "DefaultApiUrl": "http://************:52100/@api",
      "DefaultEncryptKey": "卡充通讯key"
    },
    "IPWhiteList": {
      "AllowedIPs": [
        "*************",
        "127.0.0.1",
        "::1"
      ]
    },
    "CardpoenApi": {
      "OpenApiKey": "CardOpenApiKeyValue"
    }
  },
  "AliSMS": {
    "Endpoint": "dysmsapi.aliyuncs.com",
    "AccessKeyId": "LTAI5tBbFVCb5iYzjAMc1TYQ",
    "AccessKeySecret": "******************************",
    "SignName": "唐人游"
  },
  "SmsVerification": {
    "ExpirationMinutes": 5,
    "CodeLength": 6
  },
  "SmsLogin": {
    "AllowAutoRegister": true,
    "SetRandomPassword": true
  }
}