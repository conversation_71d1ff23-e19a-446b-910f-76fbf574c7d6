using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 系统菜单实体
    /// </summary>
    [Table("sys_menu")]
    [Index(nameof(Name), IsUnique = true)]
    public class SysMenu : BaseEntity_GUID
    {
        /// <summary>
        /// 父级ID
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        [Comment("父级ID")]
        public string? ParentId { get; set; }

        /// <summary>
        /// 菜单名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Comment("菜单名称")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 菜单路径
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        [Comment("菜单路径")]
        public string? Path { get; set; }

        /// <summary>
        /// 组件路径
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        [Comment("组件路径")]
        public string? Component { get; set; }

        /// <summary>
        /// 权限标识
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        [Comment("权限标识")]
        public string? Perms { get; set; }

        /// <summary>
        /// 图标
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        [Comment("图标")]
        public string? Icon { get; set; }

        /// <summary>
        /// 类型（0：目录，1：菜单，2：按钮）
        /// </summary>
        [Column(TypeName = "tinyint")]
        [Comment("类型（0：目录，1：菜单，2：按钮）")]
        public byte Type { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Column(TypeName = "int")]
        [Comment("排序")]
        public int OrderNum { get; set; }

        /// <summary>
        /// 是否可见（0：隐藏，1：显示）
        /// </summary>
        [Column(TypeName = "tinyint")]
        [Comment("是否可见（0：隐藏，1：显示）")]
        public byte Visible { get; set; } = 1;

        /// <summary>
        /// 状态（0：禁用，1：启用）
        /// </summary>
        [Column(TypeName = "tinyint")]
        [Comment("状态（0：禁用，1：启用）")]
        public byte Status { get; set; } = 1;

    }
}