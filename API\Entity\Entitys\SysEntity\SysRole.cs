using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 系统角色实体
    /// </summary>
    [Table("sys_role")]
    public class SysRole : BaseEntity_GUID
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(50)")]
        [Comment("角色名称")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 角色编码
        /// </summary>
        [Required]
        [Column(TypeName = "varchar(50)")]
        [Comment("角色编码")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        [Comment("排序号")]
        public int OrderNum { get; set; }

        /// <summary>
        /// 数据范围（1：全部数据权限 2：自定义数据权限 3：本部门数据权限 4：本部门及以下数据权限）
        /// </summary>
        [Column(TypeName = "tinyint")]
        [Comment("数据范围（1：全部数据权限 2：自定义数据权限 3：本部门数据权限 4：本部门及以下数据权限）")]
        public byte DataScope { get; set; } = 1;

        /// <summary>
        /// 状态（0：禁用 1：正常）
        /// </summary>
        [Column(TypeName = "tinyint")]
        [Comment("状态（0：禁用 1：正常）")]
        public byte Status { get; set; } = 1;

    }
}