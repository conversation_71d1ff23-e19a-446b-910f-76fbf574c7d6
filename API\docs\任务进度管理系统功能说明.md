# 任务进度管理系统功能说明

## 概述

本系统实现了完整的活动任务进度管理功能，包括数据同步、任务进度跟踪、奖励领取、抽奖系统、跑马灯功能和活动状态管理。

## 核心功能

### 1. 任务进度管理系统
- **数据同步机制**：定时从游戏数据库同步玩家游戏数据
- **任务进度计算**：根据游戏数据实时计算任务完成进度
- **奖励领取控制**：防止重复领取，记录领取状态和时间
- **统一时效机制**：支持daily、weekly、never三种任务刷新类型

### 2. 完整抽奖系统
- **权重随机抽取**：基于奖励权重的公平抽奖算法
- **抽奖记录管理**：完整记录每次抽奖行为
- **中奖记录跟踪**：管理中奖状态、处理进度
- **统计分析功能**：提供详细的抽奖和中奖统计

### 3. 跑马灯功能
- **自动消息生成**：中奖时自动创建跑马灯消息
- **消息格式化**：统一的消息显示格式
- **显示状态管理**：跟踪消息显示状态，支持批量操作
- **过期清理**：自动清理过期的跑马灯消息

### 4. 活动状态管理
- **自动状态转换**：根据时间和条件自动更新活动状态
- **每日重置功能**：自动重置每日奖励份数
- **手动控制**：支持手动启动/停止活动
- **状态监控**：实时监控活动状态变化

## API接口

### 任务进度相关
```
GET  /api/TaskProgress/{activityId}/{playerGameUserId}     # 获取用户任务进度
POST /api/TaskProgress/claim-reward                        # 领取任务奖励
POST /api/TaskProgress/update-progress/{activityId}        # 手动更新活动任务进度
POST /api/TaskProgress/update-all-progress                 # 手动更新所有任务进度
GET  /api/TaskProgress/activity-stats/{activityId}         # 获取活动任务统计
```

### 数据同步相关
```
POST /api/GameDataSync/sync-all                           # 同步所有活动游戏数据
POST /api/GameDataSync/sync-creator                       # 同步指定创建者游戏数据
GET  /api/GameDataSync/sync-status                        # 获取同步状态
```

### 抽奖相关
```
GET  /api/Draw/chances/{activityId}/{playerGameUserId}    # 获取玩家抽奖次数
POST /api/Draw/draw                                       # 执行抽奖
POST /api/Draw/add-chances                                # 增加玩家抽奖次数
GET  /api/Draw/records/{activityId}/{playerGameUserId}    # 获取玩家抽奖记录
GET  /api/Draw/prizes/{activityId}/{playerGameUserId}     # 获取玩家中奖记录
GET  /api/Draw/stats/{activityId}                         # 获取活动抽奖统计
POST /api/Draw/update-prize-status                        # 更新中奖记录状态
```

### 跑马灯相关
```
GET  /api/Marquee/messages/{activityId}                   # 获取活动跑马灯消息
GET  /api/Marquee/latest                                  # 获取最新跑马灯消息
GET  /api/Marquee/formatted/{activityId}                  # 获取格式化消息文本
POST /api/Marquee/mark-displayed/{messageId}              # 标记消息已显示
POST /api/Marquee/batch-mark-displayed                    # 批量标记已显示
GET  /api/Marquee/stats/{activityId}                      # 获取跑马灯统计
POST /api/Marquee/clean-expired                           # 清理过期消息
POST /api/Marquee/create-message                          # 手动创建跑马灯消息
```

### 活动状态管理相关
```
POST /api/ActivityStatus/update-all                       # 更新所有活动状态
POST /api/ActivityStatus/update/{activityId}              # 更新指定活动状态
POST /api/ActivityStatus/stop/{activityId}                # 手动停止活动
POST /api/ActivityStatus/start/{activityId}               # 手动启动活动
POST /api/ActivityStatus/reset-daily-rewards/{activityId} # 重置每日奖励
```

## 后台服务

### 1. GameDataSyncBackgroundService
- **功能**：定时同步游戏数据并更新任务进度
- **默认间隔**：5分钟
- **配置项**：`GameDataSync:IntervalMinutes`

### 2. ActivityStatusBackgroundService
- **功能**：定时检查和更新活动状态，执行每日重置
- **默认间隔**：1分钟
- **配置项**：`ActivityStatus:CheckIntervalMinutes`、`ActivityStatus:DailyResetHour`

## 配置说明

在 `appsettings.json` 中添加以下配置：

```json
{
  "GameDataSync": {
    "IntervalMinutes": 5,
    "EnableAutoSync": true
  },
  "ActivityStatus": {
    "CheckIntervalMinutes": 1,
    "DailyResetHour": 0
  }
}
```

## 数据库表结构

### 新增表
- `game_data_sync`：游戏数据同步表
- `draw_records`：抽奖记录表（已存在）
- `prize_records`：中奖记录表（已存在）
- `marquee_messages`：跑马灯消息表（已存在）

### 扩展字段
- `player_task_progress`：添加奖励相关字段
- `prize_record`：添加处理备注和处理时间字段
- `marquee_message`：添加显示状态字段
- `activity_reward`：添加上次重置时间字段

## 部署说明

1. 运行数据库迁移以创建新表和字段
2. 确保配置文件中的数据库连接字符串正确
3. 启动应用程序，后台服务将自动开始运行
4. 可通过API接口手动触发数据同步和状态更新

## 注意事项

1. 首次部署时建议手动执行一次数据同步
2. 定期检查后台服务的运行状态和日志
3. 根据实际需求调整同步间隔和重置时间
4. 定期清理过期的跑马灯消息以节省存储空间
