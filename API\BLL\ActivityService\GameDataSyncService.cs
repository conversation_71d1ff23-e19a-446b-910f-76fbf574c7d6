using Common.Autofac;
using DAL.ActivityDAL;
using Entity.Entitys.ActivityEntity;
using Microsoft.Extensions.Logging;

namespace BLL.ActivityService
{
    /// <summary>
    /// 游戏数据同步服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class GameDataSyncService(
        ActivityDAL activityDAL,
        GameDataSyncDAL gameDataSyncDAL,
        TaskProgressService taskProgressService,
        PlayerTaskProgressDAL playerTaskProgressDAL,
        ActivityTaskDAL activityTaskDAL,
        ILogger<GameDataSyncService> logger)
    {
        private readonly ActivityDAL _activityDAL = activityDAL;
        private readonly GameDataSyncDAL _gameDataSyncDAL = gameDataSyncDAL;
        private readonly TaskProgressService _taskProgressService = taskProgressService;
        private readonly PlayerTaskProgressDAL _playerTaskProgressDAL = playerTaskProgressDAL;
        private readonly ActivityTaskDAL _activityTaskDAL = activityTaskDAL;
        private readonly ILogger<GameDataSyncService> _logger = logger;

        /// <summary>
        /// 同步所有活动的游戏数据
        /// </summary>
        /// <returns>同步结果</returns>
        public async Task<GameDataSyncResult> SyncAllActivityGameDataAsync()
        {
            var result = new GameDataSyncResult();

            try
            {
                _logger.LogInformation("开始同步游戏数据");

                // ==================== 【获取数据】阶段 ====================
                // 1. 获取所有进行中活动的创建者列表
                var creators = await _activityDAL.GetActiveActivityCreatorsAsync();
                if (creators.Count == 0)
                {
                    _logger.LogInformation("没有找到进行中的活动");
                    return result;
                }

                _logger.LogInformation("找到 {CreatorCount} 个活动创建者", creators.Count);

                // ==================== 【数据处理】阶段 ====================
                // 2. 逐个处理每个活动创建者的数据同步和任务进度更新
                foreach (var (creatorId, clubId) in creators)
                {
                    try
                    {
                        _logger.LogInformation("开始处理创建者 {CreatorId} 的数据同步", creatorId);

                        // ========== 【获取数据】子阶段 ==========
                        // 2.1 获取该创建者的游戏数据
                        var todayGameData = await _activityDAL.GetTodayUsersGameDataAsync(clubId, creatorId);

                        if (todayGameData.Count == 0)
                        {
                            _logger.LogInformation("创建者 {CreatorId} 没有游戏数据", creatorId);
                            result.ProcessedCreators++;
                            continue;
                        }

                        // ========== 【插入数据】子阶段 ==========
                        // 2.2 使用EF Core方式同步游戏数据（替代原生SQL）
                        var syncedRecords = await SyncGameDataWithEFCoreAsync(todayGameData);
                        result.SyncedRecords += syncedRecords;

                        _logger.LogInformation("创建者 {CreatorId} 同步了 {SyncedRecords} 条游戏数据", creatorId, syncedRecords);

                        // ========== 【计算进度】子阶段 ==========
                        // 2.3 立即更新该创建者相关活动的任务进度
                        // 这样可以通过创建者ID准确关联用户与活动的关系
                        await UpdateTaskProgressForCreatorAsync(creatorId, todayGameData);

                        result.ProcessedCreators++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理创建者 {CreatorId} 的数据同步失败", creatorId);
                        result.FailedCreators++;
                    }
                }

                result.Success = true;
                result.Message = $"同步完成，处理了 {result.ProcessedCreators} 个创建者，同步了 {result.SyncedRecords} 条记录";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步游戏数据时发生异常");
                result.Success = false;
                result.Message = $"同步失败：{ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 同步指定创建者的游戏数据
        /// </summary>
        /// <param name="clubId">俱乐部ID</param>
        /// <param name="creatorId">创建者ID</param>
        /// <returns>同步结果</returns>
        public async Task<GameDataSyncResult> SyncCreatorGameDataAsync(int clubId, int creatorId)
        {
            var result = new GameDataSyncResult();

            try
            {
                _logger.LogInformation("开始同步创建者 {CreatorId} 的游戏数据", creatorId);

                // 获取游戏数据
                var todayGameData = await _activityDAL.GetTodayUsersGameDataAsync(clubId, creatorId);

                if (todayGameData.Count == 0)
                {
                    result.Success = true;
                    result.Message = "没有找到游戏数据";
                    return result;
                }

                // ========== 【插入数据】子阶段 ==========
                // 使用EF Core方式同步游戏数据
                var affectedRows = await SyncGameDataWithEFCoreAsync(todayGameData);

                // ========== 【计算进度】子阶段 ==========
                // 立即更新该创建者相关活动的任务进度
                await UpdateTaskProgressForCreatorAsync(creatorId, todayGameData);

                result.Success = true;
                result.ProcessedCreators = 1;
                result.SyncedRecords = affectedRows;
                result.Message = $"成功同步 {affectedRows} 条记录";

                _logger.LogInformation("创建者 {CreatorId} 同步完成，影响 {AffectedRows} 条记录", creatorId, affectedRows);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步创建者 {CreatorId} 的游戏数据失败", creatorId);
                result.Success = false;
                result.Message = $"同步失败：{ex.Message}";
            }

            return result;
        }

        /// <summary>
        /// 【插入数据】使用EF Core方式同步游戏数据（替代原生SQL批量操作）
        /// </summary>
        /// <param name="todayGameData">今日游戏数据列表</param>
        /// <returns>同步的记录数</returns>
        private async Task<int> SyncGameDataWithEFCoreAsync(List<TodayGameDataResult> todayGameData)
        {
            try
            {
                var today = DateTime.Today;
                var syncedCount = 0;

                // ========== 【数据转换】子步骤 ==========
                // 转换为GameDataSync实体列表
                var gameDataSyncList = todayGameData.Select(data => new ActivityGameDataSync
                {
                    SyncDate = today,
                    UserId = data.UserId,
                    Nickname = data.Nickname,
                    ClubId = data.ClubId,
                    ClubName = data.ClubName,
                    GameCount = data.GameCount,
                    ServiceFee = data.ServiceFee,
                    OnlineDuration = data.OnlineDuration,
                    GameDuration = data.GameDuration,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                }).ToList();

                // ========== 【插入/更新数据】子步骤 ==========
                // 使用EF Core进行批量插入/更新操作
                foreach (var gameData in gameDataSyncList)
                {
                    // 查找是否已存在相同日期和用户的记录
                    var existingRecord = await _gameDataSyncDAL.GetListAsync(new GameDataSyncDAL.GameDataSyncQuery
                    {
                        SyncDate = today,
                        UserId = gameData.UserId
                    });

                    if (existingRecord.Count > 0)
                    {
                        // 更新现有记录
                        var existing = existingRecord.First();
                        existing.Nickname = gameData.Nickname;
                        existing.ClubName = gameData.ClubName;
                        existing.GameCount = gameData.GameCount;
                        existing.ServiceFee = gameData.ServiceFee;
                        existing.OnlineDuration = gameData.OnlineDuration;
                        existing.GameDuration = gameData.GameDuration;
                        existing.UpdateTime = DateTime.Now;

                        await _gameDataSyncDAL.UpdateAsync(existing);
                        syncedCount++;
                    }
                    else
                    {
                        // 插入新记录
                        await _gameDataSyncDAL.AddAsync(gameData);
                        syncedCount++;
                    }
                }

                return syncedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用EF Core同步游戏数据失败");
                throw;
            }
        }

        /// <summary>
        /// 【计算进度】更新指定创建者相关活动的任务进度
        /// 通过创建者ID准确关联用户与活动的关系，避免全局更新的不准确性
        /// </summary>
        /// <param name="creatorId">活动创建者ID</param>
        /// <param name="todayGameData">今日游戏数据</param>
        private async Task UpdateTaskProgressForCreatorAsync(int creatorId, List<TodayGameDataResult> todayGameData)
        {
            try
            {
                _logger.LogInformation("开始更新创建者 {CreatorId} 相关活动的任务进度", creatorId);

                // ========== 【获取数据】子步骤 ==========
                // 1. 获取该创建者的所有进行中活动
                var creatorActivities = await _activityDAL.GetListAsync(new ActivityDAL.ActivityQuery
                {
                    CreatorGameUserId = creatorId.ToString(),
                    Status = Entity.ActivityStatus.running
                });

                if (creatorActivities.Count == 0)
                {
                    _logger.LogInformation("创建者 {CreatorId} 没有进行中的活动", creatorId);
                    return;
                }

                _logger.LogInformation("创建者 {CreatorId} 有 {ActivityCount} 个进行中的活动", creatorId, creatorActivities.Count);

                // ========== 【计算进度】子步骤 ==========
                // 2. 为每个活动更新任务进度
                foreach (var activity in creatorActivities)
                {
                    await UpdateActivityTaskProgressWithGameDataAsync(activity, todayGameData);
                }

                _logger.LogInformation("完成创建者 {CreatorId} 相关活动的任务进度更新", creatorId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新创建者 {CreatorId} 任务进度失败", creatorId);
                throw;
            }
        }

        /// <summary>
        /// 【计算进度】根据游戏数据更新指定活动的任务进度
        /// 详细处理任务类型判断和时效性检查
        /// </summary>
        /// <param name="activity">活动信息</param>
        /// <param name="todayGameData">今日游戏数据</param>
        private async Task UpdateActivityTaskProgressWithGameDataAsync(ActivityInfo activity, List<TodayGameDataResult> todayGameData)
        {
            try
            {
                _logger.LogDebug("开始更新活动 {ActivityId} 的任务进度", activity.Id);

                // ========== 【获取数据】子步骤 ==========
                // 1. 获取活动的所有任务配置
                var activityTasks = await _activityTaskDAL.GetListAsync(new ActivityTaskDAL.ActivityTaskQuery
                {
                    ActivityId = activity.Id
                });
                if (activityTasks.Count == 0)
                {
                    _logger.LogDebug("活动 {ActivityId} 没有配置任务", activity.Id);
                    return;
                }

                // ========== 【数据处理】子步骤 ==========
                // 2. 按用户分组处理游戏数据
                var userGameDataDict = todayGameData.ToDictionary(g => g.UserId, g => g);

                // ========== 【计算进度】子步骤 ==========
                // 3. 为每个任务更新所有相关用户的进度
                foreach (var task in activityTasks)
                {
                    await UpdateTaskProgressForAllUsersAsync(activity, task, userGameDataDict);
                }

                _logger.LogDebug("完成活动 {ActivityId} 的任务进度更新", activity.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新活动 {ActivityId} 任务进度失败", activity.Id);
                throw;
            }
        }

        /// <summary>
        /// 为指定任务更新所有用户的进度
        /// 包含详细的任务类型判断和时效性处理逻辑
        /// </summary>
        /// <param name="activity">活动信息</param>
        /// <param name="task">任务配置</param>
        /// <param name="userGameDataDict">用户游戏数据字典</param>
        private async Task UpdateTaskProgressForAllUsersAsync(
            ActivityInfo activity,
           ActivityTask task,
            Dictionary<int, TodayGameDataResult> userGameDataDict)
        {
            try
            {
                var now = DateTime.Now;
                var today = DateTime.Today;

                // 1. 计算任务的有效期（根据任务刷新类型）
                var (validFrom, validTo) = CalculateTaskValidPeriod(task, activity.StartTime, activity.EndTime, now);

                _logger.LogDebug("任务 {TaskId} 有效期：{ValidFrom} - {ValidTo}，刷新类型：{RefreshType}",
                    task.Id, validFrom, validTo, task.RefreshType);

                // 2. 为每个有游戏数据的用户更新任务进度
                foreach (var (userId, gameData) in userGameDataDict)
                {
                    await UpdateSingleUserTaskProgressAsync(activity.Id, task, userId.ToString(), gameData.Nickname, gameData, validFrom, validTo, now);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务 {TaskId} 的用户进度失败", task.Id);
                throw;
            }
        }

        /// <summary>
        /// 【计算进度】更新单个用户的任务进度
        /// 包含任务类型判断、进度计算、时效性检查等完整逻辑
        /// </summary>
        private async Task UpdateSingleUserTaskProgressAsync(
            int activityId,
            ActivityTask task,
            string playerGameUserId,
            string playerNickname,
            TodayGameDataResult gameData,
            DateTime validFrom,
            DateTime validTo,
            DateTime now)
        {
            try
            {
                // ========== 【获取数据】子步骤 ==========
                // 1. 获取或创建任务进度记录
                var progress = await _playerTaskProgressDAL.GetOrCreatePlayerTaskProgressAsync(
                    activityId, task.Id, playerGameUserId, playerNickname, validFrom, validTo);

                // ========== 【时效性检查】子步骤 ==========
                // 2. 检查任务是否在有效期内
                if (now < validFrom || now > validTo)
                {
                    _logger.LogDebug("任务 {TaskId} 用户 {UserId} 不在有效期内，跳过更新", task.Id, playerGameUserId);
                    return;
                }

                // ========== 【进度重置检查】子步骤 ==========
                // 3. 检查是否需要重置进度（处理跨天/跨周任务）
                var shouldReset = ShouldResetTaskProgress(progress, task, now);
                if (shouldReset)
                {
                    ResetTaskProgress(progress, now);
                    _logger.LogDebug("重置任务 {TaskId} 用户 {UserId} 的进度", task.Id, playerGameUserId);
                }

                // ========== 【计算进度】子步骤 ==========
                // 4. 根据任务类型计算新的进度值
                var newProgressValue = CalculateTaskProgressByType(task.TaskType, gameData, progress, task.RefreshType);

                // ========== 【更新进度】子步骤 ==========
                // 5. 更新进度（只有进度增加时才更新）
                if (newProgressValue > progress.CurrentProgress)
                {
                    var oldProgress = progress.CurrentProgress;
                    progress.CurrentProgress = newProgressValue;
                    progress.UpdateTime = now;

                    // ========== 【完成检查】子步骤 ==========
                    // 6. 检查任务是否完成
                    if (progress.CurrentProgress >= task.TargetValue && !progress.RewardClaimed)
                    {
                        progress.CompletedTimes++;
                        progress.LastCompletedTime = now;

                        _logger.LogInformation("任务完成：活动 {ActivityId}，任务 {TaskId}，用户 {UserId}，进度 {Progress}/{Target}",
                            activityId, task.Id, playerGameUserId, progress.CurrentProgress, task.TargetValue);
                    }

                    // ========== 【保存数据】子步骤 ==========
                    // 7. 保存进度更新
                    await _playerTaskProgressDAL.BatchUpdateTaskProgressAsync([progress]);

                    _logger.LogDebug("更新任务进度：活动 {ActivityId}，任务 {TaskId}，用户 {UserId}，进度 {OldProgress} -> {NewProgress}",
                        activityId, task.Id, playerGameUserId, oldProgress, newProgressValue);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户 {UserId} 任务 {TaskId} 进度失败", playerGameUserId, task.Id);
                // 不抛出异常，避免影响其他用户的进度更新
            }
        }

        /// <summary>
        /// 计算任务有效期（根据任务刷新类型和活动时间）
        /// </summary>
        /// <param name="task">任务配置</param>
        /// <param name="activityStartTime">活动开始时间</param>
        /// <param name="activityEndTime">活动结束时间</param>
        /// <param name="currentTime">当前时间</param>
        /// <returns>任务有效期的开始和结束时间</returns>
        private static (DateTime validFrom, DateTime validTo) CalculateTaskValidPeriod(
            ActivityTask task,
            DateTime activityStartTime,
            DateTime activityEndTime,
            DateTime currentTime)
        {
            switch (task.RefreshType)
            {
                case Entity.RefreshType.daily:
                    // 每日任务：当天0点到23:59:59
                    return (currentTime.Date, currentTime.Date.AddDays(1).AddSeconds(-1));

                case Entity.RefreshType.weekly:
                    // 每周任务：本周一0点到周日23:59:59
                    var weekStart = currentTime.Date.AddDays(-(int)currentTime.DayOfWeek + 1);
                    var weekEnd = weekStart.AddDays(7).AddSeconds(-1);
                    return (weekStart, weekEnd);

                case Entity.RefreshType.never:
                    // 不刷新任务：整个活动期间有效
                    return (activityStartTime, activityEndTime);

                default:
                    // 默认使用活动期间
                    return (activityStartTime, activityEndTime);
            }
        }

        /// <summary>
        /// 判断是否需要重置任务进度
        /// 根据任务刷新类型和上次刷新时间判断
        /// </summary>
        /// <param name="progress">任务进度记录</param>
        /// <param name="task">任务配置</param>
        /// <param name="currentTime">当前时间</param>
        /// <returns>是否需要重置</returns>
        private static bool ShouldResetTaskProgress(
            ActivityPlayerTaskProgress progress,
            ActivityTask task,
            DateTime currentTime)
        {
            // 如果没有上次刷新时间，不需要重置
            if (progress.LastRefreshTime == null)
                return false;

            switch (task.RefreshType)
            {
                case Entity.RefreshType.daily:
                    // 每日任务：跨天需要重置
                    return progress.LastRefreshTime.Value.Date < currentTime.Date;

                case Entity.RefreshType.weekly:
                    // 每周任务：跨周需要重置
                    var lastWeekStart = progress.LastRefreshTime.Value.Date.AddDays(-(int)progress.LastRefreshTime.Value.DayOfWeek + 1);
                    var currentWeekStart = currentTime.Date.AddDays(-(int)currentTime.DayOfWeek + 1);
                    return lastWeekStart < currentWeekStart;

                case Entity.RefreshType.never:
                    // 不刷新任务：永远不重置
                    return false;

                default:
                    return false;
            }
        }

        /// <summary>
        /// 重置任务进度
        /// 清空进度但保留历史完成次数
        /// </summary>
        /// <param name="progress">任务进度记录</param>
        /// <param name="currentTime">当前时间</param>
        private static void ResetTaskProgress(ActivityPlayerTaskProgress progress, DateTime currentTime)
        {
            progress.CurrentProgress = 0;
            progress.LastRefreshTime = currentTime;
            progress.RewardClaimed = false;
            progress.RewardClaimedTime = null;
            progress.RewardChancesEarned = 0;
            // 注意：不重置CompletedTimes，保留历史完成记录
        }

        /// <summary>
        /// 【计算进度】根据任务类型计算进度值
        /// 支持不同的任务类型和刷新机制
        /// </summary>
        /// <param name="taskType">任务类型</param>
        /// <param name="gameData">游戏数据</param>
        /// <param name="progress">当前进度记录</param>
        /// <param name="refreshType">刷新类型</param>
        /// <returns>新的进度值</returns>
        private int CalculateTaskProgressByType(
            Entity.TaskType taskType,
            TodayGameDataResult gameData,
            ActivityPlayerTaskProgress progress,
            Entity.RefreshType refreshType)
        {
            switch (taskType)
            {
                case Entity.TaskType.login:
                    // ========== 【登录任务】计算逻辑 ==========
                    // 登录任务：在线时长大于0表示已登录
                    // 对于每日登录任务，每天最多计1次
                    if (gameData.OnlineDuration > 0)
                    {
                        return refreshType == Entity.RefreshType.daily ? 1 : progress.CurrentProgress + 1;
                    }
                    return progress.CurrentProgress;

                case Entity.TaskType.game_rounds:
                    // ========== 【游戏局数任务】计算逻辑 ==========
                    // 游戏局数任务：累计游戏局数
                    if (refreshType == Entity.RefreshType.daily)
                    {
                        // 每日任务：使用当天的游戏局数
                        return gameData.GameCount;
                    }
                    else
                    {
                        // 累计任务：在原有基础上增加今日局数
                        return progress.CurrentProgress + gameData.GameCount;
                    }

                case Entity.TaskType.service_fee:
                    // ========== 【服务费任务】计算逻辑 ==========
                    // 服务费任务：累计服务费金额
                    if (refreshType == Entity.RefreshType.daily)
                    {
                        // 每日任务：使用当天的服务费
                        return (int)gameData.ServiceFee;
                    }
                    else
                    {
                        // 累计任务：在原有基础上增加今日服务费
                        return progress.CurrentProgress + (int)gameData.ServiceFee;
                    }

                default:
                    // ========== 【未知任务类型】处理 ==========
                    // 未知任务类型，保持原进度
                    _logger.LogWarning("未知的任务类型：{TaskType}", taskType);
                    return progress.CurrentProgress;
            }
        }
    }

    /// <summary>
    /// 游戏数据同步结果
    /// </summary>
    public class GameDataSyncResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; } = false;

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 处理的创建者数量
        /// </summary>
        public int ProcessedCreators { get; set; } = 0;

        /// <summary>
        /// 失败的创建者数量
        /// </summary>
        public int FailedCreators { get; set; } = 0;

        /// <summary>
        /// 同步的记录数
        /// </summary>
        public int SyncedRecords { get; set; } = 0;
    }
}
