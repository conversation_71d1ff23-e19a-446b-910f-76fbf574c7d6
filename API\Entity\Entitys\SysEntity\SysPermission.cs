using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.SysEntity
{
    /// <summary>
    /// 权限实体
    /// </summary>
    [Table("sys_permission")]
    public class SysPermission : BaseEntity_GUID
    {
        /// <summary>
        /// 主体ID（用户ID或角色ID）
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        [Comment("主体ID（用户ID或角色ID）")]
        public string SubjectId { get; set; } = string.Empty;

        /// <summary>
        /// 对象ID（菜单ID或按钮ID）
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        [Comment("对象ID（菜单ID或按钮ID）")]
        public string ObjectId { get; set; } = string.Empty;
    }


}