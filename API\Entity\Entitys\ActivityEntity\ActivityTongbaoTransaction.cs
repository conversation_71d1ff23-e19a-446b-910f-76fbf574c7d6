using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 通宝流水记录表（仅记录活动相关流水）
/// </summary>
[Table("activity_tongbao_transactions")]
public class ActivityTongbaoTransaction : BaseEntity_ID
{
    /// <summary>
    /// 游戏用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("game_user_id")]
    [Comment("游戏用户ID")]
    public string GameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("user_nickname")]
    [Comment("用户昵称")]
    public string UserNickname { get; set; } = string.Empty;

    /// <summary>
    /// 关联活动ID
    /// </summary>
    [Column("activity_id")]
    [Comment("关联活动ID")]
    public int? ActivityId { get; set; }

    /// <summary>
    /// 交易类型
    /// </summary>
    [Required]
    [Column("transaction_type")]
    [Comment("交易类型")]
    public TransactionType TransactionType { get; set; }

    /// <summary>
    /// 金额（正数为收入，负数为支出）
    /// </summary>
    [Required]
    [Column("amount", TypeName = "decimal(15,2)")]
    [Comment("金额（正数为收入，负数为支出）")]
    public decimal Amount { get; set; }

    /// <summary>
    /// 交易前余额
    /// </summary>
    [Required]
    [Column("balance_before", TypeName = "decimal(15,2)")]
    [Comment("交易前余额")]
    public decimal BalanceBefore { get; set; }

    /// <summary>
    /// 交易后余额
    /// </summary>
    [Required]
    [Column("balance_after", TypeName = "decimal(15,2)")]
    [Comment("交易后余额")]
    public decimal BalanceAfter { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [MaxLength(500)]
    [Column("description")]
    [Comment("描述")]
    public string? Description { get; set; }

}
