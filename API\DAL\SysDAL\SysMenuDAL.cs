using Common.Autofac;
using DAL.Databases;
using Entity.Dto;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.SysDAL
{
    /// <summary>
    /// 菜单数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SysMenuDAL(MyContext context, SysUserDAL userDLL) : BaseQueryDLL<SysMenu, SysMenuDAL.Queryable>(context)
    {
        private readonly SysUserDAL _userDLL = userDLL;

        private readonly MyContext _context = context;

        /// <summary>
        /// 菜单查询条件模型
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 菜单ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? Id { get; set; }

            /// <summary>
            /// 菜单类型
            /// </summary>
            [Query(QueryOperator.包含)]
            public string[]? Ids { get; set; }

            /// <summary>
            /// 父级ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? ParentId { get; set; }

            /// <summary>
            /// 菜单名称
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Name { get; set; }

            /// <summary>
            /// 菜单类型
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Type { get; set; }

            /// <summary>
            /// 状态
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 排序
            /// </summary>
            [Query(QueryOperator.排序, columnName: "OrderNum", orderDirection: OrderDirection.升序)]
            public int? OrderByOrderNum { get; set; }
        }





        /// <summary>
        /// 获取分页数据列表
        /// </summary>
        /// <param name="queryable">查询条件模型</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<SysMenu>> GetPageDataAsync(
            Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderBy(x => x.OrderNum));
        }


        /// <summary>
        /// 分页获取用户列表
        /// </summary>
        /// <param name="queryable">查询条件，包含分页参数和筛选条件</param>
        /// <returns>分页结果，包含用户列表和总记录数</returns>
        public async Task<PageEntity<MenuDto>> GetPageAsync(Queryable queryable)
        {
            var result = await GetPageDataAsync(queryable);

            // 转换为DTO
            return new PageEntity<MenuDto>
            {
                List = [.. (result.List ?? Enumerable.Empty<SysMenu>()).Select(x => new MenuDto
                {
                    Id = x.Id,
                    Name = x.Name,
                    ParentId = x.ParentId,
                    Type = x.Type,
                    Status = x.Status,
                    OrderNum = x.OrderNum,
                    CreateTime = x.CreateTime,
                    UpdateTime = x.UpdateTime,
                })],
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }






        /// <summary>
        /// 获取用户所有菜单列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户菜单列表</returns>
        public async Task<List<SysMenu>> GetUserALLMenusAsync(string userId)
        {
            // 获取用户角色ID列表
            var permissionsSubjects = await _userDLL.GetUserRoleNamesAsync(userId);
            permissionsSubjects.Add(userId);
            var roleMenuIds = await _context.SysPermissions
                    .Where(p => permissionsSubjects.Contains(p.SubjectId))
                    .Select(rm => rm.ObjectId)
                    .ToListAsync();


            // 获取用户菜单ID列表
            return await GetListAsync(new Queryable(), q => q.Where(m => roleMenuIds.Contains(m.Id)).OrderByDescending(m => m.OrderNum));

        }

        /// <summary>
        /// 检查是否有子菜单
        /// </summary>
        public Task<bool> HasChildrenAsync(string id)
        => _context.SysMenus.AnyAsync(m => m.ParentId == id);



        /// <summary>
        /// 根据ID列表获取菜单
        /// </summary>
        /// <param name="menuIds">菜单ID列表</param>
        /// <returns>菜单列表</returns>
        public Task<List<SysMenu>> GetMenusByIdsAsync(List<string> menuIds)
        => _context.SysMenus
                .Where(m => menuIds.Contains(m.Id))
                .OrderBy(m => m.OrderNum)
                .ToListAsync();

    }
}