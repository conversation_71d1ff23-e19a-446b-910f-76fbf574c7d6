using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text;

namespace Common.Export
{
    /// <summary>
    /// 导出服务实现
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="logger">日志记录器</param>
    public class ExportService(ILogger<ExportService> logger) : IExportService
    {
        private readonly ILogger<ExportService> _logger = logger;

        /// <summary>
        /// 导出CSV文件
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要导出的数据</param>
        /// <param name="columns">要导出的列配置</param>
        /// <returns>文件字节数组</returns>
        public byte[] ExportToCsv<T>(IEnumerable<T> data, List<ExportColumn> columns)
        {
            try
            {
                _logger.LogInformation("开始导出CSV文件");

                // 按排序顺序排列列
                columns = [.. columns.OrderBy(c => c.Order)];

                using var memoryStream = new MemoryStream();
                using var writer = new StreamWriter(memoryStream, Encoding.UTF8);

                // 写入表头
                var headerLine = string.Join(",", columns.Select(c => EscapeCsvField(c.Title)));
                writer.WriteLine(headerLine);

                // 写入数据行
                foreach (var item in data)
                {
                    var values = new List<string>();

                    foreach (var column in columns)
                    {
                        var value = GetPropertyValue(item, column);
                        values.Add(EscapeCsvField(value));
                    }

                    writer.WriteLine(string.Join(",", values));
                }

                writer.Flush();
                _logger.LogInformation("CSV文件导出完成");

                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出CSV文件失败");
                throw new ApplicationException("导出CSV文件失败", ex);
            }
        }

        /// <summary>
        /// 通用导出方法
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要导出的数据</param>
        /// <param name="exportRequest">导出配置</param>
        /// <param name="defaultFileName">默认文件名</param>
        /// <returns>文件结果</returns>
        public FileContentResult ExportToFile<T>(IEnumerable<T> data, ExportRequest exportRequest, string defaultFileName)
        {
            try
            {
                _logger.LogInformation("开始通用导出文件");

                // 如果没有提供导出列配置，则返回错误
                if (exportRequest.Columns == null || exportRequest.Columns.Count == 0)
                {
                    throw new ArgumentException("导出列配置不能为空");
                }

                // 创建ExportColumn列表
                var columns = exportRequest.Columns.Select(c => new ExportColumn
                {
                    Title = c.Title ?? string.Empty,
                    PropertyName = c.PropertyName ?? string.Empty,
                    Order = c.Order,
                    Format = c.Format ?? string.Empty
                }).ToList();

                // 生成CSV数据
                byte[] csvData = ExportToCsv(data, columns);

                // 简化文件名逻辑
                var fileName = !string.IsNullOrEmpty(exportRequest.FileName)
                    ? $"{exportRequest.FileName}.csv"
                    : $"{defaultFileName}_{DateTime.Now:yyyyMMddHHmmss}.csv";

                _logger.LogInformation("通用导出文件完成");

                // 返回文件内容结果
                return new FileContentResult(
                    csvData,
                    "text/csv; charset=gb2312")
                {
                    FileDownloadName = fileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "通用导出文件失败");
                throw new ApplicationException("通用导出文件失败", ex);
            }
        }

        /// <summary>
        /// 获取属性值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="item">数据项</param>
        /// <param name="column">列配置</param>
        /// <returns>格式化后的属性值</returns>
        private string GetPropertyValue<T>(T item, ExportColumn column)
        {
            try
            {
                // 支持嵌套属性，如 "User.Name"
                var propertyNames = column.PropertyName.Split('.');
                object? value = item;

                foreach (var propName in propertyNames)
                {
                    if (value == null)
                        return string.Empty;

                    PropertyInfo? property = value.GetType().GetProperty(propName);
                    if (property == null)
                        return string.Empty;

                    value = property.GetValue(value);
                }

                if (value == null)
                    return string.Empty;

                // 应用格式化
                if (!string.IsNullOrEmpty(column.Format))
                {
                    if (value is IFormattable formattable)
                    {
                        return formattable.ToString(column.Format, null);
                    }
                }

                return value.ToString() ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取属性值失败: {PropertyName}", column.PropertyName);
                return string.Empty;
            }
        }

        /// <summary>
        /// 转义CSV字段
        /// </summary>
        /// <param name="field">字段值</param>
        /// <returns>转义后的字段值</returns>
        private static string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "\"\"";

            // 如果字段包含逗号、引号或换行符，需要用引号包围并转义内部引号
            bool needsQuotes = field.Contains(',') || field.Contains('"') || field.Contains('\n') || field.Contains('\r');

            if (needsQuotes)
            {
                // 将字段中的引号替换为两个引号
                field = field.Replace("\"", "\"\"");
                return $"\"{field}\"";
            }

            return field;
        }
    }
}