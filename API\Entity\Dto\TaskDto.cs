using System.ComponentModel.DataAnnotations;

namespace Entity.Dto;

/// <summary>
/// 更新任务进度请求DTO
/// </summary>
public class UpdateTaskProgressDto
{
    /// <summary>
    /// 游戏用户ID
    /// </summary>
    [Required(ErrorMessage = "游戏用户ID不能为空")]
    [MaxLength(50, ErrorMessage = "游戏用户ID长度不能超过50个字符")]
    public string GameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 进度增量
    /// </summary>
    [Required(ErrorMessage = "进度增量不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "进度增量必须大于0")]
    public int ProgressIncrement { get; set; }

    /// <summary>
    /// 行为类型
    /// </summary>
    [Required(ErrorMessage = "行为类型不能为空")]
    public string ActionType { get; set; } = string.Empty;

    /// <summary>
    /// 行为值
    /// </summary>
    [Required(ErrorMessage = "行为值不能为空")]
    public int ActionValue { get; set; }
}

/// <summary>
/// 任务进度响应DTO
/// </summary>
public class TaskProgressResponseDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int TaskId { get; set; }

    /// <summary>
    /// 当前进度
    /// </summary>
    public int CurrentProgress { get; set; }

    /// <summary>
    /// 是否完成
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// 获得的奖励次数
    /// </summary>
    public int RewardChancesGained { get; set; }
}

/// <summary>
/// 玩家任务列表响应DTO
/// </summary>
public class PlayerTaskResponseDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public TaskType TaskType { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 目标数值
    /// </summary>
    public int TargetValue { get; set; }

    /// <summary>
    /// 奖励次数
    /// </summary>
    public int RewardChances { get; set; }

    /// <summary>
    /// 刷新类型
    /// </summary>
    public RefreshType RefreshType { get; set; }

    /// <summary>
    /// 当前进度
    /// </summary>
    public int CurrentProgress { get; set; }

    /// <summary>
    /// 完成次数
    /// </summary>
    public int CompletedTimes { get; set; }

    /// <summary>
    /// 是否完成
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// 下次刷新时间
    /// </summary>
    public DateTime? NextRefreshTime { get; set; }
}
