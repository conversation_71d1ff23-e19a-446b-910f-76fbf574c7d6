using BLL.SysService;
using Entity.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 认证控制器 - 处理用户认证相关的请求
    /// </summary>
    /// <remarks>
    /// 构造函数 - 依赖注入用户服务和短信登录服务
    /// </remarks>
    /// <param name="userService">用户服务实例</param>
    /// <param name="smsLoginService">短信登录服务实例</param>
    /// <param name="logService">日志服务实例</param>
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController(SysUserService userService, SmsLoginService smsLoginService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 用户服务接口
        /// </summary>
        private readonly SysUserService _userService = userService;

        /// <summary>
        /// 短信登录服务接口
        /// </summary>
        private readonly SmsLoginService _smsLoginService = smsLoginService;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 用户登录接口
        /// </summary>
        /// <param name="loginRequest">登录请求DTO，包含用户名和密码</param>
        /// <returns>
        /// 返回登录响应结果:
        /// - 成功: 返回200状态码和用户Token信息
        /// - 失败: 返回500状态码和错误信息
        /// </returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<Result<LoginResponseDto>> Login([FromBody] LoginRequestDto loginRequest)
        {
            // 执行登录操作
            var loginResult = await _userService.LoginAsync(loginRequest);

            // 记录登录成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "认证管理",
                Operation = "用户登录",
                BusinessObject = "SysUser",
                ObjectId = loginResult.UserInfo.UserId,
                DetailedInfo = $"用户 {loginResult.UserInfo.Username} 登录成功",
                UserId = loginResult.UserInfo.UserId,
                Username = loginResult.UserInfo.Username,
                Ip = IP
            });

            // 登录成功，返回成功响应
            return Success(loginResult, "登录成功");

        }

        /// <summary>
        /// 用户登出接口
        /// </summary>
        /// <returns>登出结果</returns>
        [HttpPost("logout")]
        [Permission]
        public async Task<Result> Logout()
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行登出操作
            SysUserService.Logout(currentUser.UserId);
            ClearCurrentUserCache(); // 清除用户缓存

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "认证管理",
                Operation = "用户登出",
                BusinessObject = "SysUser",
                ObjectId = currentUser.UserId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功登出",
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("登出成功");
        }

        /// <summary>
        /// 发送短信验证码
        /// </summary>
        /// <param name="phoneNumber">手机号码</param>
        /// <returns>发送结果</returns>
        [HttpPost("sendSmsCode")]
        public async Task<Result> SendSmsCode([FromQuery] string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return Fail("手机号码不能为空");

            var result = await _smsLoginService.SendLoginVerificationCodeAsync(phoneNumber);
            return result.Success
                ? Success(result.Message)
                : Fail(result.Message);
        }

        /// <summary>
        /// 使用短信验证码登录
        /// </summary>
        /// <param name="dto">登录信息，包含手机号和验证码</param>
        /// <returns>登录结果</returns>
        [HttpPost("smsLogin")]
        public async Task<Result<LoginResponseDto>> SmsLogin([FromBody] SmsLoginDto dto)
        {
            if (!ModelState.IsValid)
                return Fail<LoginResponseDto>(GetModelStateErrors());

            var result = await _smsLoginService.LoginWithSmsAsync(dto);
            if (!result.Success)
                return Fail<LoginResponseDto>(result.Message);

            // 确保返回的数据不为null
            if (result.Data == null)
                return Fail<LoginResponseDto>("登录成功但未获取到用户信息");

            return Success(result.Data, result.Message);
        }

        /// <summary>
        /// 获取当前登录用户信息接口
        /// </summary>
        /// <returns>
        /// 返回用户信息结果:
        /// - 成功: 返回200状态码和用户详细信息
        /// - 未授权: 返回401状态码
        /// - 失败: 返回500状态码和错误信息
        /// </returns>
        [HttpGet("userinfo")]
        [Permission]
        // JWT认证特性，确保接口受保护
        public async Task<Result<UserInfoDto>> GetUserInfo()
        => Success(await GetCurrentUserInfoAsync(_userService), "获取成功");

        /// <summary>
        /// 获取ModelState中的错误信息
        /// </summary>
        private string GetModelStateErrors()
        => string.Join("; ", ModelState.Values
            .SelectMany(v => v.Errors)
            .Select(e => e.ErrorMessage)
            .ToList());
    }
}