[{"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.ActivityController", "Method": "CreateAsync", "RelativePath": "api/activities", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Entity.Dto.CreateActivityDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.ActivityResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.ActivityController", "Method": "GetActivitiesAsync", "RelativePath": "api/activities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "scope", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.ActivityController", "Method": "GetActivityDetailAsync", "RelativePath": "api/activities/{activityId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.ActivityDetailResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.DrawController", "Method": "AddDrawChancesAsync", "RelativePath": "api/activities/{activityId}/add-chances", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityId", "Type": "System.Int32", "IsRequired": true}, {"Name": "gameUserId", "Type": "System.String", "IsRequired": false}, {"Name": "playerNickname", "Type": "System.String", "IsRequired": false}, {"Name": "chances", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.DrawChancesResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.ActivityController", "Method": "CloseActivityAsync", "RelativePath": "api/activities/{activityId}/close", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.DrawController", "Method": "DrawAsync", "RelativePath": "api/activities/{activityId}/draw", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.DrawResultResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.DrawController", "Method": "GetDrawChancesAsync", "RelativePath": "api/activities/{activityId}/draw-chances", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.DrawChancesResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.DrawController", "Method": "GetDrawRecords", "RelativePath": "api/activities/{activityId}/draw-records", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "activityId", "Type": "System.Int32", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.ActivityController", "Method": "GetTodayGameData", "RelativePath": "api/activities/get-today-game-data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ShanxiGameActivity.Controllers.ActivityControllers.GetTodayGameDataRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[DAL.ActivityDAL.TodayGameDataResult, DAL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.ActivityController", "Method": "UpdateUserPoint", "RelativePath": "api/activities/update-user-point", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ShanxiGameActivity.Controllers.ActivityControllers.UpdateUserPointRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[DAL.ActivityDAL.UpdatePointResult, DAL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.ActivityController", "Method": "ValidateDownUser", "RelativePath": "api/activities/validate-down-user", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ShanxiGameActivity.Controllers.ActivityControllers.ValidateDownUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginRequest", "Type": "Entity.Dto.LoginRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.LoginResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.AuthController", "Method": "SendSmsCode", "RelativePath": "api/Auth/sendSmsCode", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "phoneNumber", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.AuthController", "Method": "SmsLogin", "RelativePath": "api/Auth/smsLogin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Entity.Dto.SmsLoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.LoginResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.AuthController", "Method": "GetUserInfo", "RelativePath": "api/Auth/userinfo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.UserInfoDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.BasisController.BasisController", "Method": "UploadFileAsync", "RelativePath": "api/Basis/UploadFileAsync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.BasisController.BasisController", "Method": "UploadFilesAsync", "RelativePath": "api/Basis/UploadFilesAsync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.ButtonController", "Method": "SyncButtons", "RelativePath": "api/Button/sync", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.DebugController", "Method": "TestProcedure", "RelativePath": "api/debug/test-procedure", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ShanxiGameActivity.Controllers.ActivityControllers.ProcedureTestRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.LogController", "Method": "GetAsync", "RelativePath": "api/Log/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Entitys.SysEntity.SysLog, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.LogController", "Method": "ClearLogsAsync", "RelativePath": "api/Log/clear", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "clearDto", "Type": "Entity.Dto.LogClearDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.LogController", "Method": "ExportLogsAsync", "RelativePath": "api/Log/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "Operation", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "LogType", "Type": "System.String", "IsRequired": false}, {"Name": "LogLevel", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByCreateTime", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "exportRequest", "Type": "BLL.SysService.Exports.ExportRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.LogController", "Method": "QueryAsync", "RelativePath": "api/Log/query", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "Username", "Type": "System.String", "IsRequired": false}, {"Name": "Operation", "Type": "System.String", "IsRequired": false}, {"Name": "Method", "Type": "System.String", "IsRequired": false}, {"Name": "LogType", "Type": "System.String", "IsRequired": false}, {"Name": "LogLevel", "Type": "System.String", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByCreateTime", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[DAL.Databases.EFHelper+PageEntity`1[[Entity.Entitys.SysEntity.SysLog, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], DAL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.MenuController", "Method": "GetAsync", "RelativePath": "api/Menu/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.MenuDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.MenuController", "Method": "CreateAsync", "RelativePath": "api/Menu/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.CreateMenuDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.MenuController", "Method": "DeleteAsync", "RelativePath": "api/Menu/delete/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.MenuController", "Method": "GetSelfUserMenuTreeAsync", "RelativePath": "api/Menu/GetSelfUserMenuTreeAsync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.MenuDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.MenuController", "Method": "GetTreeAsync", "RelativePath": "api/Menu/tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "Ids", "Type": "System.String[]", "IsRequired": false}, {"Name": "ParentId", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Type", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByOrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.MenuDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.MenuController", "Method": "UpdateAsync", "RelativePath": "api/Menu/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.UpdateMenuDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.PermissionController", "Method": "AssignPermissionsAsync", "RelativePath": "api/Permission/assign", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "assignPermissions", "Type": "Entity.Dto.AssignPermissions_ReqDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.PermissionController", "Method": "ClearPermissionCache", "RelativePath": "api/Permission/clear/cache", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.PermissionController", "Method": "GetRolePermissionsAsync", "RelativePath": "api/Permission/role/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.MenuDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.PermissionController", "Method": "GetSelfAllPermissionsAsync", "RelativePath": "api/Permission/self/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.MenuDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.PermissionController", "Method": "GetUserPermissionsAsync", "RelativePath": "api/Permission/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.MenuDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.PermissionController", "Method": "GetUserAllPermissionsAsync", "RelativePath": "api/Permission/user/{userId}/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.MenuDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.RoleController", "Method": "GetAsync", "RelativePath": "api/Role/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.RoleDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.RoleController", "Method": "CreateAsync", "RelativePath": "api/Role/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.CreateRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.RoleController", "Method": "DeleteAsync", "RelativePath": "api/Role/delete/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.RoleController", "Method": "GetPageAsync", "RelativePath": "api/Role/query", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Code", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByOrderNum", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[DAL.Databases.EFHelper+PageEntity`1[[Entity.Dto.RoleDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], DAL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.RoleController", "Method": "UpdateAsync", "RelativePath": "api/Role/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.UpdateRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "BatchCreateAsync", "RelativePath": "api/SysDictionary/batch-create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDtos", "Type": "System.Collections.Generic.List`1[[Entity.Dto.SysDictionaryCreateDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "GetByTypeCodeAsync", "RelativePath": "api/SysDictionary/by-type/{dictTypeCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dictTypeCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.SysDictionaryDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "CreateAsync", "RelativePath": "api/SysDictionary/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.SysDictionaryCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "CreateParentDictionaryAsync", "RelativePath": "api/SysDictionary/create-parent", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "Entity.Dto.SysDictionaryCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "DeleteByTypeCodeAsync", "RelativePath": "api/SysDictionary/delete-by-type/{dictTypeCode}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dictTypeCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "DeleteParentDictionaryAsync", "RelativePath": "api/SysDictionary/delete-parent/{parentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "DeleteAsync", "RelativePath": "api/SysDictionary/delete/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "GetDropdownAsync", "RelativePath": "api/SysDictionary/dropdown/{dictTypeCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dictTypeCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.DictionaryDropdownDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "GetAllAsync", "RelativePath": "api/SysDictionary/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.SysDictionaryDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "QueryAsync", "RelativePath": "api/SysDictionary/query", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "ParentId", "Type": "System.String", "IsRequired": false}, {"Name": "DictTypeCode", "Type": "System.String", "IsRequired": false}, {"Name": "DictTypeName", "Type": "System.String", "IsRequired": false}, {"Name": "DictItemCode", "Type": "System.String", "IsRequired": false}, {"Name": "DictItemName", "Type": "System.String", "IsRequired": false}, {"Name": "IsEnabled", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderBySortOrder", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[DAL.Databases.EFHelper+PageEntity`1[[Entity.Dto.SysDictionaryDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], DAL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "GetAllTypesAsync", "RelativePath": "api/SysDictionary/types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Dto.DictionaryTypeDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.SysDictionaryController", "Method": "UpdateAsync", "RelativePath": "api/SysDictionary/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "Entity.Dto.SysDictionaryUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Boolean, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "GetAsync", "RelativePath": "api/User/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.UserDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "AssignRolesAsync", "RelativePath": "api/User/{userId}/assign-roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "roleIds", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "GetUserRolesAsync", "RelativePath": "api/User/{userId}/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.List`1[[Entity.Entitys.SysEntity.SysRole, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "ChangePasswordAsync", "RelativePath": "api/User/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "CreateAsync", "RelativePath": "api/User/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "DeleteAsync", "RelativePath": "api/User/delete/{id}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "ExportUsersAsync", "RelativePath": "api/User/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "RealName", "Type": "System.String", "IsRequired": false}, {"Name": "Mobile", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByCreateTime", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "exportRequest", "Type": "BLL.SysService.Exports.ExportRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "QueryAsync", "RelativePath": "api/User/query", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "RealName", "Type": "System.String", "IsRequired": false}, {"Name": "Mobile", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.Byte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndTime", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "OrderByCreateTime", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[DAL.Databases.EFHelper+PageEntity`1[[Entity.Dto.UserDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], DAL, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "ResetPasswordAsync", "RelativePath": "api/User/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.ResetPasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.SysControllers.UserController", "Method": "UpdateAsync", "RelativePath": "api/User/update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Entity.Dto.UpdateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.UserCacheController", "Method": "GetNicknameAsync", "RelativePath": "api/users/{gameUserId}/nickname", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "gameUserId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.UserCacheController", "Method": "CacheNicknameAsync", "RelativePath": "api/users/cache-nickname", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Entity.Dto.CacheUserNicknameDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[Entity.Dto.UserCacheResponseDto, Entity, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ShanxiGameActivity.Controllers.ActivityControllers.UserCacheController", "Method": "GetNicknamesAsync", "RelativePath": "api/users/nicknames", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "gameUserIds", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "ShanxiGameActivity.Controllers.BasisController.ResuItEntity.Result`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]