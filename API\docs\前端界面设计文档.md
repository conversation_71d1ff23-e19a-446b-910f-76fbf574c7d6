# 陕西平台活动开放工具 - 前端界面设计文档

## 1. 概述

本文档描述了陕西平台活动开放工具的前端界面设计，包括页面布局、组件设计、交互流程和用户体验规范。

**系统集成说明**：
- 本系统作为游戏系统的内嵌模块，无需独立的登录界面
- 用户身份和权限信息从游戏系统获取
- 界面风格需要与游戏系统保持一致
- 支持在游戏客户端内以iframe或webview方式嵌入

## 2. 设计原则

### 2.1 用户体验原则
- **简洁明了**: 界面简洁，操作直观
- **响应迅速**: 快速响应用户操作
- **一致性**: 保持与游戏系统一致的设计风格和交互模式
- **无缝集成**: 作为游戏系统的一部分，保持无缝的用户体验
- **可访问性**: 支持不同设备和屏幕尺寸

### 2.2 视觉设计原则
- **主色调**: #1890FF (蓝色)
- **辅助色**: #52C41A (绿色), #FAAD14 (橙色), #F5222D (红色)
- **字体**: 微软雅黑, <PERSON><PERSON>, sans-serif
- **圆角**: 4px
- **阴影**: 0 2px 8px rgba(0,0,0,0.15)

## 3. 页面结构设计

### 3.1 整体布局
```
┌─────────────────────────────────────────┐
│                 顶部导航栏                │
├─────────────────────────────────────────┤
│  侧边栏  │           主内容区            │
│         │                              │
│  导航菜单 │         页面内容             │
│         │                              │
│         │                              │
└─────────────────────────────────────────┘
```

### 3.2 顶部导航栏
- **左侧**: 系统Logo + 系统名称
- **右侧**: 用户头像 + 昵称 + 通宝余额 + 返回游戏按钮

### 3.3 侧边栏导航
根据用户角色显示不同菜单：

**盟主/合伙人菜单**:
- 🏠 首页
- 🎯 活动管理
  - 创建活动
  - 活动列表
  - 历史活动
- 📊 数据统计
- 💰 通宝管理
- ⚙️ 系统设置

**玩家菜单**:
- 🏠 首页
- 🎮 我的活动
- 🏆 中奖记录
- 💰 通宝记录

## 4. 核心页面设计

### 4.1 活动创建页面

#### 4.1.1 页面布局
```
┌─────────────────────────────────────────┐
│              创建活动                    │
├─────────────────────────────────────────┤
│  步骤导航: ① 基础配置 → ② 详细配置 → ③ 确认  │
├─────────────────────────────────────────┤
│                                        │
│              表单内容区                  │
│                                        │
├─────────────────────────────────────────┤
│           [上一步]    [下一步]           │
└─────────────────────────────────────────┘
```

#### 4.1.2 步骤一：基础配置
**表单字段**:
- 活动名称 (必填，最多50字符)
- 活动类型 (下拉选择，目前仅"盲盒活动")
- 活动通宝 (数字输入，显示当前余额)
- 活动时间 (日期时间选择器，开始时间+结束时间)

**验证规则**:
- 活动名称不能为空
- 通宝数量不能超过当前余额
- 结束时间必须晚于开始时间

#### 4.1.3 步骤二：详细配置
**奖励配置区域**:
```
┌─────────────────────────────────────────┐
│  奖励配置                    [+ 添加奖励] │
├─────────────────────────────────────────┤
│  奖励1: 一等奖 1000通宝                  │
│  ├ 奖励名称: [一等奖 1000通宝]           │
│  ├ 奖励类型: ○通宝 ○实物                │
│  ├ 通宝数量: [1000]                     │
│  ├ 权重: [1]  概率: 5.26%               │
│  ├ 每日份数: [5]                        │
│  └ [删除]                               │
├─────────────────────────────────────────┤
│  奖励2: 二等奖 500通宝                   │
│  └ ... (类似结构)                       │
└─────────────────────────────────────────┘
```

**任务配置区域**:
```
┌─────────────────────────────────────────┐
│  任务配置                    [+ 添加任务] │
├─────────────────────────────────────────┤
│  任务1: 每日登录                         │
│  ├ 任务类型: [登录任务 ▼]               │
│  ├ 任务名称: [每日登录]                 │
│  ├ 目标数值: [1]                        │
│  ├ 奖励次数: [1]                        │
│  ├ 刷新类型: [每日 ▼]                   │
│  └ [删除]                               │
└─────────────────────────────────────────┘
```

#### 4.1.4 步骤三：确认信息
显示所有配置的汇总信息，包括：
- 活动基本信息
- 奖励列表和概率分布
- 任务列表
- 通宝投入确认

### 4.2 活动参与页面（玩家视角）

#### 4.2.1 页面布局
```
┌─────────────────────────────────────────┐
│              春节盲盒活动                │
├─────────────────────────────────────────┤
│  奖池: 45,000通宝    剩余时间: 5天23小时  │
├─────────────────────────────────────────┤
│                                        │
│              任务列表区                  │
│                                        │
├─────────────────────────────────────────┤
│  可用次数: 3次        [开启盲盒]         │
├─────────────────────────────────────────┤
│              中奖记录区                  │
└─────────────────────────────────────────┘
```

#### 4.2.2 任务列表组件
```
┌─────────────────────────────────────────┐
│  📋 任务列表                             │
├─────────────────────────────────────────┤
│  ✅ 每日登录 (1/1)           +1次        │
│     刷新倒计时: 5小时23分                │
├─────────────────────────────────────────┤
│  🎮 完成10局游戏 (7/10)      +2次        │
│     进度条: ████████░░ 70%              │
│     刷新倒计时: 5小时23分                │
├─────────────────────────────────────────┤
│  💰 贡献500通宝 (320/500)    +3次        │
│     进度条: ██████░░░░ 64%              │
│     刷新倒计时: 不刷新                   │
└─────────────────────────────────────────┘
```

#### 4.2.3 抽奖动画
**盲盒开启动画**:
1. 点击"开启盲盒"按钮
2. 显示3D盒子旋转动画 (2秒)
3. 盒子打开，显示奖励内容
4. 播放庆祝动画和音效
5. 显示奖励详情弹窗

#### 4.2.4 中奖记录组件
```
┌─────────────────────────────────────────┐
│  🏆 中奖记录          [全部▼] [我的▼]    │
├─────────────────────────────────────────┤
│  玩家001  获得  一等奖1000通宝  15:30    │
│  玩家002  获得  二等奖500通宝   15:28    │
│  玩家003  获得  三等奖100通宝   15:25    │
│  ...                                   │
│  [查看更多]                             │
└─────────────────────────────────────────┘
```

### 4.3 活动管理页面（管理员视角）

#### 4.3.1 活动列表
```
┌─────────────────────────────────────────┐
│  活动管理                    [创建活动]   │
├─────────────────────────────────────────┤
│  筛选: [全部状态▼] [活动类型▼] [搜索框]   │
├─────────────────────────────────────────┤
│  ┌─────────────────────────────────────┐ │
│  │ 春节盲盒活动              进行中    │ │
│  │ 投入: 50,000  剩余: 45,000         │ │
│  │ 参与: 156人   时间: 2025-08-01~07  │ │
│  │ [查看] [数据] [关闭]               │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │ 元宵节活动                已结束    │ │
│  │ 投入: 30,000  剩余: 5,000          │ │
│  │ 参与: 89人    时间: 2025-07-20~25  │ │
│  │ [查看] [数据]                      │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

#### 4.3.2 活动数据统计页面
```
┌─────────────────────────────────────────┐
│              春节盲盒活动 - 数据统计      │
├─────────────────────────────────────────┤
│  📊 总体数据                             │
│  参与人数: 156人    总抽奖: 523次        │
│  已发奖励: 25,000通宝  剩余: 25,000通宝  │
├─────────────────────────────────────────┤
│  👥 玩家统计                             │
│  ┌─────────────────────────────────────┐ │
│  │ 玩家昵称  │ ID    │ 抽奖次数 │ 中奖通宝│ │
│  │ 玩家001  │ 1001  │ 15      │ 2,500  │ │
│  │ 玩家002  │ 1002  │ 12      │ 1,800  │ │
│  │ ...                                │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│  🎁 实物奖励统计                         │
│  京东卡*3, 手机*1, 耳机*5               │
└─────────────────────────────────────────┘
```

## 5. 组件设计规范

### 5.1 按钮组件
**主要按钮**: 蓝色背景，白色文字，圆角4px
**次要按钮**: 白色背景，蓝色边框，蓝色文字
**危险按钮**: 红色背景，白色文字

### 5.2 表单组件
**输入框**: 边框1px #d9d9d9，聚焦时边框变蓝色
**下拉选择**: 带箭头图标，选项悬停高亮
**日期选择**: 日历弹窗，支持时间选择

### 5.3 数据展示组件
**进度条**: 蓝色填充，灰色背景，显示百分比
**标签**: 不同状态用不同颜色 (成功-绿色，警告-橙色，错误-红色)
**卡片**: 白色背景，阴影效果，圆角4px

## 6. 响应式设计

### 6.1 断点设置
- **手机**: < 768px
- **平板**: 768px - 1024px  
- **桌面**: > 1024px

### 6.2 适配策略
**手机端**:
- 侧边栏改为抽屉式
- 表格改为卡片列表
- 按钮增大触摸区域

**平板端**:
- 保持桌面布局
- 调整字体和间距
- 优化触摸交互

## 7. 交互设计

### 7.1 加载状态
- **页面加载**: 骨架屏 + 加载动画
- **按钮加载**: 按钮内显示loading图标
- **数据加载**: 表格/列表显示加载中状态

### 7.2 错误处理
- **表单验证**: 实时验证，错误信息显示在字段下方
- **网络错误**: 顶部显示错误提示条
- **操作失败**: 弹窗提示具体错误信息

### 7.3 成功反馈
- **操作成功**: 右上角显示成功提示
- **中奖动画**: 全屏庆祝动画 + 音效
- **任务完成**: 任务项显示完成动画

## 8. 性能优化

### 8.1 图片优化
- 使用WebP格式
- 懒加载非关键图片
- 压缩图片大小

### 8.2 代码优化
- 组件懒加载
- 路由代码分割
- 缓存静态资源

### 8.3 用户体验优化
- 预加载关键资源
- 骨架屏减少白屏时间
- 防抖处理频繁操作

## 9. 跑马灯组件设计

### 9.1 跑马灯样式
```
┌─────────────────────────────────────────┐
│  🎉 恭喜玩家001获得一等奖1000通宝！ →     │
└─────────────────────────────────────────┘
```

**设计规范**:
- 背景色: 渐变金色 (#FFD700 到 #FFA500)
- 文字颜色: 白色，加粗
- 动画: 从右到左滚动，速度2秒/屏宽
- 位置: 固定在活动页面顶部

### 9.2 跑马灯逻辑
- 实时显示最新中奖消息
- 30秒内无新消息时，循环播放最近10条
- 支持暂停/继续播放
- 点击消息可查看详情

## 10. 移动端专项设计

### 10.1 手机端活动页面
```
┌─────────────────┐
│   春节盲盒活动   │
├─────────────────┤
│ 奖池: 45,000通宝 │
│ 剩余: 5天23小时  │
├─────────────────┤
│     任务区域     │
│   (可滑动查看)   │
├─────────────────┤
│  可用次数: 3次   │
│   [开启盲盒]     │
├─────────────────┤
│     中奖记录     │
│   (上拉加载)     │
└─────────────────┘
```

### 10.2 手势交互
- **下拉刷新**: 更新活动数据
- **上拉加载**: 加载更多记录
- **左右滑动**: 切换任务/记录标签
- **长按**: 显示详细信息

## 11. 无障碍设计

### 11.1 键盘导航
- 支持Tab键切换焦点
- 支持Enter键确认操作
- 支持Esc键关闭弹窗

### 11.2 屏幕阅读器支持
- 添加aria-label属性
- 使用语义化HTML标签
- 提供替代文本

## 12. 主题定制

### 12.1 色彩主题
**默认主题**:
- 主色: #1890FF
- 成功色: #52C41A
- 警告色: #FAAD14
- 错误色: #F5222D

**节日主题** (可选):
- 春节: 红金配色
- 中秋: 橙黄配色
- 圣诞: 红绿配色

### 12.2 暗色模式
- 背景色: #141414
- 卡片色: #1F1F1F
- 文字色: #FFFFFF
- 边框色: #303030

---

**文档版本**: v1.0
**最后更新**: 2025-07-31
**维护人员**: 开发团队
