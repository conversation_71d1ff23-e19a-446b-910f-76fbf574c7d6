using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 用户信息缓存表（仅用于显示，不存储权限信息）
/// </summary>
[Table("activity_user_cache")]
public class ActivityUserCache : BaseEntity_ID
{
    /// <summary>
    /// 游戏系统用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("game_user_id")]
    [Comment("游戏系统用户ID")]
    public string GameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("nickname")]
    [Comment("昵称")]
    public string Nickname { get; set; } = string.Empty;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [Column("last_update_time")]
    [Comment("最后更新时间")]
    public DateTime LastUpdateTime { get; set; } = DateTime.Now;
}
