using Microsoft.AspNetCore.Http;

namespace Common.Export
{
    /// <summary>
    /// 导入服务接口
    /// </summary>
    public interface IImportService
    {
        /// <summary>
        /// 从CSV文件导入数据
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="file">上传的CSV文件</param>
        /// <param name="columnMappings">列映射配置</param>
        /// <returns>导入的数据列表</returns>
        Task<List<T>> ImportFromCsvAsync<T>(IFormFile file, Dictionary<string, string> columnMappings) where T : new();

        /// <summary>
        /// 验证CSV文件格式
        /// </summary>
        /// <param name="file">上传的CSV文件</param>
        /// <param name="requiredColumns">必需的列名</param>
        /// <returns>验证结果</returns>
        Task<(bool IsValid, string ErrorMessage)> ValidateCsvFileAsync(IFormFile file, List<string> requiredColumns);
    }
}