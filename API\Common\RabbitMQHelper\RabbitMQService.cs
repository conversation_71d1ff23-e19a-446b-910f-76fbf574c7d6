using Common.RabbitMQHelper.Entity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Collections.Concurrent;
using System.Text;

namespace Common.RabbitMQHelper;

/// <summary>
/// RabbitMQ 服务实现
/// </summary>
public class RabbitMQService(
    IOptions<RabbitMQOptions> options,
    ILogger<RabbitMQService> logger,
    IConnectionPool connectionPool) : IRabbitMQService, IDisposable
{
    private readonly RabbitMQOptions _options = options.Value;
    private readonly ILogger<RabbitMQService> _logger = logger;
    private readonly IConnectionPool _connectionPool = connectionPool;
    private readonly ConcurrentDictionary<string, bool> _declaredQueues = new();

    public async Task PublishAsync<T>(T message, string queue, string exchange = "", string routingKey = "", IDictionary<string, object>? headers = null, int expiration = 0)
    {
        var channel = _connectionPool.GetChannel();
        try
        {
            await Task.Run(() =>
            {
                // 确保队列已声明
                EnsureQueueDeclared(channel, queue);

                var properties = channel.CreateBasicProperties();
                if (expiration > 0)
                {
                    properties.Expiration = expiration.ToString();
                }

                properties.Headers = headers ?? new Dictionary<string, object>
                {
                    { "retry", 1 }
                };

                var messageBody = Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(message));
                channel.BasicPublish(
                    exchange: exchange,
                    routingKey: string.IsNullOrEmpty(routingKey) ? queue : routingKey,
                    basicProperties: properties,
                    body: messageBody
                );

                _logger.LogInformation("Message published to queue: {queue}", queue);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error publishing message to queue: {queue}", queue);
            throw;
        }
        finally
        {
            _connectionPool.ReleaseChannel(channel);
        }
    }

    public async Task ConsumeAsync(string queue, Func<RabbitMessageEntity, Task<bool>> callback, int maxRetry = 0)
    {
        var channel = _connectionPool.GetChannel();
        try
        {
            // 确保队列已声明
            EnsureQueueDeclared(channel, queue);

            // 设置预取数量
            channel.BasicQos(0, 2, false);

            var consumer = new AsyncEventingBasicConsumer(channel);
            consumer.Received += async (model, ea) =>
            {
                var rabbitMessage = new RabbitMessageEntity
                {
                    Content = Encoding.UTF8.GetString(ea.Body.ToArray()),
                    BasicDeliver = ea
                };

                try
                {
                    var success = await callback(rabbitMessage);
                    if (success)
                    {
                        channel.BasicAck(ea.DeliveryTag, false);
                    }
                    else if (ShouldRetry(ea, maxRetry))
                    {
                        channel.BasicNack(ea.DeliveryTag, false, false);

                        // 更新重试次数
                        var retryCount = GetRetryCount(ea) + 1;
                        var properties = ea.BasicProperties;
                        properties.Headers["retry"] = retryCount;

                        // 重新发布消息
                        channel.BasicPublish("", queue, properties, ea.Body);
                    }
                    else
                    {
                        channel.BasicNack(ea.DeliveryTag, false, false);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing message from queue: {queue}", queue);
                    channel.BasicNack(ea.DeliveryTag, false, false);
                }
            };

            channel.BasicConsume(queue, false, consumer);

            _logger.LogInformation("Started consuming from queue: {queue}", queue);

            // 保持消费者运行
            await Task.Delay(-1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting up consumer for queue: {queue}", queue);
            throw;
        }
    }

    public async Task CloseAsync()
    {
        await Task.Run(() => _connectionPool.Dispose());
    }

    private void EnsureQueueDeclared(IModel channel, string queue)
    {
        if (!_declaredQueues.ContainsKey(queue))
        {
            channel.QueueDeclare(queue, true, false, false, null);
            _declaredQueues.TryAdd(queue, true);
        }
    }

    private static bool ShouldRetry(BasicDeliverEventArgs ea, int maxRetry)
    {
        if (maxRetry < 1) return false;
        var retryCount = GetRetryCount(ea);
        return retryCount < maxRetry;
    }

    private static int GetRetryCount(BasicDeliverEventArgs ea)
    {
        if (ea.BasicProperties.Headers?.ContainsKey("retry") == true)
        {
            return Convert.ToInt32(ea.BasicProperties.Headers["retry"]);
        }
        return 0;
    }

    public void Dispose()
    {
        _connectionPool?.Dispose();
        GC.SuppressFinalize(this); // 抑制终结器
    }
}