using BLL.SysService;
using DAL.SysDAL;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 权限管理控制器
    /// 用于管理系统的权限相关功能,包括菜单权限、角色权限等
    /// </summary>
    [Permission]
    public class PermissionController(SysPermissionService permissionService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 权限服务接口
        /// </summary>
        private readonly SysPermissionService _permissionService = permissionService;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 获取当前用户所有权限
        /// </summary>
        /// <returns></returns>
        [HttpGet("self/all")]
        public async Task<Result<List<MenuDto>>> GetSelfAllPermissionsAsync()
        {
            if (IsAdmin())
                return Success(await _permissionService.GetALLMenuAndButtonPermissionsAsync(), "获取成功");
            return Success(await _permissionService.GetUserAllPermissionsAsync(GetCurrentUserId()), "获取成功");
        }

        /// <summary>
        /// 获取用户所有权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>返回用户所有权限数据</returns>
        [HttpGet("user/{userId}/all")]
        public async Task<Result<List<MenuDto>>> GetUserAllPermissionsAsync(string userId)
        {
            if (IsAdmin())
                return Success(await _permissionService.GetALLMenuAndButtonPermissionsAsync(), "获取成功");
            return Success(await _permissionService.GetUserAllPermissionsAsync(userId), "获取成功");
        }

        /// <summary>
        /// 获取用户权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>返回用户权限数据</returns>
        [HttpGet("user/{userId}")]
        public async Task<Result<List<MenuDto>>> GetUserPermissionsAsync(string userId)
        => Success(await _permissionService.GetUserPermissionsAsync(userId), "获取成功");


        /// <summary>
        /// 清空权限缓存
        /// </summary>
        /// <returns></returns>
        [HttpGet("clear/cache")]
        public Result ClearPermissionCache()
        {
            //if (!IsAdmin()) return Fail("无权限");

            // 清空权限缓存
            SysPermissionService.ClearPermissionCache();
            return Success(true, "清空成功");
        }


        /// <summary>
        /// 获取角色权限
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>返回角色权限数据</returns>
        [HttpGet("role/{roleId}")]
        public async Task<Result<List<MenuDto>>> GetRolePermissionsAsync(string roleId)
        => Success(await _permissionService.GetRolePermissionsAsync(roleId), "获取成功");


        /// <summary>
        /// 分配权限
        /// </summary>
        /// <param name="assignPermissions">权限分配参数</param>
        /// <returns>返回分配结果</returns>
        [FunctionPermission("permission:assign", "分配权限")]
        [HttpPost("assign")]
        public async Task<Result> AssignPermissionsAsync([FromBody] AssignPermissions_ReqDto assignPermissions)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            var beforePermissions = await _permissionService.GetListAsync(new SysPermissionDAL.Queryable { SubjectId = assignPermissions.SubjectId });
            // 执行业务操作
            var result = await _permissionService.AssignPermissionsAsync([assignPermissions], currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "权限管理",
                Operation = "分配权限",
                BusinessObject = "SysPermission",
                ObjectId = assignPermissions.SubjectId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功为ID: {assignPermissions.SubjectId} 分配了权限，共 {assignPermissions.PermissionCodes.Count} 个权限，",
                BeforeData = beforePermissions.Select(t => t.ObjectId).ToList(),
                AfterData = assignPermissions.PermissionCodes,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(result, "分配成功");
        }
    }
}