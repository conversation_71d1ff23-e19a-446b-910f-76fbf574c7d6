using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.ActivityEntity;

/// <summary>
/// 中奖记录表
/// </summary>
[Table("activity_prize_records")]
public class ActivityPrizeRecord : BaseEntity_ID
{
    /// <summary>
    /// 活动ID
    /// </summary>
    [Required]
    [Column("activity_id")]
    [Comment("活动ID")]
    public int ActivityId { get; set; }

    /// <summary>
    /// 玩家游戏用户ID
    /// </summary>
    [Required]
    [MaxLength(50)]
    [Column("player_game_user_id")]
    [Comment("玩家游戏用户ID")]
    public string PlayerGameUserId { get; set; } = string.Empty;

    /// <summary>
    /// 玩家昵称
    /// </summary>
    [Required]
    [MaxLength(100)]
    [Column("player_nickname")]
    [Comment("玩家昵称")]
    public string PlayerNickname { get; set; } = string.Empty;

    /// <summary>
    /// 奖励ID
    /// </summary>
    [Required]
    [Column("reward_id")]
    [Comment("奖励ID")]
    public int RewardId { get; set; }

    /// <summary>
    /// 奖励名称
    /// </summary>
    [Required]
    [MaxLength(200)]
    [Column("reward_name")]
    [Comment("奖励名称")]
    public string RewardName { get; set; } = string.Empty;

    /// <summary>
    /// 奖励类型
    /// </summary>
    [Required]
    [Column("reward_type")]
    [Comment("奖励类型")]
    public RewardType RewardType { get; set; }

    /// <summary>
    /// 通宝数量
    /// </summary>
    [Column("tongbao_amount", TypeName = "decimal(15,2)")]
    [Comment("通宝数量")]
    public decimal TongbaoAmount { get; set; } = 0;

    /// <summary>
    /// 实物描述
    /// </summary>
    [MaxLength(500)]
    [Column("physical_item")]
    [Comment("实物描述")]
    public string? PhysicalItem { get; set; }

    /// <summary>
    /// 发放状态
    /// </summary>
    [Required]
    [Column("status")]
    [Comment("发放状态")]
    public PrizeStatus Status { get; set; } = PrizeStatus.pending;

    /// <summary>
    /// 处理备注
    /// </summary>
    [MaxLength(500)]
    [Column("process_note")]
    [Comment("处理备注")]
    public string? ProcessNote { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    [Column("process_time")]
    [Comment("处理时间")]
    public DateTime? ProcessTime { get; set; }

}
