using BLL.ActivityService;
using DAL.ActivityDAL;
using Entity;
using Entity.Dto;
using Entity.Entitys.ActivityEntity;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;

namespace ShanxiGameActivity.Controllers.ActivityControllers
{
    /// <summary>
    /// 任务进度管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TaskProgressController(
        TaskProgressService taskProgressService,
        PlayerTaskProgressDAL playerTaskProgressDAL,
        ActivityDAL activityDAL,
        ActivityTaskDAL activityTaskDAL) : BaseController
    {
        private readonly TaskProgressService _taskProgressService = taskProgressService;
        private readonly PlayerTaskProgressDAL _playerTaskProgressDAL = playerTaskProgressDAL;
        private readonly ActivityDAL _activityDAL = activityDAL;
        private readonly ActivityTaskDAL _activityTaskDAL = activityTaskDAL;

        /// <summary>
        /// 获取用户在指定活动中的任务进度
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>任务进度列表</returns>
        [HttpGet("{activityId}/{playerGameUserId}")]
        public async Task<Result<ActivityTaskProgressSummaryDto>> GetPlayerTaskProgressAsync(int activityId, string playerGameUserId)
        {
            try
            {
                // 获取活动信息
                var activity = await _activityDAL.GetByIdAsync(activityId);
                if (activity == null)
                {
                    return Fail<ActivityTaskProgressSummaryDto>("活动不存在");
                }

                // 获取任务进度
                var progressList = await _playerTaskProgressDAL.GetPlayerTaskProgressAsync(activityId, playerGameUserId);

                // 获取所有相关的任务信息
                var taskIds = progressList.Select(p => p.TaskId).Distinct().ToList();
                var tasks = new Dictionary<int, ActivityTask>();
                foreach (var taskId in taskIds)
                {
                    var task = await _activityTaskDAL.GetFirstAsync(new ActivityTaskDAL.ActivityTaskQuery { Id = taskId });
                    if (task != null)
                    {
                        tasks[taskId] = task;
                    }
                }

                // 转换为DTO
                var taskProgressDtos = progressList.Select(p =>
                {
                    var task = tasks.GetValueOrDefault(p.TaskId);
                    return new TaskProgressDetailResponseDto
                    {
                        Id = p.Id,
                        ActivityId = p.ActivityId,
                        TaskId = p.TaskId,
                        TaskName = task?.TaskName ?? "未知任务",
                        TaskType = task?.TaskType ?? TaskType.login,
                        TargetValue = task?.TargetValue ?? 0,
                        CurrentProgress = p.CurrentProgress,
                        CompletedTimes = p.CompletedTimes,
                        RewardChances = task?.RewardChances ?? 0,
                        RefreshType = task?.RefreshType ?? RefreshType.never,
                        IsCompleted = p.CurrentProgress >= (task?.TargetValue ?? 0),
                        RewardClaimed = p.RewardClaimed,
                        RewardClaimedTime = p.RewardClaimedTime,
                        RewardChancesEarned = p.RewardChancesEarned,
                        ValidFrom = p.ValidFrom,
                        ValidTo = p.ValidTo,
                        LastCompletedTime = p.LastCompletedTime
                    };
                }).ToList();

                var summary = new ActivityTaskProgressSummaryDto
                {
                    ActivityId = activityId,
                    ActivityName = activity.ActivityName,
                    PlayerGameUserId = playerGameUserId,
                    PlayerNickname = progressList.FirstOrDefault()?.PlayerNickname ?? "",
                    TaskProgresses = taskProgressDtos
                };

                return Success(summary, "获取任务进度成功");
            }
            catch (Exception ex)
            {
                return Fail<ActivityTaskProgressSummaryDto>($"获取任务进度失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 领取任务奖励
        /// </summary>
        /// <param name="request">领取请求</param>
        /// <returns>领取结果</returns>
        [HttpPost("claim-reward")]
        public async Task<Result<TaskRewardClaimResponseDto>> ClaimTaskRewardAsync([FromBody] TaskRewardClaimRequestDto request)
        {
            try
            {
                var result = await _taskProgressService.ClaimTaskRewardAsync(
                    request.ActivityId,
                    request.TaskId,
                    request.PlayerGameUserId);

                var response = new TaskRewardClaimResponseDto
                {
                    Success = result.Success,
                    Message = result.Message,
                    RewardChances = result.RewardChances,
                    ClaimedTime = result.Success ? DateTime.Now : null
                };

                if (result.Success)
                {
                    return Success(response, "奖励领取成功");
                }
                else
                {
                    return Success(response, result.Message);
                }
            }
            catch (Exception ex)
            {
                return Fail<TaskRewardClaimResponseDto>($"领取奖励失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 手动更新指定活动的任务进度
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>更新结果</returns>
        [HttpPost("update-progress/{activityId}")]
        public async Task<Result<string>> UpdateActivityTaskProgressAsync(int activityId)
        {
            try
            {
                var result = await _taskProgressService.UpdateActivityTaskProgressAsync(activityId);

                if (result.Success)
                {
                    return Success(result.Message, "任务进度更新成功");
                }
                else
                {
                    return Fail<string>(result.Message);
                }
            }
            catch (Exception ex)
            {
                return Fail<string>($"更新任务进度失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 手动更新所有活动的任务进度
        /// </summary>
        /// <returns>更新结果</returns>
        [HttpPost("update-all-progress")]
        public async Task<Result<string>> UpdateAllTaskProgressAsync()
        {
            try
            {
                var result = await _taskProgressService.UpdateAllTaskProgressAsync();

                if (result.Success)
                {
                    return Success(result.Message, "所有任务进度更新成功");
                }
                else
                {
                    return Fail<string>(result.Message);
                }
            }
            catch (Exception ex)
            {
                return Fail<string>($"更新所有任务进度失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取活动的任务配置和进度统计
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>任务统计信息</returns>
        [HttpGet("activity-stats/{activityId}")]
        public async Task<Result<object>> GetActivityTaskStatsAsync(int activityId)
        {
            try
            {
                // 获取活动信息
                var activity = await _activityDAL.GetByIdAsync(activityId);
                if (activity == null)
                {
                    return Fail<object>("活动不存在");
                }

                // 获取参与人数
                var participantsCount = await _playerTaskProgressDAL.GetActivityParticipantsCountAsync(activityId);

                // 获取所有任务进度
                var allProgress = await _playerTaskProgressDAL.GetListAsync(new PlayerTaskProgressDAL.PlayerTaskProgressQuery
                {
                    ActivityId = activityId
                });

                var stats = new
                {
                    ActivityId = activityId,
                    ActivityName = activity.ActivityName,
                    ParticipantsCount = participantsCount,
                    TotalTaskRecords = allProgress.Count,
                    CompletedTaskRecords = 0, // 需要手动计算，因为没有导航属性
                    ClaimedRewardRecords = allProgress.Count(p => p.RewardClaimed),
                    TotalRewardChancesEarned = allProgress.Sum(p => p.RewardChancesEarned)
                };

                return Success((object)stats, "获取活动任务统计成功");
            }
            catch (Exception ex)
            {
                return Fail<object>($"获取活动任务统计失败：{ex.Message}");
            }
        }
    }
}
