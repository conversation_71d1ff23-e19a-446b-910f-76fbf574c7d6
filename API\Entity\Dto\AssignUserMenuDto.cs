using System.ComponentModel.DataAnnotations;

namespace Entity.Dto
{
    /// <summary>
    /// 用户分配菜单权限请求DTO
    /// </summary>
    public class AssignUserMenuDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 菜单ID列表
        /// </summary>
        public List<string> MenuIds { get; set; } = [];
    }
}