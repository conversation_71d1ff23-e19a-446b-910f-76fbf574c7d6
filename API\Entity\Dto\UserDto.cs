using System.ComponentModel.DataAnnotations;

namespace Entity.Dto
{
    /// <summary>
    /// 用户基础信息DTO
    /// </summary>
    public class UserBaseDto
    {
        /// <summary>
        /// 真实姓名,最大长度50个字符
        /// </summary>
        [MaxLength(50)]
        public string RealName { get; set; } = string.Empty;

        /// <summary>
        /// 头像URL地址,最大长度200个字符
        /// </summary>
        [MaxLength(200)]
        public string Avatar { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮箱地址,最大长度100个字符,必须符合邮箱格式
        /// </summary>
        //[EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 手机号码,最大长度20个字符,必须符合手机号格式
        /// </summary>
        //[Phone]
        [MaxLength(20)]
        public string Mobile { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息,最大长度500个字符
        /// </summary>
        [MaxLength(500)]
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 用户状态,0:禁用 1:启用
        /// </summary>
        public byte Status { get; set; }
    }

    /// <summary>
    /// 创建用户DTO,继承自UserBaseDto
    /// </summary>
    public class CreateUserDto : UserBaseDto
    {
        /// <summary>
        /// 用户名,必填,最大长度50个字符
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [MaxLength(50)]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 密码,必填,最大长度100个字符
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        [MaxLength(100)]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 更新用户DTO,继承自UserBaseDto
    /// </summary>
    public class UpdateUserDto : UserBaseDto
    {
        /// <summary>
        /// 用户ID,必填
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户信息DTO,继承自UserBaseDto,用于返回完整的用户信息
    /// </summary>
    public class UserDto : UserBaseDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// 角色编码
        /// </summary>
        public string RoleCode { get; set; } = string.Empty;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最后登录IP地址
        /// </summary>
        public string? LastLoginIp { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }

    // /// <summary>
    // /// 用户查询DTO,用于查询用户列表时的条件参数
    // /// </summary>
    // public class QueryUserDto : BaseQueryDto
    // {
    //     /// <summary>
    //     /// 用户名(模糊查询)
    //     /// </summary>
    //     public string UserName { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 真实姓名(模糊查询)
    //     /// </summary>
    //     public string RealName { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 手机号码(模糊查询)
    //     /// </summary>
    //     public string Mobile { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 电子邮箱(模糊查询)
    //     /// </summary>
    //     public string Email { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 用户状态(精确查询) 0:禁用 1:启用
    //     /// </summary>
    //     public byte? Status { get; set; }

    //     /// <summary>
    //     /// 开始时间,用于按创建时间范围查询
    //     /// </summary>
    //     public DateTime? StartTime { get; set; }

    //     /// <summary>
    //     /// 结束时间,用于按创建时间范围查询
    //     /// </summary>
    //     public DateTime? EndTime { get; set; }
    // }

    /// <summary>
    /// 修改密码DTO,用于用户修改自己的密码
    /// </summary>
    public class ChangePasswordDto
    {
        /// <summary>
        /// 用户ID,必填
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 旧密码,必填
        /// </summary>
        [Required]
        public string OldPassword { get; set; } = string.Empty;

        /// <summary>
        /// 新密码,必填,最小长度6个字符
        /// </summary>
        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 重置密码DTO,用于管理员重置用户密码
    /// </summary>
    public class ResetPasswordDto
    {
        /// <summary>
        /// 用户ID,必填
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 新密码,必填,最小长度6个字符
        /// </summary>
        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户角色信息DTO，用于返回用户关联的角色信息
    /// </summary>
    public class UserRoleDto
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public string RoleId { get; set; } = string.Empty;

        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// 角色编码
        /// </summary>
        public string RoleCode { get; set; } = string.Empty;

        /// <summary>
        /// 角色描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}