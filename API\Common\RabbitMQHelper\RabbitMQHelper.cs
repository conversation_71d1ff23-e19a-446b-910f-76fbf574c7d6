﻿
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Collections;
using System.Text;

namespace Common.RabbitMQHelper
{
    public static class RabbitMQHelper
    {
        //链接工厂
        private static readonly ConnectionFactory _connectionFactory;

        private static readonly IConnection connection;

        private static readonly IModel channel;
        static RabbitMQHelper()
        {
            _connectionFactory = new ConnectionFactory()
            {
                HostName = RabbitMQSetting.HostName,
                Port = Convert.ToInt32(RabbitMQSetting.Port),
                UserName = RabbitMQSetting.UserName,
                Password = RabbitMQSetting.Password,
                VirtualHost = RabbitMQSetting.VirtualHost,
            };
            //创建TCP长链接
            connection ??= _connectionFactory.CreateConnection();
            //创建通道，相当于TCP中虚拟链接
            channel ??= connection.CreateModel();
        }

        /// <summary>
        /// 队列缓存
        /// </summary>
        private static readonly Hashtable MQCache = Hashtable.Synchronized([]);

        /// <summary>
        /// 获取ConnectionFactory对象
        /// </summary>
        /// <returns></returns>
        public static ConnectionFactory GetConnectionFactory(string HostName, int Port, string UserName, string Password, string VirtualHost)
        {
            return new ConnectionFactory()
            {
                HostName = HostName,
                Port = Port,
                UserName = UserName,
                Password = Password,
                VirtualHost = VirtualHost,
            };
        }

        #region TCP长连接

        /// <summary>
        /// 获取TCP长连接 并且创建IModel
        /// </summary>
        /// <returns></returns>
        //public static IConnection GetConnection()
        //{
        //    //创建TCP长链接
        //    if (connection == null) connection = _connectionFactory.CreateConnection();
        //    //创建通道，相当于TCP中虚拟链接
        //    if (channel == null) channel = connection.CreateModel();

        //    return connection;
        //}

        /// <summary>
        /// 关闭TCP长连接
        /// </summary>
        /// <returns></returns>
        public static void CloseConnection(IConnection connection) => connection.Close();

        #endregion


        /// <summary>
        /// 重新加入队列
        /// </summary>
        /// <param name="queue"></param>
        /// <param name="ea"></param>
        public static void ReProducer(string queue, BasicDeliverEventArgs ea)
            => channel.BasicPublish("", queue, ea.BasicProperties, ea.Body);


        /// <summary>
        /// 生产者创建队列 
        /// </summary>
        /// <param name="message">队列信息</param>
        /// <param name="queue">队列名称</param>
        /// <param name="exchange">交换机名称</param>
        /// <param name="routingKey">路由KEY</param>
        /// <param name="props"></param>
        /// <param name="Expiration">过期时间</param>
        public static void Producer(string message, string queue, string exchange, string routingKey, IDictionary<string, object> props, int Expiration, IDictionary<string, object>? headers = null)
        {
            //生成队列缓存Key
            string MQCacheKey = $"{queue}{exchange}{routingKey}{JsonConvert.SerializeObject(props)}";

            var properties = channel.CreateBasicProperties();

            //如果已经创建过队列则无需生成
            if (!MQCache.ContainsKey(MQCacheKey))
            {
                /**
                * 创建队列，声明并创建一个队列，如果队列已存在，则使用这个队列
                * 第一个参数：队列ID
                * 第二个参数：是否持久化，false对应不持久化数据,MQ停止之后数据就会丢失
                * 第三个参数：是否队列私有化，false则代表所有的消费者都可以访问，true代表只有第一次拥有它的消费者才能一直使用
                * 第四个参数: 是否自动删除，false代表链接停掉之后不自动删除这个队列
                * 其他额外参数为null
                * */
                if (props.Count > 0)
                {
                    string deadExchange = props["x-dead-letter-exchange"]?.ToString() ?? "";
                    string deadRoutingKey = props["x-dead-letter-routing-key"]?.ToString() ?? "";
                    if (!string.IsNullOrEmpty(deadExchange) && !string.IsNullOrEmpty(deadRoutingKey))
                    {
                        // 创建延时队列
                        //延时队列连接通道
                        var delayChannel = connection.CreateModel();
                        string delayQueue = queue + "-delay";
                        delayChannel.ExchangeDeclare(deadExchange, "direct");//创建交换器
                        delayChannel.QueueDeclare(delayQueue, true, false, false, null);
                        delayChannel.QueueBind(delayQueue, deadExchange, deadRoutingKey);
                        //最多接受条数 0为无限制，每次消费消息数(根据实际场景设置)，true=作用于整channel,false=作用于具体的消费者
                        delayChannel.BasicQos(0, 10, false);
                    }
                }
                // 队列申明
                channel.QueueDeclare(queue, true, false, false, props);
                MQCache[MQCacheKey] = "";
            }
            // var properties = channel.CreateBasicProperties();
            // 设置过期时间
            properties.Expiration = Expiration.ToString();
            properties.Headers = headers ?? new Dictionary<string, object>
            {
                { "retry", 1 }
            };
            // 发布队列
            channel.BasicPublish(exchange, routingKey == "" ? queue : routingKey, properties, Encoding.UTF8.GetBytes(message));

        }

        /// <summary>
        /// 消费者
        /// </summary>
        /// <param name="queue">队列名称</param>
        /// <param name="callback"></param>
        /// <param name="maxRetry">重试次数</param>
        /// <exception cref="Exception"></exception>
        public static void Consunmer(string queue, Func<RabbitMessageEntity, bool> callback, int maxRetry = 0)
        {
            var modelChannel = connection.CreateModel();
            /**
             * 创建队列，声明并创建一个队列，如果队列已存在，这使用这个队列
             * 第一个参数：队列ID
             * 第二个参数：是否持久化，false对应不持久化数据,MQ停止之后数据就会丢失
             * 第三个参数：是否队列私有化，false则代表所有的消费者都可以访问，true代表只有第一次拥有它的消费者才能一直使用
             * 第四个参数: 是否自动删除，false代表链接停掉之后不自动删除这个队列
             * 其他额外参数为null
             * **/
            modelChannel.BasicQos(0, 2, false);

            /**
             * 从MQ服务器中获取数据
             * 创建一个消息消费者
             * 第一个参数：队列名
             * 第二个参数：是否自动确认收到消息，false代表手动确认消息，这个是MQ推荐的做法
             * 第三个参数：要传入IBasicConsumer接口
             * **/
            //创建事件
            EventingBasicConsumer comsumers = new(modelChannel);
            modelChannel.BasicConsume(queue, false, comsumers);

            comsumers.Received += async (object? sender, BasicDeliverEventArgs args) =>
            {
                await Task.Run(() =>
                {
                    if (sender == null) throw new Exception("sender 参数为空");
                    RabbitMessageEntity rabbitMessage = new();
                    try
                    {
                        rabbitMessage.Content = Encoding.UTF8.GetString(args.Body.ToArray());
                        rabbitMessage.Consumer = (EventingBasicConsumer)sender;
                        rabbitMessage.BasicDeliver = args;
                        //调用委托 返回结果为是否签收消息
                        if (callback(rabbitMessage))
                        {
                            /**
                             * 确认签收当前消息
                             * 第一个参数 事件标签
                             * 第二个参数 是否确认多个以传递的消息
                             **/
                            rabbitMessage.Consumer.Model.BasicAck(args.DeliveryTag, false);
                        }
                        else
                        {
                            if (WhetherRetry(args, maxRetry))
                            {
                                // 拒绝签收消息
                                rabbitMessage.Consumer.Model.BasicNack(args.DeliveryTag, false, false);
                                // 添加重试次数
                                var recount = GetRetryCount(args) + 1;
                                args.BasicProperties.Headers["retry"] = recount;
                                // 重新加入队列
                                ReProducer(queue, args);
                                return;
                            }
                            // 拒绝签收消息
                            rabbitMessage.Consumer.Model.BasicNack(args.DeliveryTag, false, false);
                        }
                    }
                    catch (Exception e)
                    {
                        rabbitMessage.Exception = e;
                        rabbitMessage.Code = 500;
                        /**
                        * 第一个参数 事件标签
                        * 第二个参数 是否拒绝多个以传递的消息
                        * 第三个参数 是否重新排队
                        **/
                        rabbitMessage.Consumer?.Model.BasicNack(args.DeliveryTag, false, false);

                        throw new Exception($"消息消费失败,队列内容：{JsonConvert.SerializeObject(args)}", e);
                    }
                });
            };
        }

        /// <summary>
        /// 是否重试
        /// </summary>
        /// <param name="ea"></param>
        /// <param name="maxReCount"></param>
        /// <returns></returns>
        private static bool WhetherRetry(BasicDeliverEventArgs ea, int maxReCount)
        {
            if (maxReCount < 1) return false;
            // 获取队列重试次数
            int reCount = GetRetryCount(ea);
            if (reCount < 1) return false;
            // 判断是否超过重试次数
            if (reCount < maxReCount) return true;
            return false;
        }
        /// <summary>
        /// 获取队列重试次数
        /// </summary>
        /// <param name="ea"></param>
        /// <returns></returns>
        private static int GetRetryCount(BasicDeliverEventArgs ea)
        {
            var headers = ea.BasicProperties.Headers;
            if (headers == null) return 0;
            if (!int.TryParse(headers["retry"].ToString(), out int reCount)) return 0;
            return reCount;
        }

    }
    /// <summary>
    /// 消息重试配置
    /// </summary>
    public class MQRetryConfig
    {
        public int RetryCount { get; set; }
    }

    public class RabbitMessageEntity
    {
        public string? Content { get; set; }
        public EventingBasicConsumer? Consumer { get; set; }
        public BasicDeliverEventArgs? BasicDeliver { get; set; }
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
        public int Code { get; set; }
    }
}