using BLL.SysService;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ShanxiGameActivity.Controllers.Attributes;
using ShanxiGameActivity.Controllers.BasisController;
using ShanxiGameActivity.Controllers.BasisController.ResuItEntity;
using System.Reflection;

namespace ShanxiGameActivity.Controllers.SysControllers
{
    /// <summary>
    /// 按钮管理控制器
    /// </summary>
    [Permission]
    public class ButtonController(SysButtonService buttonService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 按钮服务接口
        /// </summary>
        private readonly SysButtonService _buttonService = buttonService;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 重新获取权限数据
        /// </summary>
        /// <returns>操作结果</returns>
        [FunctionPermission("button:sync", "同步按钮")]
        [HttpPost("sync")]
        public async Task<Result<bool>> SyncButtons()
        {
            // 获取当前应用程序域中所有的程序集
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();

            // 存储所有FunctionPermission的元数据
            var permissionMetadataList = new List<PermissionMetadataDto>();

            foreach (var assembly in assemblies)
            {
                // 获取程序集中的所有类型
                var types = assembly.GetTypes();

                foreach (var type in types)
                {
                    // 获取类型中的所有方法
                    var methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly);

                    foreach (var method in methods)
                    {
                        // 获取FunctionPermission特性
                        var functionPermissionAttribute = method.GetCustomAttribute<FunctionPermissionAttribute>();

                        if (functionPermissionAttribute != null)
                            // 提取权限编码和描述
                            permissionMetadataList.Add(new PermissionMetadataDto
                            {
                                PermissionCode = functionPermissionAttribute.Code,
                                PermissionDescription = functionPermissionAttribute.Description
                            });
                    }
                }
            }

            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 调用服务层同步按钮数据
            var result = await _buttonService.SyncButtonsAsync(permissionMetadataList);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "按钮管理",
                Operation = "重新获取权限数据",
                BusinessObject = "SysButton",
                ObjectId = "N/A",
                DetailedInfo = $"用户 {currentUser.UserName} {(result ? "成功" : "失败")}同步了按钮权限数据，共 {permissionMetadataList.Count} 条权限记录",
                AfterData = new { PermissionCount = permissionMetadataList.Count, Permissions = permissionMetadataList },
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(result, result ? "重新获取权限数据成功" : "重新获取权限数据失败");
        }

    }
}