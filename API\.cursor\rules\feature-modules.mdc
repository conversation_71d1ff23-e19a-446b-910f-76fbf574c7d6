---
description: feature-modules
globs: 
alwaysApply: false
---
# 功能模块

## 系统模块
系统模块主要处理用户、角色、权限等系统基础功能：
- 控制器: [DataMgrSystem/Controllers/SysControllers/](mdc:DataMgrSystem/Controllers/SysControllers)
- 服务: [BLL/SysService/](mdc:BLL/SysService)
- 数据访问: [DAL/SysDAL/](mdc:DAL/SysDAL)
- 实体: [Entity/Entitys/SysEntity/](mdc:Entity/Entitys/SysEntity)
- DTO: [Entity/Dto/OrganizationDto/](mdc:Entity/Dto/OrganizationDto)

### 主要功能
- 用户管理：注册、登录、修改信息等
- 角色与权限管理
- 组织机构管理
- 系统日志与审计

## 卡片模块
卡片模块处理与卡片相关的业务功能：
- 控制器: [DataMgrSystem/Controllers/CardControllers/](mdc:DataMgrSystem/Controllers/CardControllers)
- 服务: [BLL/CardService/](mdc:BLL/CardService)
- 数据访问: [DAL/CardDAL/](mdc:DAL/CardDAL)
- 实体: [Entity/Entitys/CardEntity/](mdc:Entity/Entitys/CardEntity)
- DTO: [Entity/Dto/CardDto/](mdc:Entity/Dto/CardDto)

### 主要功能
- 卡片创建与管理
- 卡片状态更新
- 卡片数据导出
- 卡片类型管理

## 基础服务模块
提供系统基础功能支持：
- 服务: [BLL/BaseService/](mdc:BLL/BaseService)
- 实现接口如：IBaseService, ICreateSupport, IUpdateSupport等

### 主要功能
- 通用CRUD操作
- 分页查询
- 数据验证
- 实体映射

## 公共组件
- 缓存管理: [Common/Caches/](mdc:Common/Caches)
- JWT认证: [Common/JWT/](mdc:Common/JWT)
- 导出功能: [Common/Export/](mdc:Common/Export) 和 [BLL/SysService/Exports/](mdc:BLL/SysService/Exports)
- 日志记录: [Common/Log4Net/](mdc:Common/Log4Net)
- Redis工具: [Common/redis/](mdc:Common/redis)

## 中间件与过滤器
- 中间件: [DataMgrSystem/Controllers/Middleware/](mdc:DataMgrSystem/Controllers/Middleware)
- 过滤器: [DataMgrSystem/Controllers/Filter/](mdc:DataMgrSystem/Controllers/Filter)
- 特性: [DataMgrSystem/Controllers/Attributes/](mdc:DataMgrSystem/Controllers/Attributes)

