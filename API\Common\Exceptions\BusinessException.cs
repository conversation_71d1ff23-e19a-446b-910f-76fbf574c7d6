namespace Common.Exceptions
{
    /// <summary>
    /// 业务异常类，用于表示业务逻辑错误
    /// </summary>
    public class BusinessException : Exception
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; }

        /// <summary>
        /// 错误级别
        /// </summary>
        public ErrorLevel Level { get; }

        /// <summary>
        /// 创建业务异常实例
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="errorCode">错误代码，默认为400</param>
        /// <param name="level">错误级别，默认为警告</param>
        public BusinessException(string message, int errorCode = 500, ErrorLevel level = ErrorLevel.Warning)
            : base(message)
        {
            ErrorCode = errorCode;
            Level = level;
        }

        /// <summary>
        /// 创建业务异常实例
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        /// <param name="errorCode">错误代码，默认为400</param>
        /// <param name="level">错误级别，默认为警告</param>
        public BusinessException(string message, Exception innerException, int errorCode = 500, ErrorLevel level = ErrorLevel.Warning)
            : base(message, innerException)
        {
            ErrorCode = errorCode;
            Level = level;
        }
    }

    /// <summary>
    /// 错误级别枚举
    /// </summary>
    public enum ErrorLevel
    {
        /// <summary>
        /// 信息级别 - 不严重的问题
        /// </summary>
        Info,

        /// <summary>
        /// 警告级别 - 需要注意但不会导致程序崩溃的问题
        /// </summary>
        Warning,

        /// <summary>
        /// 错误级别 - 严重问题，可能影响系统部分功能
        /// </summary>
        Error,

        /// <summary>
        /// 严重错误 - 可能导致系统崩溃的致命问题
        /// </summary>
        Critical
    }
}