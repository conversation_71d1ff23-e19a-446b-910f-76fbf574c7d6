using Common.Autofac;
using DAL.ActivityDAL;
using Entity;
using Entity.Dto;
using Entity.Entitys.ActivityEntity;

namespace BLL.ActivityService
{
    /// <summary>
    /// 活动服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class ActivityService(
        ActivityDAL activityDAL,
        ActivityRewardDAL activityRewardDAL,
        ActivityTaskDAL activityTaskDAL,
        PlayerTaskProgressDAL playerTaskProgressDAL,
        UserCacheService userCacheService)
    {
        private readonly ActivityDAL _activityDAL = activityDAL;
        private readonly ActivityRewardDAL _activityRewardDAL = activityRewardDAL;
        private readonly ActivityTaskDAL _activityTaskDAL = activityTaskDAL;
        private readonly PlayerTaskProgressDAL _playerTaskProgressDAL = playerTaskProgressDAL;
        private readonly UserCacheService _userCacheService = userCacheService;

        /// <summary>
        /// 创建活动
        /// </summary>
        /// <param name="request">创建活动请求</param>
        /// <param name="creatorGameUserId">创建者游戏用户ID</param>
        /// <param name="creatorNickname">创建者昵称</param>
        /// <returns>创建的活动信息</returns>
        public async Task<ActivityResponseDto> CreateActivityAsync(
            CreateActivityDto request,
            string creatorGameUserId,
            string creatorNickname)
        {
            // 验证请求参数
            ValidateCreateActivityRequest(request);

            // 确保创建者昵称已缓存
            await _userCacheService.EnsureUserNicknameCachedAsync(creatorGameUserId, creatorNickname);

            // 创建活动实体
            var activity = new ActivityInfo
            {
                CreatorGameUserId = creatorGameUserId,
                CreatorNickname = creatorNickname,
                ActivityName = request.ActivityName,
                ActivityType = request.ActivityType,
                TotalTongbao = request.TotalTongbao,
                RemainingTongbao = request.TotalTongbao,
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                Status = request.StartTime <= DateTime.Now ? ActivityStatus.running : ActivityStatus.pending,
                CreateTime = DateTime.Now
            };

            // 保存活动
            var createdActivity = await _activityDAL.CreateAsync(activity);

            // 创建奖励配置
            var rewards = request.Rewards.Select((reward, index) => new ActivityReward
            {
                ActivityId = createdActivity.Id,
                RewardName = reward.RewardName,
                RewardType = reward.RewardType,
                TongbaoAmount = reward.TongbaoAmount,
                PhysicalItem = reward.PhysicalItem,
                Weight = reward.Weight,
                Probability = 0, // 稍后计算
                DailyQuantity = reward.DailyQuantity,
                RemainingQuantity = reward.DailyQuantity,
                SortOrder = index,
                CreateTime = DateTime.Now
            }).ToList();

            await _activityRewardDAL.CreateBatchAsync(rewards);

            // 计算奖励概率
            await _activityRewardDAL.CalculateProbabilitiesAsync(createdActivity.Id);

            // 创建任务配置
            var tasks = request.Tasks.Select((task, index) => new ActivityTask
            {
                ActivityId = createdActivity.Id,
                TaskType = task.TaskType,
                TaskName = task.TaskName,
                TargetValue = task.TargetValue,
                RewardChances = task.RewardChances,
                RefreshType = task.RefreshType,
                SortOrder = index,
                CreateTime = DateTime.Now
            }).ToList();

            await _activityTaskDAL.CreateBatchAsync(tasks);

            return new ActivityResponseDto
            {
                Id = createdActivity.Id,
                ActivityName = createdActivity.ActivityName,
                ActivityType = createdActivity.ActivityType,
                TotalTongbao = createdActivity.TotalTongbao,
                RemainingTongbao = createdActivity.RemainingTongbao,
                StartTime = createdActivity.StartTime,
                EndTime = createdActivity.EndTime,
                Status = createdActivity.Status,
                ParticipantsCount = 0,
                CreatorNickname = createdActivity.CreatorNickname
            };
        }

        /// <summary>
        /// 获取活动列表
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <param name="currentUserGameId">当前用户游戏ID</param>
        /// <returns>活动列表</returns>
        public async Task<(List<ActivityResponseDto> activities, int total)> GetActivitiesAsync(
            ActivityQueryDto query,
            string currentUserGameId)
        {
            List<ActivityInfo> activities;
            int total;

            if (query.Scope == "all")
            {
                // 查看所有活动（仅盟主可用，这里暂不验证权限）
                (activities, total) = await _activityDAL.GetAllActivitiesAsync(query.Status, query.Page, query.Limit);
            }
            else
            {
                // 查看自己创建的活动
                (activities, total) = await _activityDAL.GetUserActivitiesAsync(currentUserGameId, query.Status, query.Page, query.Limit);
            }

            var activityDtos = new List<ActivityResponseDto>();

            foreach (var activity in activities)
            {
                var participantsCount = await _playerTaskProgressDAL.GetActivityParticipantsCountAsync(activity.Id);

                activityDtos.Add(new ActivityResponseDto
                {
                    Id = activity.Id,
                    ActivityName = activity.ActivityName,
                    ActivityType = activity.ActivityType,
                    TotalTongbao = activity.TotalTongbao,
                    RemainingTongbao = activity.RemainingTongbao,
                    StartTime = activity.StartTime,
                    EndTime = activity.EndTime,
                    Status = activity.Status,
                    ParticipantsCount = participantsCount,
                    CreatorNickname = activity.CreatorNickname
                });
            }

            return (activityDtos, total);
        }

        /// <summary>
        /// 获取活动详情
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <returns>活动详情</returns>
        public async Task<ActivityDetailResponseDto?> GetActivityDetailAsync(int activityId)
        {
            var activity = await _activityDAL.GetActivityWithDetailsAsync(activityId);
            if (activity == null)
                return null;

            var participantsCount = await _playerTaskProgressDAL.GetActivityParticipantsCountAsync(activityId);

            // 手动查询关联的奖励和任务
            var rewards = await _activityRewardDAL.GetListAsync(new ActivityRewardDAL.ActivityRewardQuery { ActivityId = activityId });
            var tasks = await _activityTaskDAL.GetListAsync(new ActivityTaskDAL.ActivityTaskQuery { ActivityId = activityId });

            return new ActivityDetailResponseDto
            {
                Id = activity.Id,
                ActivityName = activity.ActivityName,
                ActivityType = activity.ActivityType,
                TotalTongbao = activity.TotalTongbao,
                RemainingTongbao = activity.RemainingTongbao,
                StartTime = activity.StartTime,
                EndTime = activity.EndTime,
                Status = activity.Status,
                ParticipantsCount = participantsCount,
                CreatorNickname = activity.CreatorNickname,
                Rewards = rewards.Select(r => new ActivityRewardResponseDto
                {
                    Id = r.Id,
                    RewardName = r.RewardName,
                    RewardType = r.RewardType,
                    TongbaoAmount = r.TongbaoAmount,
                    PhysicalItem = r.PhysicalItem,
                    Probability = r.Probability,
                    RemainingQuantity = r.RemainingQuantity
                }).ToList(),
                Tasks = tasks.Select(t => new ActivityTaskResponseDto
                {
                    Id = t.Id,
                    TaskType = t.TaskType,
                    TaskName = t.TaskName,
                    TargetValue = t.TargetValue,
                    RewardChances = t.RewardChances,
                    RefreshType = t.RefreshType
                }).ToList()
            };
        }

        /// <summary>
        /// 关闭活动
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="currentUserGameId">当前用户游戏ID</param>
        /// <returns>是否关闭成功</returns>
        public async Task<bool> CloseActivityAsync(int activityId, string currentUserGameId)
        {
            var activity = await _activityDAL.GetByIdAsync(activityId);
            if (activity == null)
                return false;

            // 验证权限：只有创建者可以关闭活动
            if (activity.CreatorGameUserId != currentUserGameId)
                return false;

            // 只有运行中或已结束的活动可以关闭
            if (activity.Status != ActivityStatus.running && activity.Status != ActivityStatus.ended)
                return false;

            return await _activityDAL.UpdateActivityStatusAsync(activityId, ActivityStatus.stopped);
        }

        /// <summary>
        /// 验证创建活动请求
        /// </summary>
        /// <param name="request">创建活动请求</param>
        private static void ValidateCreateActivityRequest(CreateActivityDto request)
        {
            if (request.StartTime >= request.EndTime)
                throw new ArgumentException("活动开始时间必须早于结束时间");

            if (request.EndTime <= DateTime.Now)
                throw new ArgumentException("活动结束时间不能早于当前时间");

            if (request.Rewards.Count == 0)
                throw new ArgumentException("活动必须配置至少一个奖励");

            if (request.Tasks.Count == 0)
                throw new ArgumentException("活动必须配置至少一个任务");

            // 验证奖励配置
            var totalWeight = request.Rewards.Sum(r => r.Weight);
            if (totalWeight <= 0)
                throw new ArgumentException("奖励权重总和必须大于0");

            // 验证任务配置
            var taskTypes = request.Tasks.Select(t => t.TaskType).ToList();
            if (taskTypes.Count != taskTypes.Distinct().Count())
                throw new ArgumentException("不能配置重复的任务类型");
        }

        #region 神秘盒子活动业务逻辑

        /// <summary>
        /// 验证用户是否为指定创建者的下级用户
        /// </summary>
        /// <param name="clubId">活动创建者所属俱乐部ID</param>
        /// <param name="creatorId">活动创建者ID</param>
        /// <param name="targetUserId">要验证的下级ID</param>
        /// <returns>验证结果</returns>
        public async Task<bool> ValidateDownUserAsync(int clubId, int creatorId, int targetUserId)
        {
            // 参数验证
            if (clubId <= 0 || creatorId <= 0 || targetUserId <= 0)
            {
                throw new ArgumentException("参数无效：所有ID必须大于0");
            }

            var count = await _activityDAL.IsDownUserAsync(clubId, creatorId, targetUserId);
            var isValid = count > 0;

            // 添加调试日志
            Console.WriteLine($"ValidateDownUserAsync验证结果: clubId={clubId}, creatorId={creatorId}, targetUserId={targetUserId}, 验证结果={isValid} (count={count})");

            return isValid;
        }

        /// <summary>
        /// 获取今日用户游戏数据
        /// </summary>
        /// <param name="clubId">活动创建者所属俱乐部ID</param>
        /// <param name="creatorId">活动创建者ID</param>
        /// <returns>今日游戏数据列表</returns>
        public async Task<List<TodayGameDataResult>> GetTodayGameDataAsync(int clubId, int creatorId)
        {
            // 参数验证
            if (clubId <= 0 || creatorId <= 0)
            {
                throw new ArgumentException("参数无效：俱乐部ID和创建者ID必须大于0");
            }

            return await _activityDAL.GetTodayUsersGameDataAsync(clubId, creatorId);
        }

        /// <summary>
        /// 更新用户通宝
        /// </summary>
        /// <param name="clubId">活动创建者所属俱乐部ID</param>
        /// <param name="creatorId">活动创建者ID</param>
        /// <param name="operationType">操作类型：1-从用户身上扣除通宝注入奖池，2-从奖池中扣除通宝</param>
        /// <param name="targetUserId">被操作者ID</param>
        /// <param name="pointAmount">本次操作的通宝数量</param>
        /// <returns>更新结果列表</returns>
        public async Task<List<UpdatePointResult>> UpdateUserPointAsync(int clubId, int creatorId, int operationType, int targetUserId, int pointAmount)
        {
            // 参数验证
            if (clubId <= 0 || creatorId <= 0 || targetUserId <= 0)
            {
                throw new ArgumentException("参数无效：所有ID必须大于0");
            }

            if (operationType != 1 && operationType != 2)
            {
                throw new ArgumentException("操作类型无效：必须为1（扣除通宝注入奖池）或2（从奖池扣除通宝）");
            }

            if (pointAmount <= 0)
            {
                throw new ArgumentException("通宝数量必须大于0");
            }

            return await _activityDAL.UpdateUserPointAsync(clubId, creatorId, operationType, targetUserId, pointAmount);
        }

        #endregion
    }
}
