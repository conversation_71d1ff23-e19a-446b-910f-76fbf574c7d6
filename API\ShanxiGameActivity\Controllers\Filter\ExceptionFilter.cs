﻿using BLL.SysService;
using Common;
using Common.Https;
using Common.Log4Net;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Security.Claims;
using System.Text.Json;

namespace ShanxiGameActivity.Controllers.Filter
{
    /// <summary>
    /// 全局异常过滤器
    /// 用于捕获和处理应用程序中所有未处理的异常
    /// </summary>
    /// <remarks>
    /// 构造函数，注入日志服务
    /// </remarks>
    /// <param name="logService">日志服务</param>
    public class ExceptionFilter(SysLogService logService) : IAsyncExceptionFilter
    {
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 异常发生时的处理方法
        /// </summary>
        /// <param name="context">异常上下文,包含异常信息和HTTP上下文</param>
        public async Task OnExceptionAsync(ExceptionContext context)
        {
            // 检查异常是否已经被处理
            if (!context.ExceptionHandled)
            {
                // 获取完整的异常信息,包括内部异常
                string message = GetFullExceptionMessage(context.Exception);
                // 获取发生异常的请求路径
                var path = context.HttpContext.Request.Path.Value;
                // 获取异常对象
                var exception = context.Exception;
                // 获取异常的堆栈跟踪信息
                var stackTrace = exception.StackTrace;
                // 获取客户端IP
                var ip = context.HttpContext.GetIPAddress();
                // 获取请求方法
                var method = context.HttpContext.Request.Method;
                // 获取用户信息
                var userId = context.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
                var username = context.HttpContext.User.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty;

                // 记录系统日志
                var logDto = new CreateLogDto
                {
                    UserId = userId,
                    Username = username,
                    Operation = "系统异常",
                    Method = context.HttpContext.Request.Method,
                    Params = JsonSerializer.Serialize(new { Path = path ?? string.Empty }),
                    Time = 0,
                    Ip = ip,
                    Path = path ?? string.Empty,
                    LogType = "System",
                    LogLevel = LogLevel.Error.ToString(),
                    Message = $"路径: {path}",
                    Exception = exception?.ToString()
                };

                var currentUser = new CurrentUserInfoDto { UserId = userId, UserName = username };
                await _logService.CreateAsync(logDto);

                // 发送异常信息到远程日志服务
                _ = Task.Run(() =>
                {
                    // 写入本地日志文件
                    LoggerHelper<ExceptionFilter>.Error($"错误位置：{path ?? string.Empty}  错误信息：{message}", exception);

                    // 发送异常信息到远程日志服务
                    _ = HttpHelper.PostAsync(LogServiceSetting.Path, new
                    {
                        projectName = "BasisWebApi", // 项目名称
                        logLevel = "ERROR", // 日志级别
                        path, // 异常发生的路径
                        parameter = JsonSerializer.Serialize(new // 序列化异常相关参数
                        {
                            Path = path,
                            Message = message,
                            Exception = exception?.GetType().Name ?? "Unknown"
                        }),
                        message, // 异常消息
                        stackTrace, // 堆栈跟踪
                        createTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fff") // 异常发生时间
                    });
                });

                // 标记异常已处理
                context.ExceptionHandled = true;
                // 返回统一的错误响应格式
                context.Result = new JsonResult(new
                {
                    Code = 500, // HTTP状态码
                    Success = false, // 处理结果
                    Msg = message // 错误信息
                });
            }
        }

        /// <summary>
        /// 获取完整的异常信息,包括所有内部异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>包含所有内部异常信息的字符串,用箭头连接</returns>
        private static string GetFullExceptionMessage(Exception ex)
        {
            var messages = new List<string>();
            var currentEx = ex;

            // 遍历异常链,获取所有内部异常信息
            while (currentEx != null)
            {
                messages.Add(currentEx.Message);
                currentEx = currentEx.InnerException;
            }

            // 用箭头符号连接所有异常信息
            return string.Join(" -> ", messages);
        }
    }
}
