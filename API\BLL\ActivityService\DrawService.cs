using Common.Autofac;
using DAL.ActivityDAL;
using Entity;
using Entity.Dto;
using Entity.Entitys.ActivityEntity;

namespace BLL.ActivityService
{
    /// <summary>
    /// 抽奖服务
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class DrawService(
        ActivityDAL activityDAL,
        ActivityRewardDAL activityRewardDAL,
        PlayerDrawChanceDAL playerDrawChanceDAL,
        DrawRecordDAL drawRecordDAL,
        PrizeRecordDAL prizeRecordDAL,
        UserCacheService userCacheService)
    {
        private readonly ActivityDAL _activityDAL = activityDAL;
        private readonly ActivityRewardDAL _activityRewardDAL = activityRewardDAL;
        private readonly PlayerDrawChanceDAL _playerDrawChanceDAL = playerDrawChanceDAL;
        private readonly DrawRecordDAL _drawRecordDAL = drawRecordDAL;
        private readonly PrizeRecordDAL _prizeRecordDAL = prizeRecordDAL;
        private readonly UserCacheService _userCacheService = userCacheService;
        private readonly Random _random = new();

        /// <summary>
        /// 获取玩家抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <returns>抽奖次数信息</returns>
        public async Task<DrawChancesResponseDto> GetDrawChancesAsync(int activityId, string playerGameUserId)
        {
            var drawChance = await _playerDrawChanceDAL.GetPlayerDrawChanceAsync(activityId, playerGameUserId);

            if (drawChance == null)
            {
                return new DrawChancesResponseDto
                {
                    TotalChances = 0,
                    UsedChances = 0,
                    RemainingChances = 0
                };
            }

            return new DrawChancesResponseDto
            {
                TotalChances = drawChance.TotalChances,
                UsedChances = drawChance.UsedChances,
                RemainingChances = drawChance.RemainingChances
            };
        }

        /// <summary>
        /// 执行抽奖
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="playerNickname">玩家昵称</param>
        /// <returns>抽奖结果</returns>
        public async Task<DrawResultResponseDto> DrawAsync(int activityId, string playerGameUserId, string playerNickname)
        {
            // 验证活动状态
            var activity = await _activityDAL.GetByIdAsync(activityId);
            if (activity == null)
                throw new ArgumentException("活动不存在");

            if (activity.Status != ActivityStatus.running)
                throw new InvalidOperationException("活动未开始或已结束");

            var now = DateTime.Now;
            if (now < activity.StartTime)
                throw new InvalidOperationException("活动未开始");

            if (now > activity.EndTime)
                throw new InvalidOperationException("活动已结束");

            // 确保用户昵称已缓存
            await _userCacheService.EnsureUserNicknameCachedAsync(playerGameUserId, playerNickname);

            // 检查抽奖次数
            var hasChances = await _playerDrawChanceDAL.HasEnoughChancesAsync(activityId, playerGameUserId);
            if (!hasChances)
                throw new InvalidOperationException("抽奖次数不足");

            // 获取可用奖励
            var availableRewards = await _activityRewardDAL.GetAvailableRewardsAsync(activityId);
            if (availableRewards.Count == 0)
                throw new InvalidOperationException("暂无可用奖品");

            // 执行抽奖算法
            var selectedReward = PerformDraw(availableRewards);

            // 使用抽奖次数
            var useSuccess = await _playerDrawChanceDAL.UseDrawChanceAsync(activityId, playerGameUserId);
            if (!useSuccess)
                throw new InvalidOperationException("使用抽奖次数失败");

            // 更新奖励剩余份数
            await _activityRewardDAL.UpdateRemainingQuantityAsync(selectedReward.Id);

            // 记录抽奖结果
            var drawRecord = new ActivityDrawRecord
            {
                ActivityId = activityId,
                PlayerGameUserId = playerGameUserId,
                PlayerNickname = playerNickname,
                RewardId = selectedReward.Id,
                DrawTime = DateTime.Now,
                CreateTime = DateTime.Now
            };

            // 保存抽奖记录
            await _drawRecordDAL.AddAsync(drawRecord);

            // 创建中奖记录
            var prizeRecord = new ActivityPrizeRecord
            {
                ActivityId = activityId,
                PlayerGameUserId = playerGameUserId,
                PlayerNickname = playerNickname,
                RewardId = selectedReward.Id,
                RewardName = selectedReward.RewardName,
                RewardType = selectedReward.RewardType,
                TongbaoAmount = selectedReward.TongbaoAmount,
                PhysicalItem = selectedReward.PhysicalItem,
                Status = PrizeStatus.pending,
                CreateTime = DateTime.Now
            };

            // 保存中奖记录
            await _prizeRecordDAL.AddAsync(prizeRecord);

            // 获取剩余次数
            var remainingChances = await GetDrawChancesAsync(activityId, playerGameUserId);

            return new DrawResultResponseDto
            {
                DrawId = drawRecord.Id,
                Reward = new DrawRewardDto
                {
                    Id = selectedReward.Id,
                    RewardName = selectedReward.RewardName,
                    RewardType = selectedReward.RewardType,
                    TongbaoAmount = selectedReward.TongbaoAmount,
                    PhysicalItem = selectedReward.PhysicalItem
                },
                RemainingChances = remainingChances.RemainingChances
            };
        }

        /// <summary>
        /// 增加玩家抽奖次数
        /// </summary>
        /// <param name="activityId">活动ID</param>
        /// <param name="playerGameUserId">玩家游戏用户ID</param>
        /// <param name="playerNickname">玩家昵称</param>
        /// <param name="chances">增加的次数</param>
        /// <returns>更新后的抽奖次数</returns>
        public async Task<DrawChancesResponseDto> AddDrawChancesAsync(
            int activityId,
            string playerGameUserId,
            string playerNickname,
            int chances)
        {
            // 确保用户昵称已缓存
            await _userCacheService.EnsureUserNicknameCachedAsync(playerGameUserId, playerNickname);

            var drawChance = await _playerDrawChanceDAL.AddDrawChancesAsync(activityId, playerGameUserId, playerNickname, chances);

            return new DrawChancesResponseDto
            {
                TotalChances = drawChance.TotalChances,
                UsedChances = drawChance.UsedChances,
                RemainingChances = drawChance.RemainingChances
            };
        }

        /// <summary>
        /// 执行抽奖算法
        /// </summary>
        /// <param name="rewards">可用奖励列表</param>
        /// <returns>中奖奖励</returns>
        private ActivityReward PerformDraw(List<ActivityReward> rewards)
        {
            // 使用权重进行随机抽奖
            var totalWeight = rewards.Sum(r => r.Weight);
            var randomValue = _random.Next(1, totalWeight + 1);

            var currentWeight = 0;
            foreach (var reward in rewards)
            {
                currentWeight += reward.Weight;
                if (randomValue <= currentWeight)
                {
                    return reward;
                }
            }

            // 理论上不应该到达这里，但为了安全起见返回第一个奖励
            return rewards.First();
        }
    }
}
