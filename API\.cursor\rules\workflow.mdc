---
description: worktlow
globs: 
alwaysApply: false
---
# 开发工作流与维护指南

## 开发流程

### 添加新功能
1. 在 [Entity/Entitys/](mdc:Entity/Entitys) 中添加实体类
2. 在 [Entity/Dto/](mdc:Entity/Dto) 中添加相应的DTO
3. 在 [DAL/](mdc:DAL) 中实现数据访问层代码
4. 在 [BLL/](mdc:BLL) 中实现业务逻辑
5. 在 [DataMgrSystem/Controllers/](mdc:DataMgrSystem/Controllers) 中添加API控制器
6. 更新依赖注入配置

### 添加新实体
1. 创建实体类并继承基础实体类
2. 在DbContext中添加DbSet属性
3. 创建迁移并更新数据库
4. 实现相应的DAL和BLL代码

### 修改数据库结构
1. 修改实体类
2. 使用EF Core Migration创建迁移
3. 应用迁移到数据库

## 配置管理

### 应用程序配置
- 主要配置文件: [DataMgrSystem/Config/appsettings.json](mdc:DataMgrSystem/Config/appsettings.json)
- 环境特定配置: [DataMgrSystem/Config/appsettings.{Environment}.json](mdc:DataMgrSystem/Config)
- 自定义配置访问通过 [Common/Config/](mdc:Common/Config) 实现

### 日志配置
- 日志配置在appsettings.json中
- Log4Net配置可能位于 [Common/Log4Net/](mdc:Common/Log4Net) 目录

## 测试策略
- 添加新功能时编写单元测试
- 控制器测试关注HTTP状态码和返回值
- 服务层测试关注业务逻辑
- 数据访问层测试使用内存数据库或模拟

## 部署流程
1. 发布应用程序（Release配置）
2. 应用数据库迁移
3. 配置服务器环境变量
4. 配置反向代理和HTTPS
5. 设置适当的应用程序池和权限

## 性能优化
- 使用缓存减少数据库访问
- 大数据量使用分页
- 避免N+1查询问题
- 使用异步方法处理I/O操作
- 考虑使用并行处理批量操作

